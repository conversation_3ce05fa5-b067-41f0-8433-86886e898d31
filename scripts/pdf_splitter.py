#!/usr/bin/env python3
"""
PDF Splitter Script

This script splits a PDF file into multiple smaller PDF files based on a specified number of pages per output file.
Usage: python pdf_splitter.py input.pdf [pages_per_file=10] [output_directory=./split_pdfs]
"""

import os
import sys
import argparse
from PyPDF2 import PdfReader, PdfWriter


def split_pdf(input_path, pages_per_file=10, output_dir="./split_pdfs"):
    """
    Split a PDF file into multiple smaller PDF files.
    
    Args:
        input_path (str): Path to the input PDF file
        pages_per_file (int): Number of pages per output file
        output_dir (str): Directory to save the output files
    
    Returns:
        list: List of paths to the output files
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Get the base filename without extension
    base_filename = os.path.splitext(os.path.basename(input_path))[0]
    
    # Open the PDF file
    pdf = PdfReader(input_path)
    total_pages = len(pdf.pages)
    
    output_files = []
    
    # Calculate the number of output files
    num_output_files = (total_pages + pages_per_file - 1) // pages_per_file
    
    for i in range(num_output_files):
        # Create a PDF writer
        pdf_writer = PdfWriter()
        
        # Calculate start and end page for this chunk
        start_page = i * pages_per_file
        end_page = min((i + 1) * pages_per_file, total_pages)
        
        # Add pages to the writer
        for page_num in range(start_page, end_page):
            pdf_writer.add_page(pdf.pages[page_num])
        
        # Create output filename
        output_filename = f"{base_filename}_part_{i+1}.pdf"
        output_path = os.path.join(output_dir, output_filename)
        
        # Write the output file
        with open(output_path, "wb") as output_file:
            pdf_writer.write(output_file)
        
        output_files.append(output_path)
        print(f"Created: {output_path} (Pages {start_page+1}-{end_page})")
    
    print(f"\nSplit complete! Created {len(output_files)} files from {total_pages} pages.")
    return output_files


def main():
    parser = argparse.ArgumentParser(description="Split a PDF file into multiple smaller PDF files.")
    parser.add_argument("input_pdf", help="Path to the input PDF file")
    parser.add_argument("-p", "--pages", type=int, default=10, help="Number of pages per output file (default: 10)")
    parser.add_argument("-o", "--output-dir", default="./split_pdfs", help="Directory to save the output files (default: ./split_pdfs)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_pdf):
        print(f"Error: Input file '{args.input_pdf}' does not exist.")
        sys.exit(1)
    
    split_pdf(args.input_pdf, args.pages, args.output_dir)


if __name__ == "__main__":
    main()