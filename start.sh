#!/bin/bash

# 设置颜色变量
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 检查是否在项目根目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}错误：请在项目根目录下运行此脚本${NC}"
    exit 1
fi

# 启动函数
start_project() {
    echo -e "${GREEN}正在启动会计应用...${NC}"
    
    # 启动Python虚拟环境
    # if [ ! -d "venv" ]; then
    #     echo -e "${YELLOW}警告：未找到虚拟环境，正在创建...${NC}"
    #     python -m venv venv
    # fi
    
    # 激活虚拟环境并获取Python和uvicorn的路径
    # source venv/bin/activate
    PYTHON_PATH=$(which python)
    UVICORN_PATH=$(which uvicorn)
    
    # 安装Python依赖（如果不存在）
    if [ ! -f "backend/requirements.txt" ]; then
        echo -e "${YELLOW}警告：requirements.txt不存在，正在创建...${NC}"
        pip install fastapi uvicorn python-multipart aiohttp Pillow PyPDF2
        pip freeze > backend/requirements.txt
    else
        # 确保所有依赖都已安装
        echo -e "${GREEN}正在安装Python依赖...${NC}"
        pip install -r backend/requirements.txt

        # 检查可选依赖
        echo -e "${GREEN}检查可选依赖...${NC}"

        # 检查LangChain相关依赖
        if ! pip show langchain >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：LangChain未安装，智能体增强功能将不可用${NC}"
        else
            echo -e "${GREEN}✓ LangChain已安装${NC}"
        fi

        # 检查网络搜索依赖
        if ! pip show duckduckgo-search >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：duckduckgo-search未安装，网络搜索功能将使用模拟数据${NC}"
        else
            echo -e "${GREEN}✓ 网络搜索功能可用${NC}"
        fi

        # 检查向量数据库依赖
        if ! pip show chromadb >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：chromadb未安装，向量存储功能将不可用${NC}"
        else
            echo -e "${GREEN}✓ 向量数据库功能可用${NC}"
        fi
    fi
    
    # 启动后端服务前自动初始化数据库
    echo -e "${GREEN}正在初始化数据库表结构...${NC}"
    $PYTHON_PATH backend/init_db.py

    # 启动后端服务
    echo -e "${GREEN}正在启动后端服务...${NC}"
    cd backend
    $PYTHON_PATH $UVICORN_PATH main:app --reload --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # 启动前端服务
    echo -e "${GREEN}正在启动前端服务...${NC}"
    cd frontend
    if [ ! -f "yarn.lock" ]; then
        echo -e "${YELLOW}警告：前端依赖未安装，正在安装...${NC}"
        yarn install
    fi
    yarn dev &
    FRONTEND_PID=$!
    cd ..
    
    # 添加清理函数
    cleanup() {
        echo -e "${YELLOW}正在停止服务...${NC}"
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
        fi
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
        fi
        echo -e "${GREEN}服务已停止${NC}"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup INT TERM
    
    # 等待进程结束
    wait $BACKEND_PID
    wait $FRONTEND_PID
}

# Electron 桌面应用启动函数
start_electron() {
    echo -e "${GREEN}正在启动会计助手桌面应用 (Headless 模式)...${NC}"
    
    # 检查是否安装了 yarn
    if ! command -v yarn &> /dev/null; then
        echo -e "${RED}错误: 未找到 yarn，请先安装 yarn${NC}"
        echo "安装命令: npm install -g yarn"
        exit 1
    fi
    
    # 检查是否安装了 Electron 依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${GREEN}📦 安装 Electron 依赖...${NC}"
        yarn install
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        echo -e "${GREEN}📦 安装前端依赖...${NC}"
        cd frontend && yarn install && cd ..
    fi
    
    # 启动后端服务
    echo -e "${GREEN}🔧 启动后端服务...${NC}"
    
    # 激活虚拟环境并获取Python和uvicorn的路径
    PYTHON_PATH=$(which python)
    UVICORN_PATH=$(which uvicorn)
    
    # 安装Python依赖（如果不存在）
    if [ ! -f "backend/requirements.txt" ]; then
        echo -e "${YELLOW}警告：requirements.txt不存在，正在创建...${NC}"
        pip install fastapi uvicorn python-multipart aiohttp Pillow PyPDF2
        pip freeze > backend/requirements.txt
    else
        echo -e "${GREEN}正在安装Python依赖...${NC}"
        pip install -r backend/requirements.txt
    fi
    
    # 初始化数据库
    echo -e "${GREEN}正在初始化数据库表结构...${NC}"
    $PYTHON_PATH backend/init_db.py
    
    # 启动后端服务
    cd backend
    $PYTHON_PATH $UVICORN_PATH main:app --reload --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端服务启动
    echo -e "${GREEN}等待后端服务启动...${NC}"
    sleep 3
    
    # 启动 Electron 桌面应用 (开发模式直接连接 Vite 服务器)
    echo -e "${GREEN}🖥️  启动桌面应用 (Headless 模式)...${NC}"
    yarn electron:dev &
    ELECTRON_PID=$!
    
    # 添加清理函数
    cleanup_electron() {
        echo -e "${YELLOW}正在停止桌面应用...${NC}"
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
        fi
        if kill -0 $ELECTRON_PID 2>/dev/null; then
            kill $ELECTRON_PID
        fi
        echo -e "${GREEN}桌面应用已停止${NC}"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup_electron INT TERM
    
    # 等待进程结束
    wait $BACKEND_PID
    wait $ELECTRON_PID
}

# 构建 Electron 应用函数
build_electron() {
    echo -e "${GREEN}正在构建会计助手桌面应用...${NC}"
    
    # 检查是否安装了 yarn
    if ! command -v yarn &> /dev/null; then
        echo -e "${RED}错误: 未找到 yarn，请先安装 yarn${NC}"
        echo "安装命令: npm install -g yarn"
        exit 1
    fi
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${GREEN}📦 安装 Electron 依赖...${NC}"
        yarn install
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        echo -e "${GREEN}📦 安装前端依赖...${NC}"
        cd frontend && yarn install && cd ..
    fi
    
    # 构建前端
    echo -e "${GREEN}🔨 构建前端应用...${NC}"
    yarn frontend:build
    
    # 构建 Electron 应用
    echo -e "${GREEN}📦 打包桌面应用...${NC}"
    yarn electron:dist
    
    echo -e "${GREEN}✅ 桌面应用构建完成！${NC}"
    echo -e "${GREEN}安装包位置: ./dist/${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}会计助手启动脚本${NC}"
    echo ""
    echo "用法: $0 {command}"
    echo ""
    echo "命令:"
    echo "  start         启动 Web 版本（前端 + 后端）"
    echo "  electron      启动桌面版本（Electron 应用）"
    echo "  build         构建桌面应用安装包"
    echo "  help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start      # 启动 Web 版本，访问 http://localhost:5173"
    echo "  $0 electron   # 启动桌面版本"
    echo "  $0 build      # 构建桌面应用安装包"
}

# 主程序
case "$1" in
    start)
        start_project
        ;;
    electron)
        start_electron
        ;;
    build)
        build_electron
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        exit 1
esac

exit 0
