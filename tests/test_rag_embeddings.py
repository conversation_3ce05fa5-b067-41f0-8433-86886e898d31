#!/usr/bin/env python3
"""
测试RAG管理器的嵌入模型功能
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.rag.rag_manager import RAGManager
from core.config import config

def test_rag_embeddings():
    """测试RAG嵌入模型功能"""
    print("=" * 50)
    print("测试RAG嵌入模型功能")
    print("=" * 50)
    
    # 检查配置
    print(f"OpenAI已启用: {config.is_openai_enabled()}")
    if config.is_openai_enabled():
        print(f"OpenAI Base URL: {config.OPENAI_BASE_URL}")
    
    # 初始化RAG管理器
    print("\n初始化RAG管理器...")
    rag_manager = RAGManager("test_embeddings")
    
    # 检查嵌入模型
    print(f"\n嵌入模型类型: {type(rag_manager.embedding_model)}")
    print(f"嵌入维度: {rag_manager.embedding_dimension}")
    
    # 测试生成嵌入
    test_text = "这是一个测试文本，用于验证嵌入模型是否正常工作。"
    print(f"\n测试文本: {test_text}")
    
    embedding = rag_manager._generate_embedding(test_text)
    print(f"嵌入向量维度: {len(embedding)}")
    print(f"嵌入向量前5个值: {embedding[:5]}")
    
    # 测试添加文档
    print("\n测试添加文档...")
    test_documents = [
        {
            "content": "财务审核需要遵循相关会计准则和法规。",
            "title": "财务审核规则",
            "category": "财务",
            "source": "内部规章制度"
        },
        {
            "content": "发票审核需要检查发票的真实性、合法性和完整性。",
            "title": "发票审核规则",
            "category": "发票",
            "source": "内部规章制度"
        }
    ]
    
    result = rag_manager.add_documents(test_documents)
    print(f"添加文档结果: {result}")
    
    # 测试搜索文档
    print("\n测试搜索文档...")
    search_query = "财务审核"
    search_result = rag_manager.search_documents(search_query, n_results=2)
    print(f"搜索结果: {search_result}")
    
    # 测试按分类搜索
    print("\n测试按分类搜索...")
    category_result = rag_manager.search_by_category("财务", n_results=1)
    print(f"分类搜索结果: {category_result}")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    test_rag_embeddings()