#!/usr/bin/env python3
"""
测试 MCP 集成功能
"""
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.integration import MCPIntegration
from backend.core.mcp.config import MCPServerConfig, MCPTransportType

def test_mcp_integration():
    """测试 MCP 集成功能"""
    print("开始测试 MCP 集成功能...")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    settings_dir = os.path.join(temp_dir, ".settings")
    os.makedirs(settings_dir, exist_ok=True)
    config_path = os.path.join(settings_dir, "mcp_config.json")
    
    try:
        # 测试 1: 创建集成实例
        print("\n1. 测试创建集成实例...")
        integration = MCPIntegration(config_path)
        print("✓ 集成实例创建成功")
        
        # 测试 2: 添加服务器配置
        print("\n2. 测试添加服务器配置...")
        server1 = MCPServerConfig(
            name="integration-test-stdio",
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "."],
            env={"DEBUG": "true"},
            disabled=False,
            auto_approve=["read_file", "list_directory"],
            timeout=30,
            retry_count=3
        )
        
        server2 = MCPServerConfig(
            name="integration-test-sse",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:3002/api",
            env={"FASTMCP_LOG_LEVEL": "ERROR"},
            disabled=True,
            auto_approve=[],
            timeout=30,
            retry_count=3
        )
        
        # 添加服务器配置
        success1 = integration.add_server(server1)
        success2 = integration.add_server(server2)
        assert success1, "添加第一个服务器配置失败"
        assert success2, "添加第二个服务器配置失败"
        print("✓ 服务器配置添加成功")
        
        # 测试 3: 验证配置文件是否正确保存
        print("\n3. 测试配置文件保存格式...")
        assert os.path.exists(config_path), "配置文件未创建"
        
        # 读取并验证配置文件内容
        with open(config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        # 验证配置文件结构
        assert "mcpServers" in saved_config, "配置文件缺少 mcpServers 字段"
        assert len(saved_config["mcpServers"]) == 2, "配置文件中的服务器数量不正确"
        
        print("✓ 配置文件保存格式正确")
        
        # 测试 4: 重新加载配置
        print("\n4. 测试重新加载配置...")
        # 创建新的集成实例来模拟重新启动
        new_integration = MCPIntegration(config_path)
        loaded_servers = new_integration.load_config()
        
        assert len(loaded_servers) == 2, "加载的服务器数量不正确"
        assert "integration-test-stdio" in loaded_servers, "缺少 integration-test-stdio 服务器配置"
        assert "integration-test-sse" in loaded_servers, "缺少 integration-test-sse 服务器配置"
        
        print("✓ 配置重新加载成功")
        
        # 测试 5: 获取所有服务器配置
        print("\n5. 测试获取所有服务器配置...")
        all_servers = new_integration.get_all_server_configs()
        assert len(all_servers) == 2, "获取的服务器数量不正确"
        assert "integration-test-stdio" in all_servers, "缺少 integration-test-stdio 服务器配置"
        assert "integration-test-sse" in all_servers, "缺少 integration-test-sse 服务器配置"
        
        print("✓ 获取所有服务器配置成功")
        
        # 测试 6: 获取特定服务器配置
        print("\n6. 测试获取特定服务器配置...")
        specific_server = new_integration.get_server_config("integration-test-stdio")
        assert specific_server is not None, "未能获取服务器配置"
        assert specific_server.name == "integration-test-stdio", "服务器名称不正确"
        assert specific_server.transport_type == MCPTransportType.STDIO, "传输类型不正确"
        
        print("✓ 获取特定服务器配置成功")
        
        # 测试 7: 更新服务器配置
        print("\n7. 测试更新服务器配置...")
        updated_server = MCPServerConfig(
            name="integration-test-stdio-updated",
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "/tmp"],
            env={"DEBUG": "false"},
            disabled=True,
            auto_approve=["read_file"],
            timeout=60,
            retry_count=5
        )
        
        success = new_integration.update_server("integration-test-stdio", updated_server)
        assert success, "更新服务器配置失败"
        
        # 验证更新后的配置
        all_servers = new_integration.get_all_server_configs()
        assert len(all_servers) == 2, "更新后服务器数量不正确"
        assert "integration-test-stdio-updated" in all_servers, "更新后的服务器配置不存在"
        assert "integration-test-stdio" not in all_servers, "旧的服务器配置仍存在"
        
        updated_server_config = new_integration.get_server_config("integration-test-stdio-updated")
        assert updated_server_config.args == ["mcp-server-filesystem@latest", "--path", "/tmp"], "参数未更新"
        assert updated_server_config.env == {"DEBUG": "false"}, "环境变量未更新"
        assert updated_server_config.disabled == True, "禁用状态未更新"
        assert updated_server_config.auto_approve == ["read_file"], "自动批准列表未更新"
        assert updated_server_config.timeout == 60, "超时设置未更新"
        assert updated_server_config.retry_count == 5, "重试次数未更新"
        
        print("✓ 更新服务器配置成功")
        
        # 测试 8: 删除服务器配置
        print("\n8. 测试删除服务器配置...")
        success = new_integration.remove_server("integration-test-stdio-updated")
        assert success, "删除服务器配置失败"
        
        # 验证删除后的配置
        all_servers = new_integration.get_all_server_configs()
        assert len(all_servers) == 1, "删除后服务器数量不正确"
        assert "integration-test-sse" in all_servers, "剩余的服务器配置不存在"
        assert "integration-test-stdio-updated" not in all_servers, "已删除的服务器配置仍存在"
        
        print("✓ 删除服务器配置成功")
        
        print("\n所有测试通过！✓")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n临时目录 {temp_dir} 已清理")

if __name__ == "__main__":
    try:
        test_mcp_integration()
        print("\n🎉 所有测试都通过了！MCP 集成功能工作正常。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)