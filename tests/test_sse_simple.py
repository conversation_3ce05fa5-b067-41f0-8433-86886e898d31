#!/usr/bin/env python3
"""
简化的 SSE 连接测试脚本
用于诊断 SSE 协议的基本连接问题
"""
import asyncio
import aiohttp
import json
import logging
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_basic_sse_connection():
    """测试基本的 SSE 连接"""
    base_url = "http://localhost:8080/sse"
    
    print(f"🧪 测试基本 SSE 连接: {base_url}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 测试基本连接
            print("1. 测试基本连接...")
            async with session.get(base_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                print(f"   状态码: {response.status}")
                print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
                
                if response.status == 200:
                    print("   ✅ 基本连接成功")
                else:
                    print(f"   ❌ 基本连接失败: {response.status}")
                    return
        
        except Exception as e:
            print(f"   ❌ 基本连接异常: {e}")
            return
        
        # 2. 测试初始化请求
        print("\n2. 测试初始化请求...")
        init_request = {
            "jsonrpc": "2.0",
            "id": "init_test",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"sampling": {}},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        try:
            async with session.post(
                base_url,
                json=init_request,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                print(f"   初始化状态码: {response.status}")
                
                if response.status == 202:
                    print("   ✅ 初始化请求被接受")
                    
                    # 等待一下让服务器处理
                    await asyncio.sleep(1)
                    
                    # 3. 测试 SSE 流
                    print("\n3. 测试 SSE 流...")
                    query_params = {
                        'method': 'initialize',
                        'id': 'init_test'
                    }
                    query_string = urllib.parse.urlencode(query_params)
                    sse_url = f"{base_url}?{query_string}"
                    
                    print(f"   SSE URL: {sse_url}")
                    
                    try:
                        async with session.get(
                            sse_url,
                            headers={
                                "Accept": "text/event-stream",
                                "Cache-Control": "no-cache"
                            },
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as sse_response:
                            print(f"   SSE 状态码: {sse_response.status}")
                            print(f"   SSE Content-Type: {sse_response.headers.get('content-type', 'N/A')}")
                            
                            if sse_response.status == 200:
                                print("   ✅ SSE 连接成功")
                                
                                # 读取 SSE 流
                                print("\n4. 读取 SSE 流...")
                                line_count = 0
                                start_time = asyncio.get_event_loop().time()
                                
                                async for line in sse_response.content:
                                    line_str = line.decode('utf-8').strip()
                                    if line_str:
                                        print(f"   SSE 行 {line_count}: {line_str}")
                                        line_count += 1
                                    
                                    # 限制读取时间和行数
                                    current_time = asyncio.get_event_loop().time()
                                    if current_time - start_time > 8 or line_count > 20:
                                        print("   达到读取限制，停止读取")
                                        break
                                
                                print(f"   ✅ 成功读取 {line_count} 行 SSE 数据")
                            else:
                                print(f"   ❌ SSE 连接失败: {sse_response.status}")
                    
                    except asyncio.TimeoutError:
                        print("   ❌ SSE 流读取超时")
                    except Exception as e:
                        print(f"   ❌ SSE 流读取异常: {e}")
                
                elif response.status == 200:
                    print("   ✅ 初始化请求成功")
                    result = await response.json()
                    print(f"   初始化结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"   ❌ 初始化请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        
        except asyncio.TimeoutError:
            print("   ❌ 初始化请求超时")
        except Exception as e:
            print(f"   ❌ 初始化请求异常: {e}")

async def test_tool_call():
    """测试工具调用"""
    base_url = "http://localhost:8080/sse"
    
    print(f"\n🔧 测试工具调用: {base_url}")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 发送工具调用请求
        tool_request = {
            "jsonrpc": "2.0",
            "id": "tool_test",
            "method": "tools/call",
            "params": {
                "name": "get_city_tier",
                "arguments": {"city_name": "北京"}
            }
        }
        
        try:
            print("1. 发送工具调用请求...")
            async with session.post(
                base_url,
                json=tool_request,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                print(f"   工具调用状态码: {response.status}")
                
                if response.status == 202:
                    print("   ✅ 工具调用请求被接受")
                    
                    # 等待处理
                    await asyncio.sleep(1)
                    
                    # 检查 SSE 端点获取结果
                    print("\n2. 检查工具调用结果...")
                    query_params = {
                        'method': 'tools/call',
                        'id': 'tool_test'
                    }
                    query_string = urllib.parse.urlencode(query_params)
                    sse_url = f"{base_url}?{query_string}"
                    
                    try:
                        async with session.get(
                            sse_url,
                            headers={
                                "Accept": "text/event-stream",
                                "Cache-Control": "no-cache"
                            },
                            timeout=aiohttp.ClientTimeout(total=15)
                        ) as sse_response:
                            print(f"   结果查询状态码: {sse_response.status}")
                            
                            if sse_response.status == 200:
                                print("   ✅ 结果查询成功")
                                
                                # 读取结果
                                line_count = 0
                                start_time = asyncio.get_event_loop().time()
                                
                                async for line in sse_response.content:
                                    line_str = line.decode('utf-8').strip()
                                    if line_str:
                                        print(f"   结果行 {line_count}: {line_str}")
                                        line_count += 1
                                        
                                        # 尝试解析 JSON 结果
                                        if line_str.startswith('data: '):
                                            data = line_str[6:]
                                            try:
                                                result = json.loads(data)
                                                if isinstance(result, dict) and ('result' in result or 'error' in result):
                                                    print(f"   ✅ 找到工具调用结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                                                    break
                                            except json.JSONDecodeError:
                                                pass
                                    
                                    # 限制读取时间和行数
                                    current_time = asyncio.get_event_loop().time()
                                    if current_time - start_time > 12 or line_count > 30:
                                        print("   达到读取限制，停止读取")
                                        break
                                
                                if line_count == 0:
                                    print("   ❌ 未收到任何结果数据")
                            else:
                                print(f"   ❌ 结果查询失败: {sse_response.status}")
                    
                    except asyncio.TimeoutError:
                        print("   ❌ 结果查询超时")
                    except Exception as e:
                        print(f"   ❌ 结果查询异常: {e}")
                
                else:
                    print(f"   ❌ 工具调用失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        
        except Exception as e:
            print(f"   ❌ 工具调用异常: {e}")

async def main():
    """主函数"""
    print("🚀 开始 SSE 连接诊断")
    print("请确保 MCP 服务器正在运行在 http://localhost:8080/sse")
    print()
    
    await test_basic_sse_connection()
    await test_tool_call()
    
    print("\n🎉 诊断完成")

if __name__ == "__main__":
    asyncio.run(main())
