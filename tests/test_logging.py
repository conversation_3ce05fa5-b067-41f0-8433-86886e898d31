#!/usr/bin/env python3
"""
测试日志记录功能
验证发送给AI服务器的消息内容是否正确记录到日志中
"""

import asyncio
import logging
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_ai_server_logs.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

from backend.core.agent.agent import get_agent_executor

async def test_agent_logging():
    """测试智能体日志记录功能"""
    logger = logging.getLogger(__name__)
    logger.info("开始测试智能体日志记录功能")
    
    try:
        # 创建智能体执行器
        agent = get_agent_executor()
        
        # 配置LLM（使用测试配置）
        test_configured = agent.configure_llm(
            api_key="test_key",
            base_url="https://api.example.com/v1",
            model="test-model"
        )
        
        if not test_configured:
            logger.error("LLM配置失败")
            return
        
        # 测试process_message方法
        logger.info("测试process_message方法")
        try:
            result = await agent.process_message(
                message="测试消息：公司购买了一台电脑，价值5000元",
                user_id="test_user"
            )
            logger.info(f"process_message结果: {result}")
        except Exception as e:
            logger.error(f"process_message测试失败: {str(e)}")
        
        # 测试stream_message方法
        logger.info("测试stream_message方法")
        try:
            async for chunk in agent.stream_message(
                message="测试流式消息：公司支付了办公室租金，金额3000元",
                user_id="test_user"
            ):
                logger.info(f"stream_message chunk: {chunk}")
                break  # 只获取第一个chunk以避免过长输出
        except Exception as e:
            logger.error(f"stream_message测试失败: {str(e)}")
        
        # 测试process_document方法
        logger.info("测试process_document方法")
        try:
            result = await agent.process_document(
                document_type="invoice",
                document_content="测试文档内容：这是一张发票",
                user_id="test_user"
            )
            logger.info(f"process_document结果: {result}")
        except Exception as e:
            logger.error(f"process_document测试失败: {str(e)}")
        
        # 测试stream_document方法
        logger.info("测试stream_document方法")
        try:
            async for chunk in agent.stream_document(
                document_type="contract",
                document_content="测试流式文档内容：这是一份合同",
                user_id="test_user"
            ):
                logger.info(f"stream_document chunk: {chunk}")
                break  # 只获取第一个chunk以避免过长输出
        except Exception as e:
            logger.error(f"stream_document测试失败: {str(e)}")
        
        # 测试stream_audit_message方法
        logger.info("测试stream_audit_message方法")
        try:
            async for chunk in agent.stream_audit_message(
                message="测试审核消息：请审核这张发票",
                user_id="test_user"
            ):
                logger.info(f"stream_audit_message chunk: {chunk}")
                break  # 只获取第一个chunk以避免过长输出
        except Exception as e:
            logger.error(f"stream_audit_message测试失败: {str(e)}")
        
        logger.info("所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")

def check_log_file():
    """检查日志文件中是否包含预期的日志记录"""
    logger = logging.getLogger(__name__)
    logger.info("检查日志文件内容")
    
    try:
        with open('test_ai_server_logs.log', 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 检查是否包含我们添加的日志标记
        expected_log_patterns = [
            "[LLM_REQUEST]",
            "[LLM_STREAM_REQUEST]",
            "[CHAIN_REQUEST]",
            "[STREAM_DOCUMENT_REQUEST]",
            "[STREAM_AUDIT_REQUEST]",
            "[REGULATION_PARSE_REQUEST]",
            "[LLM_RESPONSE]",
            "[LLM_STREAM_RESPONSE]"
        ]
        
        found_patterns = []
        for pattern in expected_log_patterns:
            if pattern in log_content:
                found_patterns.append(pattern)
                logger.info(f"✓ 找到日志模式: {pattern}")
            else:
                logger.warning(f"✗ 未找到日志模式: {pattern}")
        
        # 输出包含AI服务器请求和响应的日志行
        logger.info("包含AI服务器请求和响应的日志行:")
        for line in log_content.split('\n'):
            if any(pattern in line for pattern in expected_log_patterns):
                logger.info(f"  {line}")
        
        logger.info(f"找到 {len(found_patterns)}/{len(expected_log_patterns)} 个预期的日志模式")
        
        if len(found_patterns) == len(expected_log_patterns):
            logger.info("✓ 所有预期的日志模式都已找到")
        else:
            logger.warning("✗ 部分预期的日志模式未找到")
            
    except FileNotFoundError:
        logger.error("日志文件未找到")
    except Exception as e:
        logger.error(f"检查日志文件时发生错误: {str(e)}")

async def main():
    """主函数"""
    logger = logging.getLogger(__name__)
    logger.info("开始测试AI服务器消息日志记录功能")
    
    # 运行测试
    await test_agent_logging()
    
    # 检查日志文件
    check_log_file()
    
    logger.info("测试完成")

if __name__ == "__main__":
    asyncio.run(main())