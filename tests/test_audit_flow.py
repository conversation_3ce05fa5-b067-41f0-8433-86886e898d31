"""
测试单据审核模式的完整流程
"""

import sys
import os
import asyncio
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.agent import get_agent_executor
from core.rag import get_rag_manager

async def test_audit_flow():
    """测试单据审核流程"""
    print("=== 开始测试单据审核流程 ===")
    
    # 1. 初始化组件
    agent = get_agent_executor("test_audit_session")
    rag_manager = get_rag_manager()
    
    # 2. 添加测试用的RAG数据
    print("\n1. 添加测试用的RAG数据...")
    sample_regulations = [
        {
            "title": "报销制度适用范围",
            "content": "本制度适用于公司所有正式员工、实习生及经董事会批准的劳务派遣人员。",
            "category": "适用范围",
            "source": "《员工手册》"
        },
        {
            "title": "差旅费报销标准",
            "content": "员工出差乘坐飞机需选择经济舱，住宿标准为三星级酒店，每日住宿费不超过500元。",
            "category": "差旅费",
            "source": "《差旅费管理办法》"
        },
        {
            "title": "发票要求",
            "content": "所有报销单据必须提供真实、合法、完整的发票，发票内容应与实际业务一致。",
            "category": "发票管理",
            "source": "《财务报销制度》"
        }
    ]
    
    result = rag_manager.add_documents(sample_regulations)
    if result["success"]:
        print(f"   ✓ 成功添加 {len(sample_regulations)} 条规章制度")
    else:
        print(f"   ✗ 添加RAG数据失败: {result.get('error')}")
        return
    
    # 3. 配置智能体（使用测试配置）
    print("\n2. 配置智能体...")
    # 注意：在实际测试中，您需要提供有效的API密钥和模型配置
    # 这里我们只是测试流程，所以使用占位符
    agent.configure_llm(
        api_key="test_key",
        base_url="https://api.test.com",
        model="test_model"
    )
    
    # 4. 测试RAG数据搜索
    print("\n3. 测试RAG数据搜索...")
    search_result = rag_manager.search_documents("差旅费报销", n_results=5)
    if search_result["success"]:
        search_results = search_result["data"]
        print(f"   搜索到 {len(search_results)} 条相关规章制度")
        for i, doc in enumerate(search_results, 1):
            metadata = doc.get("metadata", {})
            print(f"   {i}. {metadata.get('title', '无标题')} ({metadata.get('category', '无分类')})")
    else:
        print(f"   ✗ 搜索RAG数据失败: {search_result.get('error')}")
    
    # 5. 测试单据审核（模拟）
    print("\n4. 测试单据审核...")
    test_document = """
    员工张三出差报销申请：
    - 机票费用：经济舱机票 1200元
    - 住宿费用：四星级酒店 800元（超出标准）
    - 餐费：300元
    - 交通费：150元
    
    请审核是否符合公司报销制度。
    """
    
    # 由于我们没有真实的LLM配置，这里只测试流程
    print("   测试文档内容：")
    print(f"   {test_document}")
    
    # 6. 列出所有RAG数据
    print("\n5. 列出所有RAG数据...")
    list_result = rag_manager.list_documents(limit=100, offset=0)
    if list_result["success"]:
        print(f"   总共 {list_result.get('count', 0)} 条RAG数据")
        for i, doc in enumerate(list_result.get('data', []), 1):
            metadata = doc.get('metadata', {})
            print(f"   {i}. {metadata.get('title', '无标题')} [{metadata.get('category', '无分类')}]")
    else:
        print(f"   ✗ 列出RAG数据失败: {list_result.get('error')}")
    
    # 7. 清理测试数据（删除我们添加的测试数据）
    print("\n6. 清理测试数据...")
    list_result = rag_manager.list_documents(limit=100, offset=0)
    if list_result["success"]:
        for doc in list_result.get('data', []):
            doc_id = doc.get('id')
            if doc_id:
                delete_result = rag_manager.delete_document(doc_id)
                if delete_result["success"]:
                    print(f"   ✓ 删除文档 {doc_id}")
                else:
                    print(f"   ✗ 删除文档 {doc_id} 失败: {delete_result.get('error')}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_audit_flow())