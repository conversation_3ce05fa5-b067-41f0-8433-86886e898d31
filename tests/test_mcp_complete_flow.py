#!/usr/bin/env python3
"""
完整测试 MCP 配置管理流程
包括：添加配置 -> 保存配置 -> 重启应用 -> 加载配置
"""
import sys
import asyncio
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.integration import MCPIntegration
from backend.core.mcp.config import MCPServerConfig, MCPTransportType

async def test_complete_flow():
    """测试完整流程"""
    print("=== MCP 配置管理完整流程测试 ===\n")
    
    # 确保配置目录存在
    settings_dir = Path("backend/.settings")
    settings_dir.mkdir(exist_ok=True)
    
    # 配置文件路径
    config_path = settings_dir / "mcp_config.json"
    
    # 如果配置文件已存在，先备份
    if config_path.exists():
        backup_path = config_path.with_suffix(".json.backup")
        config_path.rename(backup_path)
        print(f"已备份现有配置文件到: {backup_path}")
    
    try:
        print("步骤 1: 创建新的 MCP 集成实例")
        integration1 = MCPIntegration()
        
        print("步骤 2: 添加服务器配置")
        # 添加文件系统服务器
        fs_server = MCPServerConfig(
            name="filesystem-server",
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "/tmp"],
            env={"DEBUG": "true"},
            disabled=False,
            auto_approve=["read_file", "list_directory", "write_file"],
            timeout=30,
            retry_count=3
        )
        
        # 添加 SSE 服务器
        sse_server = MCPServerConfig(
            name="city-server",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8000/sse",
            env={"FASTMCP_LOG_LEVEL": "INFO"},
            disabled=False,
            auto_approve=[],
            timeout=30,
            retry_count=3
        )
        
        # 添加服务器
        success1 = integration1.add_server(fs_server)
        success2 = integration1.add_server(sse_server)
        
        if success1 and success2:
            print("✓ 服务器配置添加成功")
        else:
            print("❌ 服务器配置添加失败")
            return False
        
        print("\n步骤 3: 验证配置已保存到文件")
        if config_path.exists():
            print("✓ 配置文件已创建")
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"配置文件大小: {len(content)} 字符")
        else:
            print("❌ 配置文件未创建")
            return False
        
        print("\n步骤 4: 模拟应用重启，创建新的集成实例")
        integration2 = MCPIntegration()
        
        print("步骤 5: 从文件加载配置")
        loaded_servers = integration2.load_config()
        print(f"✓ 从文件加载了 {len(loaded_servers)} 个服务器配置")
        
        print("\n步骤 6: 验证加载的配置内容")
        all_servers = integration2.get_all_server_configs()
        if len(all_servers) == 2:
            print("✓ 内存中配置数量正确")
        else:
            print(f"❌ 内存中配置数量不正确: 期望 2，实际 {len(all_servers)}")
            return False
        
        # 验证具体配置
        fs_loaded = integration2.get_server_config("filesystem-server")
        if fs_loaded:
            print("✓ filesystem-server 配置加载成功")
            print(f"  传输类型: {fs_loaded.transport_type.value}")
            print(f"  命令: {fs_loaded.command}")
            print(f"  参数: {fs_loaded.args}")
            print(f"  自动批准: {fs_loaded.auto_approve}")
        else:
            print("❌ filesystem-server 配置加载失败")
            return False
        
        sse_loaded = integration2.get_server_config("city-server")
        if sse_loaded:
            print("✓ city-server 配置加载成功")
            print(f"  传输类型: {sse_loaded.transport_type.value}")
            print(f"  URL: {sse_loaded.url}")
            print(f"  自动批准: {sse_loaded.auto_approve}")
        else:
            print("❌ city-server 配置加载失败")
            return False
        
        print("\n步骤 7: 测试配置更新")
        # 更新 filesystem 服务器配置
        updated_fs_server = MCPServerConfig(
            name="filesystem-server",  # 保持相同名称
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "/home/<USER>/documents"],
            env={"DEBUG": "false", "LOG_LEVEL": "info"},
            disabled=False,
            auto_approve=["read_file", "list_directory"],  # 减少自动批准权限
            timeout=60,  # 增加超时时间
            retry_count=5   # 增加重试次数
        )
        
        update_success = integration2.update_server("filesystem-server", updated_fs_server)
        if update_success:
            print("✓ 服务器配置更新成功")
            
            # 验证更新后的配置
            updated_config = integration2.get_server_config("filesystem-server")
            if updated_config:
                print(f"  新路径: {updated_config.args[-1]}")
                print(f"  新超时时间: {updated_config.timeout}")
                print(f"  新重试次数: {updated_config.retry_count}")
                print(f"  新自动批准: {updated_config.auto_approve}")
        else:
            print("❌ 服务器配置更新失败")
            return False
        
        print("\n步骤 8: 测试配置删除")
        remove_success = integration2.remove_server("city-server")
        if remove_success:
            print("✓ city-server 配置删除成功")
            
            # 验证删除后的配置数量
            remaining_servers = integration2.get_all_server_configs()
            if len(remaining_servers) == 1:
                print("✓ 配置数量正确更新")
            else:
                print(f"❌ 配置数量不正确: 期望 1，实际 {len(remaining_servers)}")
                return False
        else:
            print("❌ city-server 配置删除失败")
            return False
        
        print("\n🎉 所有测试通过！MCP 配置管理功能正常工作。")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 恢复备份的配置文件（如果存在）
        backup_path = config_path.with_suffix(".json.backup")
        if backup_path.exists():
            backup_path.rename(config_path)
            print(f"\n已恢复备份的配置文件")

if __name__ == "__main__":
    success = asyncio.run(test_complete_flow())
    sys.exit(0 if success else 1)