#!/usr/bin/env python3
"""
添加测试 MCP 配置
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.integration import MCPIntegration
from backend.core.mcp.config import MCPServerConfig, MCPTransportType

def add_test_config():
    """添加测试配置"""
    print("开始添加测试 MCP 配置...")
    
    try:
        # 创建集成实例
        integration = MCPIntegration()
        
        # 添加测试服务器配置
        server1 = MCPServerConfig(
            name="test-filesystem",
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "."],
            env={"DEBUG": "true"},
            disabled=False,
            auto_approve=["read_file", "list_directory"],
            timeout=30,
            retry_count=3
        )
        
        server2 = MCPServerConfig(
            name="test-city",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8000/sse",
            env={"FASTMCP_LOG_LEVEL": "ERROR"},
            disabled=False,
            auto_approve=[],
            timeout=30,
            retry_count=3
        )
        
        # 添加服务器配置
        print("添加 test-filesystem 服务器...")
        success1 = integration.add_server(server1)
        if success1:
            print("✓ test-filesystem 服务器添加成功")
        else:
            print("❌ test-filesystem 服务器添加失败")
        
        print("添加 test-city 服务器...")
        success2 = integration.add_server(server2)
        if success2:
            print("✓ test-city 服务器添加成功")
        else:
            print("❌ test-city 服务器添加失败")
        
        if success1 and success2:
            print("\n🎉 所有测试配置添加成功！")
            print("配置文件已保存到 backend/.settings/mcp_config.json")
        else:
            print("\n❌ 部分配置添加失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"\n❌ 添加测试配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_test_config()
    sys.exit(0 if success else 1)