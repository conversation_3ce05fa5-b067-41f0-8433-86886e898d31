#!/usr/bin/env python3
"""
测试 MCP 配置管理功能
"""
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.config import (
    MCPConfigManager, 
    MCPServerConfig, 
    MCPTransportType
)

def test_config_manager():
    """测试配置管理器的基本功能"""
    print("开始测试 MCP 配置管理器...")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    settings_dir = os.path.join(temp_dir, ".settings")
    os.makedirs(settings_dir, exist_ok=True)
    config_path = os.path.join(settings_dir, "test_mcp_config.json")
    
    try:
        # 测试 1: 创建配置管理器
        print("\n1. 测试创建配置管理器...")
        config_manager = MCPConfigManager(config_path)
        print(f"✓ 配置管理器创建成功，配置文件路径: {config_manager.config_path}")
        
        # 测试 2: 创建默认配置
        print("\n2. 测试创建默认配置...")
        success = config_manager.create_default_config()
        assert success, "创建默认配置失败"
        print("✓ 默认配置创建成功")
        
        # 检查配置文件是否创建
        assert os.path.exists(config_path), "配置文件未创建"
        print("✓ 配置文件已创建")
        
        # 测试 3: 加载配置
        print("\n3. 测试加载配置...")
        servers = config_manager.load_config()
        assert len(servers) == 2, f"期望加载 2 个服务器配置，实际加载了 {len(servers)} 个"
        print(f"✓ 成功加载 {len(servers)} 个服务器配置")
        
        # 检查服务器配置
        assert "example_stdio" in servers, "缺少 example_stdio 服务器配置"
        assert "example_sse" in servers, "缺少 example_sse 服务器配置"
        print("✓ 服务器配置内容正确")
        
        # 测试 4: 添加新服务器
        print("\n4. 测试添加新服务器...")
        new_server = MCPServerConfig(
            name="test_server",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8080/sse",
            disabled=False
        )
        success = config_manager.add_server(new_server)
        assert success, "添加服务器配置失败"
        print("✓ 新服务器配置添加成功")
        
        # 验证服务器是否添加成功
        servers = config_manager.load_config()
        assert len(servers) == 3, f"期望有 3 个服务器配置，实际有 {len(servers)} 个"
        assert "test_server" in servers, "新服务器配置未找到"
        print("✓ 新服务器配置验证成功")
        
        # 测试 5: 更新服务器配置
        print("\n5. 测试更新服务器配置...")
        updated_server = MCPServerConfig(
            name="test_server_updated",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:8080/sse",
            disabled=False,
            timeout=60
        )
        success = config_manager.update_server("test_server", updated_server)
        assert success, "更新服务器配置失败"
        print("✓ 服务器配置更新成功")
        
        # 验证更新是否成功
        servers = config_manager.load_config()
        assert "test_server_updated" in servers, "更新后的服务器配置未找到"
        assert "test_server" not in servers, "旧的服务器配置仍存在"
        assert servers["test_server_updated"].timeout == 60, "超时设置未更新"
        print("✓ 服务器配置更新验证成功")
        
        # 测试 6: 删除服务器配置
        print("\n6. 测试删除服务器配置...")
        success = config_manager.remove_server("test_server_updated")
        assert success, "删除服务器配置失败"
        print("✓ 服务器配置删除成功")
        
        # 验证删除是否成功
        servers = config_manager.load_config()
        assert len(servers) == 2, f"期望有 2 个服务器配置，实际有 {len(servers)} 个"
        assert "test_server_updated" not in servers, "删除的服务器配置仍存在"
        print("✓ 服务器配置删除验证成功")
        
        # 测试 7: 验证配置
        print("\n7. 测试配置验证...")
        errors = config_manager.validate_config()
        assert len(errors) == 0, f"配置验证失败: {errors}"
        print("✓ 配置验证通过")
        
        # 测试 8: 创建无效配置并验证
        print("\n8. 测试无效配置验证...")
        # 创建一个有轻微问题的配置来测试验证功能
        # 首先创建一个有效的服务器配置
        valid_server = MCPServerConfig(
            name="invalid_server",
            transport_type=MCPTransportType.STDIO,
            command="python",  # 有效命令
            timeout=-1  # 无效超时值
        )
        # 直接添加到内存中，不通过配置文件
        config_manager.servers["invalid_server"] = valid_server
        
        # 验证配置
        errors = config_manager.validate_config()
        assert len(errors) > 0, "应该检测到配置错误"
        print(f"✓ 成功检测到 {len(errors)} 个配置错误")
        
        # 打印错误信息
        for error in errors:
            print(f"  - {error}")
        
        # 测试 9: 测试字符串清理功能
        print("\n9. 测试字符串清理功能...")
        # 创建带有前后空格的配置
        server_with_spaces = MCPServerConfig(
            name="  test_server_spaces  ",
            transport_type=MCPTransportType.SSE,
            url="  http://localhost:8000/sse  ",
            env={"  KEY1  ": "  VALUE1  ", "KEY2": "  VALUE2  "},
            auto_approve=["  tool1  ", "tool2  "],
            args=["  arg1  ", "arg2  "]
        )
        
        # 验证字符串被正确清理
        assert server_with_spaces.name == "test_server_spaces", f"服务器名称未正确清理: '{server_with_spaces.name}'"
        assert server_with_spaces.url == "http://localhost:8000/sse", f"URL未正确清理: '{server_with_spaces.url}'"
        assert server_with_spaces.env == {"KEY1": "VALUE1", "KEY2": "VALUE2"}, f"环境变量未正确清理: {server_with_spaces.env}"
        assert server_with_spaces.auto_approve == ["tool1", "tool2"], f"自动批准列表未正确清理: {server_with_spaces.auto_approve}"
        assert server_with_spaces.args == ["arg1", "arg2"], f"参数列表未正确清理: {server_with_spaces.args}"
        
        print("✓ 字符串清理功能正常工作")
        
        # 测试 10: 测试配置文件自动创建功能
        print("\n10. 测试配置文件自动创建功能...")
        # 创建一个新的配置管理器，指向不存在的配置文件
        new_config_path = os.path.join(temp_dir, ".settings", "auto_create_config.json")
        new_config_manager = MCPConfigManager(new_config_path)
        
        # 确保配置文件不存在
        assert not os.path.exists(new_config_path), "配置文件不应该存在"
        
        # 尝试加载配置，应该自动创建配置文件
        servers = new_config_manager.load_config()
        assert len(servers) == 0, "新配置文件应该没有服务器配置"
        
        # 验证配置文件是否被创建
        assert os.path.exists(new_config_path), "配置文件应该被自动创建"
        
        # 验证配置文件内容
        import json
        with open(new_config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        assert "version" in config_data, "配置文件应该包含版本信息"
        assert "servers" in config_data, "配置文件应该包含服务器配置"
        assert isinstance(config_data["servers"], dict), "服务器配置应该是字典"
        assert len(config_data["servers"]) == 0, "新配置文件的服务器配置应该为空"
        
        print("✓ 配置文件自动创建功能正常工作")
        
        print("\n所有测试通过！✓")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n临时目录 {temp_dir} 已清理")

def test_integration():
    """测试集成功能"""
    print("\n开始测试 MCP 集成功能...")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    settings_dir = os.path.join(temp_dir, ".settings")
    os.makedirs(settings_dir, exist_ok=True)
    config_path = os.path.join(settings_dir, "test_mcp_config.json")
    
    try:
        from backend.core.mcp.integration import MCPIntegration
        
        # 测试创建集成实例
        print("\n1. 测试创建集成实例...")
        integration = MCPIntegration(config_path)
        print("✓ 集成实例创建成功")
        
        # 测试创建默认配置
        print("\n2. 测试创建默认配置...")
        success = integration.create_default_config()
        assert success, "创建默认配置失败"
        print("✓ 默认配置创建成功")
        
        # 测试加载配置
        print("\n3. 测试加载配置...")
        servers = integration.load_config()
        assert len(servers) == 2, f"期望加载 2 个服务器配置，实际加载了 {len(servers)} 个"
        print("✓ 配置加载成功")
        
        # 测试添加服务器
        print("\n4. 测试添加服务器...")
        new_server = MCPServerConfig(
            name="integration_test",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:9000/sse"
        )
        success = integration.add_server(new_server)
        assert success, "添加服务器失败"
        print("✓ 服务器添加成功")
        
        # 验证服务器是否添加成功
        servers = integration.get_all_server_configs()
        assert len(servers) == 3, f"期望有 3 个服务器配置，实际有 {len(servers)} 个"
        print("✓ 服务器添加验证成功")
        
        # 测试获取特定服务器配置
        print("\n5. 测试获取特定服务器配置...")
        server_config = integration.get_server_config("integration_test")
        assert server_config is not None, "未能获取服务器配置"
        assert server_config.name == "integration_test", "服务器配置名称不匹配"
        print("✓ 服务器配置获取成功")
        
        print("\n集成功能测试通过！✓")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n临时目录 {temp_dir} 已清理")

if __name__ == "__main__":
    try:
        test_config_manager()
        test_integration()
        print("\n🎉 所有测试都通过了！MCP 配置管理功能工作正常。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)