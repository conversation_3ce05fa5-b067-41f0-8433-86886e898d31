#!/usr/bin/env python3
"""
测试单据审核修复的脚本
"""

import asyncio
import json
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.agent.agent import AgentExecutor

async def test_audit_response_parsing():
    """测试审核响应解析功能"""
    print("=== 测试审核响应解析功能 ===")
    
    # 创建智能体执行器
    agent = AgentExecutor()
    
    # 测试用例1：需要更多信息的响应
    test_response_1 = """
    请提供这些信息以便我能够完成单据的审核。
    ",
    "audit_conclusion": "需要更多信息"
    }
    """
    
    print("测试用例1：需要更多信息的响应")
    result1 = await agent._parse_audit_response(test_response_1)
    print(f"解析结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试用例2：审核通过的响应
    test_response_2 = """
    单据审核通过，所有信息完整且符合要求。
    审核结论：审核通过
    """
    
    print("\n测试用例2：审核通过的响应")
    result2 = await agent._parse_audit_response(test_response_2)
    print(f"解析结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试用例3：包含JSON格式的响应
    test_response_3 = """
    根据审核结果，需要补充以下信息：
    {
        "action": "request_more_info",
        "needs_more_info": true,
        "required_info": ["发票号码", "购买日期", "供应商信息"],
        "audit_conclusion": "需要更多信息"
    }
    """
    
    print("\n测试用例3：包含JSON格式的响应")
    result3 = await agent._parse_audit_response(test_response_3)
    print(f"解析结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_audit_response_parsing())