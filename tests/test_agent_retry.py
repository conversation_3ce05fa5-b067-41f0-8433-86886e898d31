import asyncio
import json
from backend.core.agent.agent import Agent<PERSON><PERSON><PERSON><PERSON>
from backend.core.chain.chain_manager import <PERSON><PERSON><PERSON><PERSON>
from backend.core.llm.llm_factory import LLMFactory
from backend.core.prompt.prompt_manager import Prompt<PERSON>anager

async def test_agent_retry():
    # Initialize components
    llm_factory = LLMFactory()
    prompt_manager = PromptManager("backend/core/agent/prompt_templates")
    chain_manager = ChainManager(llm_factory, prompt_manager)
    
    # Create agent executor
    agent = AgentExecutor(chain_manager)
    
    # Test message that might cause an imbalance
    test_message = "支付办公室租金5000元，银行手续费50元"
    
    print("Testing agent with retry mechanism...")
    print(f"Input message: {test_message}")
    
    # Test process_message
    print("\n=== Testing process_message ===")
    result = await agent.process_message(test_message)
    print(f"Result: {result}")
    
    # Test stream_message
    print("\n=== Testing stream_message ===")
    async for chunk in agent.stream_message(test_message):
        print(f"Stream chunk: {chunk}")

if __name__ == "__main__":
    asyncio.run(test_agent_retry())