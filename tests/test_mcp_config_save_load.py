#!/usr/bin/env python3
"""
测试 MCP 配置保存和加载功能
"""
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.config import (
    MCPConfigManager, 
    MCPServerConfig, 
    MCPTransportType
)

def test_config_save_load():
    """测试配置保存和加载功能"""
    print("开始测试 MCP 配置保存和加载功能...")
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    settings_dir = os.path.join(temp_dir, ".settings")
    os.makedirs(settings_dir, exist_ok=True)
    config_path = os.path.join(settings_dir, "mcp_config.json")
    
    try:
        # 测试 1: 创建配置管理器
        print("\n1. 测试创建配置管理器...")
        config_manager = MCPConfigManager(config_path)
        print(f"✓ 配置管理器创建成功，配置文件路径: {config_manager.config_path}")
        
        # 测试 2: 添加服务器配置
        print("\n2. 测试添加服务器配置...")
        server1 = MCPServerConfig(
            name="test-stdio",
            transport_type=MCPTransportType.STDIO,
            command="uvx",
            args=["mcp-server-filesystem@latest", "--path", "."],
            env={"DEBUG": "true"},
            disabled=False,
            auto_approve=["read_file", "list_directory"],
            timeout=30,
            retry_count=3
        )
        
        server2 = MCPServerConfig(
            name="test-sse",
            transport_type=MCPTransportType.SSE,
            url="http://localhost:3002/api",
            env={"FASTMCP_LOG_LEVEL": "ERROR"},
            disabled=True,
            auto_approve=[],
            timeout=30,
            retry_count=3
        )
        
        # 添加服务器配置
        success1 = config_manager.add_server(server1)
        success2 = config_manager.add_server(server2)
        assert success1, "添加第一个服务器配置失败"
        assert success2, "添加第二个服务器配置失败"
        print("✓ 服务器配置添加成功")
        
        # 测试 3: 验证配置文件是否正确保存
        print("\n3. 测试配置文件保存格式...")
        assert os.path.exists(config_path), "配置文件未创建"
        
        # 读取并验证配置文件内容
        with open(config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        # 验证配置文件结构
        assert "mcpServers" in saved_config, "配置文件缺少 mcpServers 字段"
        assert len(saved_config["mcpServers"]) == 2, "配置文件中的服务器数量不正确"
        
        # 验证第一个服务器配置
        assert "test-stdio" in saved_config["mcpServers"], "缺少 test-stdio 服务器配置"
        server1_data = saved_config["mcpServers"]["test-stdio"]
        assert server1_data["transport"] == "stdio", "传输类型不正确"
        assert server1_data["command"] == "uvx", "命令不正确"
        assert server1_data["args"] == ["mcp-server-filesystem@latest", "--path", "."], "参数不正确"
        assert server1_data["env"] == {"DEBUG": "true"}, "环境变量不正确"
        assert server1_data["disabled"] == False, "禁用状态不正确"
        assert server1_data["autoApprove"] == ["read_file", "list_directory"], "自动批准列表不正确"
        assert server1_data["timeout"] == 30, "超时设置不正确"
        assert server1_data["retryCount"] == 3, "重试次数不正确"
        
        # 验证第二个服务器配置
        assert "test-sse" in saved_config["mcpServers"], "缺少 test-sse 服务器配置"
        server2_data = saved_config["mcpServers"]["test-sse"]
        assert server2_data["transport"] == "sse", "传输类型不正确"
        assert server2_data["url"] == "http://localhost:3002/api", "URL不正确"
        assert server2_data["env"] == {"FASTMCP_LOG_LEVEL": "ERROR"}, "环境变量不正确"
        assert server2_data["disabled"] == True, "禁用状态不正确"
        assert server2_data["autoApprove"] == [], "自动批准列表不正确"
        assert server2_data["timeout"] == 30, "超时设置不正确"
        assert server2_data["retryCount"] == 3, "重试次数不正确"
        
        print("✓ 配置文件保存格式正确")
        
        # 测试 4: 重新加载配置
        print("\n4. 测试重新加载配置...")
        # 创建新的配置管理器实例来模拟重新启动
        new_config_manager = MCPConfigManager(config_path)
        loaded_servers = new_config_manager.load_config()
        
        assert len(loaded_servers) == 2, "加载的服务器数量不正确"
        assert "test-stdio" in loaded_servers, "缺少 test-stdio 服务器配置"
        assert "test-sse" in loaded_servers, "缺少 test-sse 服务器配置"
        
        # 验证加载的服务器配置
        loaded_server1 = loaded_servers["test-stdio"]
        assert loaded_server1.transport_type == MCPTransportType.STDIO, "传输类型不正确"
        assert loaded_server1.command == "uvx", "命令不正确"
        assert loaded_server1.args == ["mcp-server-filesystem@latest", "--path", "."], "参数不正确"
        assert loaded_server1.env == {"DEBUG": "true"}, "环境变量不正确"
        assert loaded_server1.disabled == False, "禁用状态不正确"
        assert loaded_server1.auto_approve == ["read_file", "list_directory"], "自动批准列表不正确"
        assert loaded_server1.timeout == 30, "超时设置不正确"
        assert loaded_server1.retry_count == 3, "重试次数不正确"
        
        loaded_server2 = loaded_servers["test-sse"]
        assert loaded_server2.transport_type == MCPTransportType.SSE, "传输类型不正确"
        assert loaded_server2.url == "http://localhost:3002/api", "URL不正确"
        assert loaded_server2.env == {"FASTMCP_LOG_LEVEL": "ERROR"}, "环境变量不正确"
        assert loaded_server2.disabled == True, "禁用状态不正确"
        assert loaded_server2.auto_approve == [], "自动批准列表不正确"
        assert loaded_server2.timeout == 30, "超时设置不正确"
        assert loaded_server2.retry_count == 3, "重试次数不正确"
        
        print("✓ 配置重新加载成功")
        
        # 测试 5: 测试配置文件自动创建
        print("\n5. 测试配置文件自动创建...")
        # 创建指向不存在配置文件的配置管理器
        new_config_path = os.path.join(settings_dir, "new_config.json")
        new_manager = MCPConfigManager(new_config_path)
        
        # 确保配置文件不存在
        assert not os.path.exists(new_config_path), "新配置文件不应该存在"
        
        # 加载配置（应该自动创建空配置文件）
        loaded_config = new_manager.load_config()
        assert len(loaded_config) == 0, "新配置文件应该没有服务器配置"
        
        # 验证配置文件是否被创建
        assert os.path.exists(new_config_path), "配置文件应该被自动创建"
        
        # 验证配置文件内容
        with open(new_config_path, 'r', encoding='utf-8') as f:
            new_config_data = json.load(f)
        
        assert "mcpServers" in new_config_data, "新配置文件应该包含 mcpServers 字段"
        assert isinstance(new_config_data["mcpServers"], dict), "mcpServers 应该是字典"
        assert len(new_config_data["mcpServers"]) == 0, "新配置文件的服务器配置应该为空"
        
        print("✓ 配置文件自动创建功能正常")
        
        print("\n所有测试通过！✓")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n临时目录 {temp_dir} 已清理")

if __name__ == "__main__":
    try:
        test_config_save_load()
        print("\n🎉 所有测试都通过了！MCP 配置保存和加载功能工作正常。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)