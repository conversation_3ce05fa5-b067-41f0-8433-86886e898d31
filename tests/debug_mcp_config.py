#!/usr/bin/env python3
"""
调试 MCP 配置加载过程
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.config import MCPConfigManager, MCPServerConfig, MCPTransportType

def debug_config():
    """调试配置加载"""
    print("开始调试 MCP 配置加载过程...")
    
    try:
        # 检查配置文件是否存在
        backend_dir = Path("backend")
        settings_dir = backend_dir / ".settings"
        config_path = settings_dir / "mcp_config.json"
        
        print(f"配置文件路径: {config_path.absolute()}")
        print(f"配置文件是否存在: {config_path.exists()}")
        
        if config_path.exists():
            print("配置文件内容:")
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content)
        
        # 创建配置管理器
        config_manager = MCPConfigManager(str(config_path))
        
        # 加载配置
        print("\n尝试加载配置...")
        servers = config_manager.load_config()
        print(f"加载的服务器数量: {len(servers)}")
        
        # 检查内存中的服务器
        print(f"内存中的服务器数量: {len(config_manager.servers)}")
        
        # 获取所有服务器
        all_servers = config_manager.get_all_servers()
        print(f"get_all_servers 返回的服务器数量: {len(all_servers)}")
        
        for name, server in all_servers.items():
            print(f"  - {name}: {server.transport_type.value}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_config()
    sys.exit(0 if success else 1)