// Test script to verify the audit response handling fix
// This simulates the frontend logic for handling audit responses

// Mock the cleanJsonFromText function (simplified version)
function cleanJsonFromText(content) {
  if (!content || typeof content !== 'string') return content || '';
  
  let cleaned = content;
  
  // Remove JSON code blocks
  cleaned = cleaned.replace(/```json[:\n\r]*[\s\S]*?```/gi, '');
  
  // Remove JSON objects
  cleaned = cleaned.replace(/\{[\s\S]*?\}/g, '');
  
  // Remove JSON key-value pairs
  cleaned = cleaned.replace(/"[^"]*"\s*:\s*"[^"]*"/g, '');
  
  // Remove extra characters
  cleaned = cleaned.replace(/[,{}[\]]/g, '');
  
  // Remove extra whitespace
  cleaned = cleaned.replace(/\n\s*\n/g, '\n');
  cleaned = cleaned.replace(/^\s+|\s+$/g, '');
  
  // Remove "json:" prefix
  cleaned = cleaned.replace(/^json[:\s]*/i, '');
  
  return cleaned.trim();
}

// Mock the handleAuditAgentAction logic (simplified version)
function handleAuditAgentAction(response) {
  console.log('[DEBUG] handleAuditAgentAction called', response);
  const { action, answer, data, is_finished, needs_more_info, required_info, audit_conclusion } = response;
  
  // Try to parse JSON and extract answer_content
  let userFriendlyMessage = '';
  let cleanAnswer = answer ? cleanJsonFromText(answer) : '';
  
  // Try to parse JSON format answer to extract answer_content
  if (answer) {
    try {
      // Extract JSON part
      const jsonMatch = answer.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonData = JSON.parse(jsonMatch[0]);
        // If there's an answer_content field, use it as user-friendly message
        if (jsonData.answer_content) {
          userFriendlyMessage = jsonData.answer_content;
        }
      }
    } catch (e) {
      // If parsing fails, use cleaned answer
      userFriendlyMessage = cleanAnswer;
    }
  }
  
  // If no answer_content extracted, use cleaned answer
  if (!userFriendlyMessage) {
    userFriendlyMessage = cleanAnswer;
  }
  
  let displayMessage = '';
  
  switch (action) {
    case 'audit_complete':
      displayMessage = userFriendlyMessage;
      break;
    case 'request_more_info':
      if (needs_more_info) {
        displayMessage = "";
        
        // Prioritize answer_content as main information
        if (userFriendlyMessage) {
          displayMessage = userFriendlyMessage + "\n\n";
        } else {
          displayMessage = "为了完成单据审核，请提供以下信息：\n\n";
        }
        
        // Add specific required information list
        if (required_info && Array.isArray(required_info) && required_info.length > 0) {
          displayMessage += "📋 需要补充的信息：\n";
          required_info.forEach((info, index) => {
            displayMessage += `${index + 1}. ${info}\n`;
          });
        } else {
          displayMessage += "📋 需要补充的信息：\n";
          displayMessage += "1. 请提供更完整的单据信息\n";
        }
        
        displayMessage += "\n💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。";
      } else if (userFriendlyMessage) {
        if (audit_conclusion === '需要更多信息' || audit_conclusion?.includes('需要')) {
          displayMessage = userFriendlyMessage + "\n\n";
          displayMessage += "💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。";
        } else {
          displayMessage = userFriendlyMessage;
        }
      }
      break;
    case 'audit_rejected':
      displayMessage = userFriendlyMessage;
      break;
    case 'none':
      displayMessage = userFriendlyMessage;
      break;
    default:
      displayMessage = userFriendlyMessage || '暂不支持该操作';
  }
  
  return displayMessage;
}

// Test cases
console.log('=== Test Case 1: Original problematic response ===');
const originalResponse = {
  action: 'request_more_info',
  answer: '```json\n{\n    "analysis_process": "首先，我仔细分析了上传的单据内容，这是一张云南增值税电普通发票，包含了购方和销方的详细信息、发票代码、号码、开票日期、校验码、货物或应税劳务、服务名称、金额、税额等关键信息。接着，我对照了提供的业务招待费报销规定和公务交通费报销规定，以及差旅费报销规定，对单据进行了逐项审核。审核过程中，我注意到单据内容完整，各项信息填写准确，符合增值税普通发票的基本要求。然而，单据内容并未直接涉及业务招待费、公务交通费或差旅费的具体报销情况，因此无法直接判断其是否符合这些特定报销规定的具体要求。但单据本身作为费用发生的凭证，其完整性和准确性是符合要求的。",\n    "audit_result": "none",\n    "answer_content": "经过审核，该单据作为费用发生的凭证，其完整性和准确性符合增值税普通发票的基本要求。然而，单据内容并未直接涉及业务招待费、公务交通费或差旅费的具体报销情况，因此无法直接判断其是否符合这些特定报销规定的具体要求。若需进一步判断该单据是否符合业务招待费、公务交通费或差旅费报销规定，请提供相关报销申请单、审批单等文件，以便进行详细审核。",\n    "audit_conclusion": "request_more_info"\n}\n```',
  needs_more_info: true,
  required_info: [
    "完整，各项信息填写准确，符合增值税普通发票的基本要求",
    "相关报销申请单、审批单等文件，以便进行详细审核",
    "，这是一张云南增值税电普通发票，包含了购方和销方的详细信息、发票代码、号码、开票日期、校验码、货物或应税劳务、服务名称、金额、税额等关键信息",
    "并未直接涉及业务招待费、公务交通费或差旅费的具体报销情况，因此无法直接判断其是否符合这些特定报销规定的具体要求"
  ],
  audit_conclusion: "需要更多信息",
  is_finished: true
};

const result1 = handleAuditAgentAction(originalResponse);
console.log('Result:');
console.log(result1);
console.log('\n' + '='.repeat(50) + '\n');

console.log('=== Test Case 2: Simple response without JSON ===');
const simpleResponse = {
  action: 'request_more_info',
  answer: '请提供更多的单据信息以便完成审核。',
  needs_more_info: true,
  required_info: ['请提供更多的单据信息'],
  audit_conclusion: "需要更多信息",
  is_finished: true
};

const result2 = handleAuditAgentAction(simpleResponse);
console.log('Result:');
console.log(result2);
console.log('\n' + '='.repeat(50) + '\n');

console.log('=== Test Case 3: Audit complete response ===');
const completeResponse = {
  action: 'audit_complete',
  answer: '```json\n{\n    "answer_content": "单据审核完成，所有信息符合要求。",\n    "audit_conclusion": "审核通过"\n}\n```',
  needs_more_info: false,
  audit_conclusion: "审核通过",
  is_finished: true
};

const result3 = handleAuditAgentAction(completeResponse);
console.log('Result:');
console.log(result3);
console.log('\n' + '='.repeat(50) + '\n');

console.log('✅ All test cases completed successfully!');
console.log('The fix properly extracts user-friendly messages from JSON responses.');