"""
设置单据审核所需的RAG数据
"""

import sys
import os
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.rag import get_rag_manager

async def setup_audit_rag_data():
    """设置单据审核所需的RAG数据"""
    print("=== 设置单据审核RAG数据 ===")
    
    # 获取RAG管理器
    rag_manager = get_rag_manager()
    
    # 定义审核规章制度数据
    audit_regulations = [
        {
            "title": "发票管理规定",
            "content": "所有报销单据必须提供真实、合法、完整的发票。发票内容应与实际业务一致，包括：发票抬头、税号、金额、日期、开票单位等信息。发票必须加盖开票单位公章。",
            "category": "发票管理",
            "source": "《财务报销制度》"
        },
        {
            "title": "差旅费报销标准",
            "content": "员工出差交通标准：飞机经济舱、高铁二等座、普通列车硬卧。住宿标准：一线城市不超过600元/天，二线城市不超过500元/天，三线城市不超过400元/天。伙食补贴：100元/天，交通补贴：50元/天。",
            "category": "差旅费",
            "source": "《差旅费管理办法》"
        },
        {
            "title": "办公用品采购规定",
            "content": "办公用品采购需提前申请，单价超过1000元的需部门经理审批，超过5000元的需分管副总审批。采购后需在3个工作日内完成报销，提供正规发票和采购清单。",
            "category": "采购管理",
            "source": "《采购管理制度》"
        },
        {
            "title": "业务招待费规定",
            "content": "业务招待费需提前申请，注明招待对象、人数、预算标准。一般客户招待标准不超过200元/人，重要客户不超过500元/人。需提供招待对象名单、消费清单和发票。",
            "category": "招待费",
            "source": "《业务招待费管理办法》"
        },
        {
            "title": "报销审批流程",
            "content": "报销流程：申请人提交→部门经理审批→财务审核→财务总监审批→出纳付款。金额超过10000元的需总经理审批。所有报销单据需在业务发生后30天内提交。",
            "category": "审批流程",
            "source": "《财务管理制度》"
        },
        {
            "title": "单据完整性要求",
            "content": "所有报销单据必须完整无缺，包括：原始发票、费用明细、审批单、相关合同或协议等。单据不得涂改，如有错误需重新开具。电子发票需打印并加盖公章。",
            "category": "单据要求",
            "source": "《单据管理规范》"
        }
    ]
    
    # 清空现有数据
    print("\n1. 清空现有RAG数据...")
    list_result = rag_manager.list_documents(limit=1000, offset=0)
    if list_result["success"]:
        for doc in list_result.get('data', []):
            doc_id = doc.get('id')
            if doc_id:
                delete_result = rag_manager.delete_document(doc_id)
                if delete_result["success"]:
                    print(f"   ✓ 删除文档 {doc_id}")
    
    # 添加新的规章制度数据
    print("\n2. 添加新的规章制度数据...")
    result = rag_manager.add_documents(audit_regulations)
    if result["success"]:
        print(f"   ✓ 成功添加 {len(audit_regulations)} 条规章制度")
    else:
        print(f"   ✗ 添加RAG数据失败: {result.get('error')}")
        return
    
    # 验证数据
    print("\n3. 验证RAG数据...")
    list_result = rag_manager.list_documents(limit=100, offset=0)
    if list_result["success"]:
        print(f"   总共 {list_result.get('count', 0)} 条RAG数据")
        for i, doc in enumerate(list_result.get('data', []), 1):
            metadata = doc.get('metadata', {})
            print(f"   {i}. {metadata.get('title', '无标题')} [{metadata.get('category', '无分类')}]")
    
    # 测试搜索功能
    print("\n4. 测试搜索功能...")
    search_result = rag_manager.search_documents("发票管理", n_results=3)
    if search_result["success"]:
        search_results = search_result["data"]
        print(f"   搜索到 {len(search_results)} 条相关规章制度")
        for i, doc in enumerate(search_results, 1):
            metadata = doc.get("metadata", {})
            print(f"   {i}. {metadata.get('title', '无标题')} ({metadata.get('category', '无分类')})")
    
    print("\n=== 设置完成 ===")

if __name__ == "__main__":
    asyncio.run(setup_audit_rag_data())