#!/usr/bin/env python3
"""
测试LLM重试机制
"""

import asyncio
import json
import logging
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.core.llm_retry import LLMRetryConfig, LLMRetryHandler, RetryErrorType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestLLMRetry:
    """测试LLM重试机制"""
    
    def __init__(self):
        self.success_count = 0
        self.attempt_count = 0
    
    async def test_successful_request(self):
        """测试成功请求"""
        logger.info("=== 测试成功请求 ===")
        
        async def mock_func():
            self.attempt_count += 1
            return {"success": True, "data": "test_result"}
        
        config = LLMRetryConfig(max_retries=3)
        handler = LLMRetryHandler(config)
        
        result = await handler.execute_with_retry(mock_func)
        
        assert result["success"] == True
        assert self.attempt_count == 1
        logger.info("✓ 成功请求测试通过")
    
    async def test_rate_limit_retry(self):
        """测试速率限制重试"""
        logger.info("=== 测试速率限制重试 ===")
        
        async def mock_func():
            self.attempt_count += 1
            if self.attempt_count < 3:
                # 前两次失败，模拟速率限制
                raise Exception('API请求失败: 429, {"error":{"code":"tpm_rate_limit_exceeded","message":"Rate limit reached for TPM","type":"rate_limit_exceeded"}}')
            else:
                # 第三次成功
                return {"success": True, "data": "retry_success"}
        
        config = LLMRetryConfig(max_retries=3, base_delay=0.1)  # 减少延迟时间以加快测试
        handler = LLMRetryHandler(config)
        
        result = await handler.execute_with_retry(mock_func)
        
        assert result["success"] == True
        assert self.attempt_count == 3
        logger.info("✓ 速率限制重试测试通过")
    
    async def test_server_error_retry(self):
        """测试服务器错误重试"""
        logger.info("=== 测试服务器错误重试 ===")
        
        async def mock_func():
            self.attempt_count += 1
            if self.attempt_count < 2:
                # 第一次失败，模拟服务器错误
                raise Exception('API请求失败: 500, {"error":{"code":"server_error","message":"Internal server error"}}')
            else:
                # 第二次成功
                return {"success": True, "data": "server_error_success"}
        
        config = LLMRetryConfig(max_retries=3, base_delay=0.1)
        handler = LLMRetryHandler(config)
        
        result = await handler.execute_with_retry(mock_func)
        
        assert result["success"] == True
        assert self.attempt_count == 2
        logger.info("✓ 服务器错误重试测试通过")
    
    async def test_non_retryable_error(self):
        """测试不可重试错误"""
        logger.info("=== 测试不可重试错误 ===")
        
        async def mock_func():
            self.attempt_count += 1
            # 模拟认证错误（不可重试）
            raise Exception('API请求失败: 401, {"error":{"code":"invalid_api_key","message":"Invalid API key"}}')
        
        config = LLMRetryConfig(max_retries=3, base_delay=0.1)
        handler = LLMRetryHandler(config)
        
        try:
            await handler.execute_with_retry(mock_func)
            assert False, "应该抛出异常"
        except Exception as e:
            assert "401" in str(e)
            assert self.attempt_count == 1
            logger.info("✓ 不可重试错误测试通过")
    
    async def test_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        logger.info("=== 测试超过最大重试次数 ===")
        
        async def mock_func():
            self.attempt_count += 1
            # 一直失败
            raise Exception('API请求失败: 429, {"error":{"code":"tpm_rate_limit_exceeded","message":"Rate limit reached for TPM"}}')
        
        config = LLMRetryConfig(max_retries=2, base_delay=0.1)
        handler = LLMRetryHandler(config)
        
        try:
            await handler.execute_with_retry(mock_func)
            assert False, "应该抛出异常"
        except Exception as e:
            assert "429" in str(e)
            assert self.attempt_count == 3  # 初始请求 + 2次重试
            logger.info("✓ 超过最大重试次数测试通过")
    
    async def test_exponential_backoff(self):
        """测试指数退避"""
        logger.info("=== 测试指数退避 ===")
        
        start_time = asyncio.get_event_loop().time()
        
        async def mock_func():
            self.attempt_count += 1
            if self.attempt_count < 4:
                raise Exception('API请求失败: 429, {"error":{"code":"tpm_rate_limit_exceeded"}}')
            else:
                return {"success": True, "data": "backoff_success"}
        
        config = LLMRetryConfig(max_retries=3, base_delay=0.2, exponential_base=2.0)
        handler = LLMRetryHandler(config)
        
        result = await handler.execute_with_retry(mock_func)
        
        end_time = asyncio.get_event_loop().time()
        elapsed_time = end_time - start_time
        
        # 预期延迟：0.2 (第一次重试) + 0.4 (第二次重试) + 0.8 (第三次重试) = 1.4秒
        # 由于有随机抖动，我们检查是否在合理范围内
        assert elapsed_time >= 1.0  # 至少1秒
        assert elapsed_time <= 2.0  # 最多2秒
        
        assert result["success"] == True
        assert self.attempt_count == 4
        logger.info(f"✓ 指数退避测试通过，耗时: {elapsed_time:.2f}秒")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行LLM重试机制测试...")
        
        test_methods = [
            self.test_successful_request,
            self.test_rate_limit_retry,
            self.test_server_error_retry,
            self.test_non_retryable_error,
            self.test_max_retries_exceeded,
            self.test_exponential_backoff
        ]
        
        for test_method in test_methods:
            try:
                self.attempt_count = 0
                await test_method()
            except Exception as e:
                logger.error(f"测试 {test_method.__name__} 失败: {e}")
                raise
        
        logger.info("🎉 所有测试通过！LLM重试机制工作正常。")


async def main():
    """主函数"""
    tester = TestLLMRetry()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())