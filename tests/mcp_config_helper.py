#!/usr/bin/env python3
"""
MCP 配置助手
帮助用户正确配置 MCP 服务器
"""

import json
import sys
from pathlib import Path
import urllib.parse

def detect_server_type(url: str) -> dict:
    """根据 URL 检测服务器类型和配置"""
    parsed = urllib.parse.urlparse(url)
    
    config = {
        "transport": "streamable-http",  # 默认
        "url": url,
        "env": {},
        "disabled": False,
        "autoApprove": [],
        "timeout": 30,
        "retryCount": 3
    }
    
    # 根据路径判断传输类型
    if '/sse' in parsed.path:
        config["transport"] = "sse"
        print(f"✅ 检测到 SSE 传输: {url}")
    elif '/api' in parsed.path:
        config["transport"] = "streamable-http"
        print(f"✅ 检测到 HTTP 传输: {url}")
    else:
        print(f"⚠️  无法确定传输类型，默认使用 HTTP: {url}")
    
    return config

def create_mcp_config(server_name: str, server_url: str) -> dict:
    """创建 MCP 配置"""
    server_config = detect_server_type(server_url)
    
    config = {
        "mcpServers": {
            server_name: server_config
        }
    }
    
    return config

def update_existing_config(config_path: Path, server_name: str, server_url: str):
    """更新现有配置"""
    try:
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
        else:
            existing_config = {"mcpServers": {}}
        
        # 添加新服务器
        server_config = detect_server_type(server_url)
        existing_config["mcpServers"][server_name] = server_config
        
        # 保存配置
        config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(existing_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已保存到: {config_path}")
        return existing_config
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return None

def validate_url(url: str) -> bool:
    """验证 URL 格式"""
    try:
        parsed = urllib.parse.urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return False
        if parsed.scheme not in ['http', 'https']:
            return False
        return True
    except:
        return False

def main():
    """主函数"""
    print("🔧 MCP 配置助手")
    print("=" * 40)
    
    if len(sys.argv) >= 3:
        # 命令行模式
        server_name = sys.argv[1]
        server_url = sys.argv[2]
    else:
        # 交互模式
        print("请输入 MCP 服务器信息:")
        server_name = input("服务器名称: ").strip()
        server_url = input("服务器URL: ").strip()
    
    # 验证输入
    if not server_name:
        print("❌ 服务器名称不能为空")
        return
    
    if not validate_url(server_url):
        print("❌ 无效的 URL 格式")
        return
    
    print(f"\n📝 配置服务器: {server_name}")
    print(f"   URL: {server_url}")
    
    # 创建配置
    config_path = Path(".settings/mcp.json")
    config = update_existing_config(config_path, server_name, server_url)
    
    if config:
        print(f"\n📋 当前配置:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        print(f"\n🚀 使用方法:")
        print(f"1. 启动应用程序")
        print(f"2. 进入设置 -> MCP服务")
        print(f"3. 找到服务器 '{server_name}' 并点击启动")
        
        print(f"\n🧪 测试连接:")
        print(f"python scripts/test_sse_connection.py {server_url}")

def create_example_configs():
    """创建示例配置"""
    examples = {
        "城市分级查询 (SSE)": {
            "url": "http://localhost:8080/sse",
            "description": "SSE 传输的城市分级查询服务"
        },
        "文档处理 (HTTP)": {
            "url": "http://localhost:3001/api",
            "description": "HTTP 传输的文档处理服务"
        },
        "数据分析 (SSE)": {
            "url": "http://localhost:9000/sse",
            "description": "SSE 传输的数据分析服务"
        }
    }
    
    print("📚 示例配置:")
    print("=" * 40)
    
    for name, info in examples.items():
        config = detect_server_type(info["url"])
        print(f"\n🔹 {name}")
        print(f"   描述: {info['description']}")
        print(f"   配置: {json.dumps(config, indent=6, ensure_ascii=False)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--examples":
        create_example_configs()
    else:
        main()