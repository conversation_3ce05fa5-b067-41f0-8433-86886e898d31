#!/usr/bin/env python3
"""
验证 MCP 配置加载功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

from backend.core.mcp.integration import MCPIntegration

async def verify_config():
    """验证配置加载"""
    print("开始验证 MCP 配置加载功能...")
    
    try:
        # 创建集成实例
        integration = MCPIntegration()
        
        # 加载配置（这会从文件加载配置到内存）
        servers = integration.load_config()
        print(f"\n从文件加载了 {len(servers)} 个服务器配置:")
        
        # 获取所有服务器配置
        all_servers = integration.get_all_server_configs()
        print(f"内存中有 {len(all_servers)} 个服务器配置:")
        
        for name, server in all_servers.items():
            print(f"\n服务器名称: {name}")
            print(f"  传输类型: {server.transport_type.value}")
            print(f"  禁用状态: {server.disabled}")
            print(f"  超时时间: {server.timeout}")
            print(f"  重试次数: {server.retry_count}")
            print(f"  自动批准: {server.auto_approve}")
            
            if server.transport_type.name == "STDIO":
                print(f"  命令: {server.command}")
                print(f"  参数: {server.args}")
                print(f"  环境变量: {server.env}")
            elif server.transport_type.name == "SSE":
                print(f"  URL: {server.url}")
                print(f"  环境变量: {server.env}")
        
        # 验证特定服务器
        print("\n验证特定服务器...")
        fs_server = integration.get_server_config("test-filesystem")
        if fs_server:
            print("✓ 成功获取 test-filesystem 服务器")
            print(f"  自动批准操作: {fs_server.auto_approve}")
        else:
            print("❌ 无法获取 test-filesystem 服务器")
            
        city_server = integration.get_server_config("test-city")
        if city_server:
            print("✓ 成功获取 test-city 服务器")
        else:
            print("❌ 无法获取 test-city 服务器")
        
        print("\n🎉 配置加载验证完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 配置加载验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(verify_config())
    sys.exit(0 if success else 1)