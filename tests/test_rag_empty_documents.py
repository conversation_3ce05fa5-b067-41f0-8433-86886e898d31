#!/usr/bin/env python3
"""
测试RAG管理器处理空文档的功能
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.rag.rag_manager import RAGManager

def test_empty_documents():
    """测试处理空文档的功能"""
    print("开始测试RAG管理器处理空文档的功能...")
    
    # 创建RAG管理器实例
    rag_manager = RAGManager("test_empty_docs")
    
    # 测试数据：包含空文档的列表
    test_documents = [
        {
            "title": "有效文档1",
            "content": "这是一个有效的文档内容，包含足够的信息用于生成嵌入向量。",
            "category": "测试分类",
            "source": "测试源"
        },
        {
            "title": "空文档1",
            "content": "",
            "category": "测试分类",
            "source": "测试源"
        },
        {
            "title": "空白文档",
            "content": "   ",
            "category": "测试分类",
            "source": "测试源"
        },
        {
            "title": "有效文档2",
            "content": "这是另一个有效的文档内容，也包含足够的信息用于生成嵌入向量。",
            "category": "测试分类",
            "source": "测试源"
        }
    ]
    
    # 测试添加文档
    print("测试添加包含空文档的列表...")
    result = rag_manager.add_documents(test_documents)
    
    # 检查结果
    if result["success"]:
        print(f"✓ 添加文档成功")
        print(f"✓ 成功添加 {len(result['ids'])} 条文档")
        print(f"✓ 跳过了 {result.get('skipped_count', 0)} 条空文档")
        
        # 验证跳过了2个空文档
        if result.get('skipped_count', 0) == 2:
            print("✓ 正确跳过了2个空文档")
        else:
            print(f"✗ 期望跳过2个空文档，实际跳过了 {result.get('skipped_count', 0)} 个")
            return False
            
        # 验证成功添加了2个有效文档
        if len(result['ids']) == 2:
            print("✓ 正确添加了2个有效文档")
        else:
            print(f"✗ 期望添加2个有效文档，实际添加了 {len(result['ids'])} 个")
            return False
            
    else:
        print(f"✗ 添加文档失败: {result['error']}")
        return False
    
    # 测试全是空文档的情况
    print("\n测试全是空文档的情况...")
    empty_documents = [
        {
            "title": "空文档1",
            "content": "",
            "category": "测试分类",
            "source": "测试源"
        },
        {
            "title": "空文档2",
            "content": "   ",
            "category": "测试分类",
            "source": "测试源"
        }
    ]
    
    result = rag_manager.add_documents(empty_documents)
    
    if result["success"]:
        print(f"✓ 处理全空文档列表成功")
        print(f"✓ 跳过了 {result.get('skipped_count', 0)} 条空文档")
        print(f"✓ 没有添加任何文档到ChromaDB")
        
        # 验证跳过了2个空文档
        if result.get('skipped_count', 0) == 2 and len(result['ids']) == 0:
            print("✓ 正确处理了全空文档的情况")
        else:
            print(f"✗ 期望跳过2个空文档且不添加任何文档，实际跳过了 {result.get('skipped_count', 0)} 个，添加了 {len(result['ids'])} 个")
            return False
    else:
        print(f"✗ 处理全空文档列表失败: {result['error']}")
        return False
    
    print("\n✓ 所有测试通过！")
    return True

def test_empty_embedding_generation():
    """测试空内容的嵌入向量生成"""
    print("\n开始测试空内容的嵌入向量生成...")
    
    # 创建RAG管理器实例
    rag_manager = RAGManager("test_empty_embedding")
    
    # 测试空内容
    empty_embedding = rag_manager._generate_embedding("")
    print(f"✓ 空内容的嵌入向量维度: {len(empty_embedding)}")
    
    # 测试空白内容
    whitespace_embedding = rag_manager._generate_embedding("   ")
    print(f"✓ 空白内容的嵌入向量维度: {len(whitespace_embedding)}")
    
    # 测试有效内容
    valid_embedding = rag_manager._generate_embedding("这是一个有效的测试内容")
    print(f"✓ 有效内容的嵌入向量维度: {len(valid_embedding)}")
    
    # 验证所有嵌入向量都有相同的维度
    if len(empty_embedding) == len(whitespace_embedding) == len(valid_embedding):
        print("✓ 所有嵌入向量都有相同的维度")
        return True
    else:
        print("✗ 嵌入向量维度不一致")
        return False

if __name__ == "__main__":
    print("=== RAG管理器空文档处理测试 ===")
    
    # 运行测试
    test1_passed = test_empty_documents()
    test2_passed = test_empty_embedding_generation()
    
    # 总结
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！RAG管理器现在可以正确处理空文档。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        sys.exit(1)