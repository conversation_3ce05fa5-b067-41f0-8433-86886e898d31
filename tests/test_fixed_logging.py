#!/usr/bin/env python3
"""
测试修复后的日志记录功能
验证日志是否不再重复显示
"""

import sys
import os
import logging

# 添加backend目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend")
sys.path.insert(0, backend_path)

# 设置工作目录为backend目录
os.chdir(backend_path)

# 导入日志配置模块
import colorlog
from core.config import config
from core.logging_config import setup_logging
from datetime import datetime

def setup_test_logging():
    """设置测试用的日志配置"""
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名（按日期）
    log_filename = os.path.join(log_dir, f"audit_system_{datetime.now().strftime('%Y%m%d')}.log")
    
    # 配置日志格式
    formatter = colorlog.ColoredFormatter(
        "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        log_colors={
            'DEBUG':    'cyan',
            'INFO':     'green',
            'WARNING': 'yellow',
            'ERROR':    'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # 配置根 logger
    logger = colorlog.getLogger()
    logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))
    
    # 添加文件处理器
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # 添加控制台处理器（带颜色）
    console_handler = colorlog.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 清除现有的处理器，避免重复
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 添加新的处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 调用setup_logging来设置特定模块的日志级别
    setup_logging()
    
    return logger

def test_logging_output():
    """测试日志输出是否重复"""
    logger = setup_test_logging()
    
    print("开始测试日志输出...")
    
    # 记录不同级别的日志
    logger.debug("这是一条DEBUG级别的日志")
    logger.info("这是一条INFO级别的日志")
    logger.warning("这是一条WARNING级别的日志")
    logger.error("这是一条ERROR级别的日志")
    
    # 测试其他模块的日志记录器
    test_logger = logging.getLogger("test_module")
    test_logger.info("这是来自测试模块的日志")
    
    # 测试专门的功能日志记录器
    from core.logging_config import log_ai_flow, log_audit_flow, log_rag_operation
    
    log_ai_flow("测试步骤", input_data="测试输入", output_data="测试输出")
    log_audit_flow("审核步骤", document_type="发票", document_content="测试内容")
    log_rag_operation("查询操作", collection_name="测试集合", query="测试查询")
    
    print("日志测试完成，请检查上面的日志输出是否重复")

if __name__ == "__main__":
    test_logging_output()