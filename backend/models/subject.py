from sqlalchemy import Column, String, Integer, Boolean
from sqlalchemy.orm import declarative_base
from core.db import Base

class SubjectAccount(Base):
    __tablename__ = "subject_accounts"
    code = Column(String, primary_key=True, index=True, comment="科目编码")
    name = Column(String, nullable=False, comment="科目名称")
    level = Column(Integer, nullable=False, comment="级次")
    parent_code = Column(String, nullable=True, comment="父级编码")
    category = Column(String, nullable=False, comment="类别")
    direction = Column(String, nullable=False, comment="方向")
    aux = Column(String, nullable=True, comment="辅助核算，逗号分隔")
    is_leaf = Column(Boolean, default=True, comment="末级")
    status = Column(String, default="启用", comment="状态")
    remark = Column(String, default="", comment="备注")
    quantity = Column(Boolean, default=False, comment="数量核算") 