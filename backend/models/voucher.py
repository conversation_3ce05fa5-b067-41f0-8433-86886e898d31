from sqlalchemy import Column, String, Integer, Date, DECIMAL, ForeignKey
from sqlalchemy.orm import relationship
from core.db import Base

class Voucher(Base):
    __tablename__ = "vouchers"
    id = Column(Integer, primary_key=True, autoincrement=True)
    voucher_no = Column(String, nullable=False, comment="凭证号")
    date = Column(Date, nullable=False, comment="日期")
    total_debit = Column(DECIMAL(18, 2), nullable=False, comment="借方合计")
    total_credit = Column(DECIMAL(18, 2), nullable=False, comment="贷方合计")
    # 可补充其他字段，如摘要、制单人等
    entries = relationship("VoucherEntry", back_populates="voucher", cascade="all, delete-orphan")

class VoucherEntry(Base):
    __tablename__ = "voucher_entries"
    id = Column(Integer, primary_key=True, autoincrement=True)
    voucher_id = Column(Integer, ForeignKey("vouchers.id"), nullable=False)
    summary = Column(String, nullable=False, comment="摘要")
    account_code = Column(String, nullable=False, comment="科目编码")
    account_name = Column(String, nullable=False, comment="科目名称")
    debit = Column(DECIMAL(18, 2), nullable=False, default=0, comment="借方金额")
    credit = Column(DECIMAL(18, 2), nullable=False, default=0, comment="贷方金额")
    voucher = relationship("Voucher", back_populates="entries") 