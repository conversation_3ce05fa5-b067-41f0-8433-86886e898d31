from sqlalchemy import Column, String, Integer, Boolean, DECIMAL
from core.db import Base

class Asset(Base):
    __tablename__ = "assets"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False, comment="资产名称")
    type = Column(String, nullable=False, comment="资产类别")
    value = Column(DECIMAL(18, 2), nullable=False, comment="资产价值")
    status = Column(String, default="在用", comment="状态")
    remark = Column(String, default="", comment="备注") 