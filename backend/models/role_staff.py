from sqlalchemy import Column, String, Integer, Boolean
from core.db import Base

class Staff(Base):
    __tablename__ = "staffs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False, comment="员工姓名")
    role = Column(String, nullable=False, comment="角色")
    status = Column(String, default="在职", comment="状态")
    remark = Column(String, default="", comment="备注") 