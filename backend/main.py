from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import os
import logging
import uvicorn
from route.subject import router as subject_router
from route.asset import router as asset_router
from route.role_staff import router as role_staff_router
from route.agent import router as agent_router
from route.voucher import router as voucher_router
from route.ledger import router as ledger_router
from route.report import router as report_router
from route.company import router as company_router
from route.mcp import router as mcp_router, initialize_mcp, shutdown_mcp
from route.agent import initialize_mcp_integration

# 配置日志
import colorlog
from core.config import config
from core.logging_config import setup_logging
from datetime import datetime

# 定义链式调用的自定义颜色
CHAIN_COLORS = {
    'voucher_generation': 'purple',
    'document_analysis': 'blue',
    'conversation_summary': 'cyan',
    'transaction_analysis': 'yellow',
    'subject_matching': 'green',
    'amount_calculation': 'purple',
    'voucher_generation_improved': 'bold_purple',
    'balance_verification': 'bold_blue'
}

# 创建 colored formatter
formatter = colorlog.ColoredFormatter(
    "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    log_colors={
        'DEBUG':    'cyan',
        'INFO':     'green',
        'WARNING': 'yellow',
        'ERROR':    'red',
        'CRITICAL': 'red,bg_white',
    }
)

# 为每个链式调用创建自定义 formatter
chain_formatters = {}
for chain_name, color in CHAIN_COLORS.items():
    chain_formatters[chain_name] = colorlog.ColoredFormatter(
        f"%(log_color)s%(asctime)s - %(name)s - %(levelname)s - [CHAIN:{chain_name}] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        log_colors={
            'DEBUG':    'cyan',
            'INFO':     color,
            'WARNING': 'yellow',
            'ERROR':    'red',
            'CRITICAL': 'red,bg_white',
        }
    )

# 创建 handler 并设置 formatter
handler = colorlog.StreamHandler()
handler.setFormatter(formatter)

# 创建日志目录
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)

# 生成日志文件名（按日期）
log_filename = os.path.join(log_dir, f"audit_system_{datetime.now().strftime('%Y%m%d')}.log")

# 配置根 logger
logger = colorlog.getLogger()
logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))

# 添加文件处理器
file_handler = logging.FileHandler(log_filename, encoding='utf-8')
file_handler.setFormatter(formatter)

# 添加控制台处理器（带颜色）
console_handler = colorlog.StreamHandler()
console_handler.setFormatter(formatter)

# 清除现有的处理器，避免重复
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# 添加新的处理器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 为其他模块创建 logger
module_logger = logging.getLogger(__name__)

# 上传文件保存目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_DIR = os.path.join(BASE_DIR, "uploaded_files")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 定义 lifespan 事件处理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件
    await initialize_mcp()
    await initialize_mcp_integration()
    yield
    # 关闭事件
    await shutdown_mcp()

# 初始化FastAPI应用
app = FastAPI(lifespan=lifespan)
app.mount("/files", StaticFiles(directory=UPLOAD_DIR), name="uploaded_files")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根路由
@app.get("/")
async def root():
    return {"message": "会计应用 - 智能体功能"}

# 注册各业务模块路由
app.include_router(subject_router)
app.include_router(asset_router)
app.include_router(role_staff_router)
app.include_router(agent_router)
app.include_router(voucher_router)
app.include_router(ledger_router)
app.include_router(report_router)
app.include_router(company_router)
app.include_router(mcp_router)

# MCP 事件处理已移至 lifespan 事件处理器中

# 启动命令
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
