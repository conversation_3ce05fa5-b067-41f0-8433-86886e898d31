from pydantic import BaseModel
from typing import List
from decimal import Decimal

class VoucherEntry(BaseModel):
    summary: str
    account_code: str
    account_name: str
    debit: Decimal
    credit: Decimal

class Voucher(BaseModel):
    voucher_no: str
    date: str
    entries: List[VoucherEntry]
    total_debit: Decimal
    total_credit: Decimal
    # 可根据实际需求补充其他字段

vouchers: List[Voucher] = []
