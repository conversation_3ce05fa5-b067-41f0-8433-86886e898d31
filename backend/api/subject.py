from fastapi import APIRouter, UploadFile, File, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
import os
import json
from api.experience import add_experience, ExperienceRecord
from datetime import datetime
from config import TEMPLATE_DIR


class SubjectAccount(BaseModel):
    科目编码: str
    科目名称: str
    级次: int
    父级编码: Optional[str] = None
    类别: str
    方向: str
    辅助核算: Optional[List[str]] = []
    末级: bool = True
    状态: str = "启用"
    备注: Optional[str] = ""
    数量核算: bool = False

# 只保留模板相关函数

def build_subject_tree(subjects: List[SubjectAccount]) -> List[Dict[str, Any]]:
    code_map = {s.科目编码: s.dict() for s in subjects}
    tree = []
    for s in subjects:
        node = code_map[s.科目编码]
        node["children"] = []
    for s in subjects:
        if s.父级编码 and s.父级编码 in code_map:
            code_map[s.父级编码]["children"].append(code_map[s.科目编码])
        else:
            tree.append(code_map[s.科目编码])
    return tree

# ========== 新增：自动补全父级编码和级次 ===========
def get_subject_level(code: str) -> int:
    if not code:
        return 0
    return (len(code) - 4) // 2 + 1

def get_parent_code(code: str) -> Optional[str]:
    if not code or len(code) <= 4:
        return None
    return code[:-2]

def check_subject_consistency(code: str, name: str) -> bool:
    # 名称用'-'分隔，层级=分隔数+1
    level = get_subject_level(code)
    name_level = name.count('-') + 1
    return level == name_level

def load_templates():
    key_to_name = {
        "small_enterprise": "小企业会计准则",
        "enterprise": "企业会计准则"
    }
    templates = []
    for fname in os.listdir(TEMPLATE_DIR):
        if fname.endswith('.json') and fname in ["small_enterprise.json", "enterprise.json"]:
            key = fname.replace('.json', '')
            path = os.path.join(TEMPLATE_DIR, fname)
            with open(path, encoding='utf-8') as f:
                subjects_raw = json.load(f)
            subjects = []
            for row in subjects_raw:
                if isinstance(row.get("辅助核算"), str):
                    row["辅助核算"] = [x.strip() for x in row["辅助核算"].split(",") if x.strip()]
                subjects.append(SubjectAccount(**row))
            name = key_to_name.get(key, key)
            desc = f"{name}标准科目表"
            templates.append({
                'key': key,
                'name': name,
                'desc': desc,
                'subjects': [s.dict() for s in subjects]
            })
    return templates

SUBJECT_TEMPLATES = load_templates()

class TemplateKey(BaseModel):
    key: str
