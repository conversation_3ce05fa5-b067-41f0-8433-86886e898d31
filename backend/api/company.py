from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, ConfigDict
from typing import List, Optional, AsyncGenerator
from datetime import date
from sqlalchemy.future import select
from core.db import AsyncSessionLocal, engine
from sqlalchemy.ext.asyncio import AsyncSession
from models.company import Company
from crud.company import get_companies, get_company, get_current_company, create_company, update_company, delete_company
import asyncio

router = APIRouter()

# Pydantic models for request/response
class CompanyBase(BaseModel):
    name: str
    business_scope: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    tax_id: Optional[str] = None
    accounting_standards: Optional[str] = None
    parent_company_id: Optional[int] = None
    company_type: Optional[str] = "parent"
    established_date: Optional[date] = None
    registered_capital: Optional[float] = None
    status: Optional[str] = "active"

class CompanyCreate(CompanyBase):
    pass

class CompanyUpdate(CompanyBase):
    pass

class CompanyInDB(CompanyBase):
    id: int
    
    model_config = ConfigDict(from_attributes=True)

# Dependency to get database session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal(bind=engine) as db:
        yield db

@router.get("/companies", response_model=List[CompanyInDB])
async def get_companies_api(skip: int = 0, limit: int = 100, db=Depends(get_db)):
    companies = await get_companies(db, skip=skip, limit=limit)
    return companies

@router.get("/company/current", response_model=CompanyInDB)
async def get_current_company_api(db=Depends(get_db)):
    """Get the current active company. If no company exists, return 404."""
    company = await get_current_company(db)
    if not company:
        raise HTTPException(status_code=404, detail="No company found")
    return company

@router.get("/companies/{company_id}", response_model=CompanyInDB)
async def get_company_api(company_id: int, db=Depends(get_db)):
    company = await get_company(db, company_id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    return company

@router.post("/companies", response_model=CompanyInDB)
async def create_company_api(company: CompanyCreate, db=Depends(get_db)):
    db_company = Company(**company.dict())
    result = await create_company(db, db_company)
    return result

@router.put("/companies/{company_id}", response_model=CompanyInDB)
async def update_company_api(company_id: int, company: CompanyUpdate, db=Depends(get_db)):
    db_company = await get_company(db, company_id)
    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    # Update the company with new data
    update_data = company.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_company, key, value)
    
    result = await update_company(db, db_company)
    return result

@router.delete("/companies/{company_id}")
async def delete_company_api(company_id: int, db=Depends(get_db)):
    db_company = await get_company(db, company_id)
    if not db_company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    await delete_company(db, db_company)
    return {"success": True, "message": "Company deleted successfully"}

@router.get("/companies/{company_id}/subsidiaries", response_model=List[CompanyInDB])
async def get_subsidiary_companies_api(company_id: int, db=Depends(get_db)):
    """Get all subsidiary companies for a given parent company."""
    result = await db.execute(select(Company).where(Company.parent_company_id == company_id))
    return result.scalars().all()

@router.post("/companies/{parent_company_id}/subsidiaries", response_model=CompanyInDB)
async def create_subsidiary_company_api(parent_company_id: int, company: CompanyCreate, db=Depends(get_db)):
    """Create a new subsidiary company under a parent company."""
    # Check if parent company exists
    parent_company = await get_company(db, parent_company_id)
    if not parent_company:
        raise HTTPException(status_code=404, detail="Parent company not found")
    
    # Create subsidiary company
    db_company = Company(**company.dict(), parent_company_id=parent_company_id, company_type="subsidiary")
    result = await create_company(db, db_company)
    return result