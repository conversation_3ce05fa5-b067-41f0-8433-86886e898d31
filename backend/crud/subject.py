from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy import update, delete
from models.subject import SubjectAccount
from core.db import AsyncSessionLocal

# 获取所有科目（分页）
async def get_subjects(db, skip=0, limit=50, keyword=None):
    query = select(SubjectAccount)
    if keyword:
        query = query.where(
            (SubjectAccount.code.contains(keyword)) |
            (SubjectAccount.name.contains(keyword))
        )
    result = await db.execute(query.offset(skip).limit(limit))
    return result.scalars().all()

# 获取科目总数
async def get_subjects_count(db, keyword=None):
    query = select(SubjectAccount)
    if keyword:
        query = query.where(
            (SubjectAccount.code.contains(keyword)) |
            (SubjectAccount.name.contains(keyword))
        )
    result = await db.execute(query)
    return len(result.scalars().all())

# 新增科目
async def add_subject(db, subject: SubjectAccount):
    db.add(subject)
    try:
        await db.commit()
        await db.refresh(subject)
        return subject
    except IntegrityError:
        await db.rollback()
        raise

# 更新科目
async def update_subject(db, code: str, data: dict):
    await db.execute(
        update(SubjectAccount).where(SubjectAccount.code == code).values(**data)
    )
    await db.commit()

# 删除科目
async def delete_subject(db, code: str):
    await db.execute(
        delete(SubjectAccount).where(SubjectAccount.code == code)
    )
    await db.commit()

# 获取单个科目
async def get_subject(db, code: str):
    result = await db.execute(select(SubjectAccount).where(SubjectAccount.code == code))
    return result.scalar_one_or_none() 