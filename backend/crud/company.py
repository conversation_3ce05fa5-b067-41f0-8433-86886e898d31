from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy import update, delete
from models.company import Company
from core.db import AsyncSessionLocal

# 获取所有公司（分页）
async def get_companies(db, skip=0, limit=50):
    result = await db.execute(select(Company).offset(skip).limit(limit))
    return result.scalars().all()

# 获取公司总数
async def get_companies_count(db):
    result = await db.execute(select(Company))
    return len(result.scalars().all())

# 获取当前活跃公司（只返回第一个公司）
async def get_current_company(db):
    result = await db.execute(select(Company).limit(1))
    return result.scalar_one_or_none()

# 新增公司
async def create_company(db, company: Company):
    db.add(company)
    try:
        await db.commit()
        await db.refresh(company)
        return company
    except IntegrityError:
        await db.rollback()
        raise

# 更新公司
async def update_company(db, company: Company):
    await db.commit()
    await db.refresh(company)
    return company

# 删除公司
async def delete_company(db, company: Company):
    await db.delete(company)
    await db.commit()

# 获取单个公司
async def get_company(db, company_id: int):
    result = await db.execute(select(Company).where(Company.id == company_id))
    return result.scalar_one_or_none()