"""
MCP 集成到 Agent 系统
将 MCP 工具集成到现有的 Agent 工作流中
"""
import logging
from typing import Dict, List, Any, Optional
from .client import mcp_client
from .config import MCPConfigManager, MCPServerConfig

logger = logging.getLogger(__name__)

class MCPIntegration:
    """MCP 集成管理器"""
    
    def __init__(self, config_path: str = None):
        self.mcp_client = mcp_client
        self.config_manager = MCPConfigManager(config_path)
    
    async def initialize(self):
        """初始化 MCP 集成"""
        try:
            # 加载配置文件
            servers = self.config_manager.load_config()
            
            # 将配置添加到客户端
            for server_config in servers.values():
                self.mcp_client.add_server(server_config)
            
            # 启动所有未禁用的服务器
            await self.mcp_client.start_all_servers()
            logger.info("MCP 集成初始化完成")
        except Exception as e:
            logger.error(f"MCP 集成初始化失败: {e}")
    
    def load_config(self) -> Dict[str, MCPServerConfig]:
        """加载 MCP 服务器配置"""
        return self.config_manager.load_config()
    
    def save_config(self, servers: Dict[str, MCPServerConfig] = None) -> bool:
        """保存 MCP 服务器配置"""
        return self.config_manager.save_config(servers)
    
    def add_server(self, config: MCPServerConfig) -> bool:
        """添加服务器配置"""
        # 添加到配置管理器
        success = self.config_manager.add_server(config)
        if success:
            # 添加到客户端
            self.mcp_client.add_server(config)
        return success
    
    def remove_server(self, server_name: str) -> bool:
        """移除服务器配置"""
        # 从配置管理器移除
        success = self.config_manager.remove_server(server_name)
        if success:
            # 从客户端移除（停止服务器）
            try:
                import asyncio
                asyncio.create_task(self.mcp_client.stop_server(server_name))
            except Exception as e:
                logger.warning(f"停止服务器失败: {server_name}, {e}")
        return success
    
    def update_server(self, server_name: str, config: MCPServerConfig) -> bool:
        """更新服务器配置"""
        # 更新配置管理器
        success = self.config_manager.update_server(server_name, config)
        if success:
            # 如果名称改变，需要停止旧服务器并添加新配置
            if server_name != config.name:
                try:
                    import asyncio
                    asyncio.create_task(self.mcp_client.stop_server(server_name))
                except Exception as e:
                    logger.warning(f"停止旧服务器失败: {server_name}, {e}")
            
            # 添加新配置到客户端
            self.mcp_client.add_server(config)
        return success
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """获取服务器配置"""
        return self.config_manager.get_server(server_name)
    
    def get_all_server_configs(self) -> Dict[str, MCPServerConfig]:
        """获取所有服务器配置"""
        return self.config_manager.get_all_servers()
    
    def create_default_config(self) -> bool:
        """创建默认配置文件"""
        return self.config_manager.create_default_config()
    
    def validate_config(self) -> List[str]:
        """验证配置的有效性"""
        return self.config_manager.validate_config()
    
    async def get_available_tools_for_agent(self) -> List[Dict[str, Any]]:
        """获取可用于 Agent 的工具列表"""
        try:
            tools = self.mcp_client.get_available_tools()
            
            # 转换为 Agent 系统可用的格式
            agent_tools = []
            for tool in tools:
                agent_tool = {
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool["input_schema"],
                    "type": "mcp_tool",
                    "server_name": tool["server_name"]
                }
                agent_tools.append(agent_tool)
            
            return agent_tools
        except Exception as e:
            logger.error(f"获取 Agent 工具列表失败: {e}")
            return []
    
    async def call_mcp_tool_for_agent(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """为 Agent 调用 MCP 工具"""
        try:
            result = await self.mcp_client.call_tool(tool_name, parameters)
            
            # 格式化返回结果供 Agent 使用
            return {
                "success": True,
                "tool_name": tool_name,
                "result": result,
                "type": "mcp_tool_result"
            }
        except Exception as e:
            logger.error(f"Agent 调用 MCP 工具失败: {tool_name}, {e}")
            return {
                "success": False,
                "tool_name": tool_name,
                "error": str(e),
                "type": "mcp_tool_error"
            }
    
    def format_tools_for_llm_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """将工具列表格式化为 LLM 提示"""
        if not tools:
            return "当前没有可用的 MCP 工具。"
        
        prompt_parts = ["可用的 MCP 工具:"]
        
        for tool in tools:
            tool_desc = f"\n- {tool['name']}: {tool['description']}"
            
            # 添加参数信息
            if tool.get('parameters', {}).get('properties'):
                params = tool['parameters']['properties']
                param_list = []
                for param_name, param_info in params.items():
                    param_desc = param_info.get('description', '')
                    param_type = param_info.get('type', 'string')
                    param_list.append(f"{param_name} ({param_type}): {param_desc}")
                
                if param_list:
                    tool_desc += f"\n  参数: {', '.join(param_list)}"
            
            prompt_parts.append(tool_desc)
        
        prompt_parts.append("\n要使用工具，请使用以下格式:")
        prompt_parts.append("```json")
        prompt_parts.append('{"action": "use_mcp_tool", "tool_name": "工具名称", "parameters": {"参数名": "参数值"}}')
        prompt_parts.append("```")
        
        return "\n".join(prompt_parts)
    
    async def process_agent_tool_request(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理 Agent 的工具使用请求"""
        try:
            if action_data.get("action") != "use_mcp_tool":
                return {"success": False, "error": "不是 MCP 工具请求"}
            
            tool_name = action_data.get("tool_name")
            parameters = action_data.get("parameters", {})
            
            if not tool_name:
                return {"success": False, "error": "缺少工具名称"}
            
            # 检查工具是否存在
            if tool_name not in self.mcp_client.tools:
                return {"success": False, "error": f"工具不存在: {tool_name}"}
            
            # 调用工具
            result = await self.call_mcp_tool_for_agent(tool_name, parameters)
            return result
            
        except Exception as e:
            logger.error(f"处理 Agent 工具请求失败: {e}")
            return {"success": False, "error": str(e)}

# 全局 MCP 集成实例
mcp_integration = MCPIntegration()
