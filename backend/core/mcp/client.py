"""
基于 FastMCP 的 MCP 客户端
替代原有的自定义 MCP 客户端实现
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional

from .fastmcp_wrapper import FastMCPWrapper
from .config import MCP<PERSON>ransportType, MCPServerConfig, MCPTool, MCPServerStatus

logger = logging.getLogger(__name__)

class MCPClient:
    """基于 FastMCP 的 MCP 客户端"""
    
    def __init__(self):
        self.fastmcp_wrapper = FastMCPWrapper()
    
    def add_server(self, config: MCPServerConfig):
        """添加 MCP 服务器配置"""
        self.fastmcp_wrapper.add_server(config)
    
    async def start_server(self, server_name: str) -> bool:
        """启动 MCP 服务器"""
        return await self.fastmcp_wrapper.start_server(server_name)
    
    async def stop_server(self, server_name: str) -> bool:
        """停止 MCP 服务器"""
        return await self.fastmcp_wrapper.stop_server(server_name)
    
    async def start_all_servers(self):
        """启动所有未禁用的服务器"""
        await self.fastmcp_wrapper.start_all_servers()
    
    async def stop_all_servers(self):
        """停止所有服务器"""
        await self.fastmcp_wrapper.stop_all_servers()
    
    def get_server_status(self, server_name: str = None) -> Dict[str, Any]:
        """获取服务器状态"""
        return self.fastmcp_wrapper.get_server_status(server_name)
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用工具"""
        return self.fastmcp_wrapper.get_available_tools()
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用 MCP 工具"""
        return await self.fastmcp_wrapper.call_tool(tool_name, parameters)
    
    # 为了兼容性，保留一些原有的属性和方法
    @property
    def connections(self) -> Dict[str, Any]:
        """获取连接信息（兼容性属性）"""
        return {
            name: {
                "connected": status.get("status") == "connected",
                "transport_type": status.get("transport_type"),
                "url": status.get("connection_info")
            }
            for name, status in self.fastmcp_wrapper.get_server_status().items()
        }
    
    @property
    def servers(self) -> Dict[str, MCPServerConfig]:
        """获取服务器配置（兼容性属性）"""
        return self.fastmcp_wrapper.servers
    
    @property
    def tools(self) -> Dict[str, MCPTool]:
        """获取工具信息（兼容性属性）"""
        return self.fastmcp_wrapper.tools

# 全局 MCP 客户端实例
mcp_client = MCPClient()
