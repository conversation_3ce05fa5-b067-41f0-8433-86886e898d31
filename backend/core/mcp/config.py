"""
MCP (Model Context Protocol) 客户端配置
"""
import json
import logging
import os
from enum import Enum
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class MCPTransportType(Enum):
    """MCP 传输类型"""
    STDIO = "stdio"
    SSE = "sse"
    STREAMABLE_HTTP = "streamable-http"

@dataclass
class MCPServerConfig:
    """MCP 服务器配置"""
    name: str
    transport_type: MCPTransportType = MCPTransportType.STDIO
    # stdio 配置
    command: Optional[str] = None
    args: List[str] = None
    # http/sse 配置
    url: Optional[str] = None
    # 通用配置
    env: Dict[str, str] = None
    disabled: bool = False
    auto_approve: List[str] = None
    # 连接配置
    timeout: int = 30
    retry_count: int = 3
    
    def __post_init__(self):
        if self.args is None:
            self.args = []
        if self.env is None:
            self.env = {}
        if self.auto_approve is None:
            self.auto_approve = []
        
        # 清理字符串字段，去掉前后空格
        self.name = self.name.strip() if self.name else ""
        if self.command:
            self.command = self.command.strip()
        if self.url:
            self.url = self.url.strip()
        
        # 清理环境变量键和值
        if self.env:
            self.env = {key.strip(): value.strip() for key, value in self.env.items()}
        
        # 清理自动批准列表
        if self.auto_approve:
            self.auto_approve = [item.strip() for item in self.auto_approve]
        
        # 清理参数列表
        if self.args:
            self.args = [arg.strip() for arg in self.args]
        
        # 验证配置
        if self.transport_type == MCPTransportType.STDIO:
            if not self.command:
                raise ValueError("stdio 传输需要指定 command")
        elif self.transport_type in [MCPTransportType.SSE, MCPTransportType.STREAMABLE_HTTP]:
            if not self.url:
                raise ValueError(f"{self.transport_type.value} 传输需要指定 url")

@dataclass
class MCPTool:
    """MCP 工具定义"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str

@dataclass
class MCPServerStatus:
    """MCP 服务器状态"""
    name: str
    status: str  # "connected", "disconnected", "error", "disabled"
    transport_type: MCPTransportType = MCPTransportType.STDIO
    pid: Optional[int] = None
    connection_info: Optional[str] = None
    error_message: Optional[str] = None
    tools: List[MCPTool] = None
    
    def __post_init__(self):
        if self.tools is None:
            self.tools = []


class MCPConfigManager:
    """MCP 配置管理器，负责加载和保存 MCP 服务器配置"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 backend/.settings/mcp_config.json
        """
        if config_path is None:
            # 默认配置文件路径
            backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            settings_dir = os.path.join(backend_dir, ".settings")
            self.config_path = os.path.join(settings_dir, "mcp_config.json")
        else:
            self.config_path = config_path
        
        self.servers: Dict[str, MCPServerConfig] = {}
    
    def load_config(self) -> Dict[str, MCPServerConfig]:
        """
        从配置文件加载 MCP 服务器配置
        
        Returns:
            Dict[str, MCPServerConfig]: 服务器名称到配置的映射
            
        Raises:
            json.JSONDecodeError: 配置文件格式错误
            ValueError: 配置内容无效
        """
        # 如果配置文件不存在，创建一个空的配置文件
        if not os.path.exists(self.config_path):
            logger.warning(f"配置文件不存在，将创建新的配置文件: {self.config_path}")
            self._create_empty_config_file()
            return {}
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            servers = {}
            
            # 解析服务器配置
            # 支持两种格式：{"servers": {...}} 和 {"mcpServers": {...}}
            servers_data = config_data.get('servers') or config_data.get('mcpServers', {})
            if servers_data:
                for server_name, server_data in servers_data.items():
                    try:
                        # 解析传输类型
                        transport_type_str = server_data.get('transport') or server_data.get('transport_type', 'stdio')
                        transport_type = MCPTransportType(transport_type_str)
                        
                        # 创建服务器配置
                        server_config = MCPServerConfig(
                            name=server_name,
                            transport_type=transport_type,
                            command=server_data.get('command'),
                            args=server_data.get('args', []),
                            url=server_data.get('url'),
                            env=server_data.get('env', {}),
                            disabled=server_data.get('disabled', False),
                            auto_approve=server_data.get('auto_approve') or server_data.get('autoApprove', []),
                            timeout=server_data.get('timeout', 30),
                            retry_count=server_data.get('retry_count') or server_data.get('retryCount', 3)
                        )
                        
                        servers[server_name] = server_config
                        logger.info(f"成功加载服务器配置: {server_name}")
                        
                    except Exception as e:
                        logger.error(f"解析服务器配置失败 {server_name}: {e}")
                        continue
            
            self.servers = servers
            logger.info(f"成功加载 {len(servers)} 个服务器配置")
            return servers
            
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise ValueError(f"加载配置文件失败: {e}")
    
    def _create_empty_config_file(self):
        """创建一个空的配置文件"""
        try:
            # 确保配置文件目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # 创建空的配置数据
            config_data = {
                "mcpServers": {}
            }
            
            # 写入配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"成功创建空的配置文件: {self.config_path}")
            
        except Exception as e:
            logger.error(f"创建配置文件失败: {e}")
            raise
    
    def save_config(self, servers: Dict[str, MCPServerConfig] = None) -> bool:
        """
        保存 MCP 服务器配置到文件
        
        Args:
            servers: 要保存的服务器配置，如果为 None 则使用当前内存中的配置
            
        Returns:
            bool: 保存是否成功
        """
        if servers is None:
            servers = self.servers
        
        try:
            # 准备配置数据
            config_data = {
                "mcpServers": {}
            }
            
            # 转换服务器配置为可序列化格式
            for server_name, server_config in servers.items():
                server_data = {
                    "transport": server_config.transport_type.value,
                    "disabled": server_config.disabled,
                    "timeout": server_config.timeout,
                    "retryCount": server_config.retry_count
                }
                
                # 根据传输类型添加特定配置
                if server_config.transport_type == MCPTransportType.STDIO:
                    if server_config.command:
                        server_data["command"] = server_config.command
                    if server_config.args:
                        server_data["args"] = server_config.args
                elif server_config.transport_type in [MCPTransportType.SSE, MCPTransportType.STREAMABLE_HTTP]:
                    if server_config.url:
                        server_data["url"] = server_config.url
                
                # 添加通用配置
                if server_config.env is not None:
                    server_data["env"] = server_config.env
                if server_config.auto_approve is not None:
                    server_data["autoApprove"] = server_config.auto_approve
                
                config_data["mcpServers"][server_name] = server_data
            
            # 确保配置文件目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # 写入配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.servers = servers
            logger.info(f"成功保存 {len(servers)} 个服务器配置到 {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def add_server(self, config: MCPServerConfig) -> bool:
        """
        添加服务器配置
        
        Args:
            config: 服务器配置
            
        Returns:
            bool: 是否添加成功
        """
        self.servers[config.name] = config
        return self.save_config()
    
    def remove_server(self, server_name: str) -> bool:
        """
        移除服务器配置
        
        Args:
            server_name: 服务器名称
            
        Returns:
            bool: 是否移除成功
        """
        if server_name in self.servers:
            del self.servers[server_name]
            return self.save_config()
        return False
    
    def update_server(self, server_name: str, config: MCPServerConfig) -> bool:
        """
        更新服务器配置
        
        Args:
            server_name: 服务器名称
            config: 新的配置
            
        Returns:
            bool: 是否更新成功
        """
        if server_name in self.servers or server_name == config.name:
            # 如果名称改变，先删除旧的配置
            if server_name != config.name and server_name in self.servers:
                del self.servers[server_name]
            
            self.servers[config.name] = config
            return self.save_config()
        return False
    
    def get_server(self, server_name: str) -> Optional[MCPServerConfig]:
        """
        获取服务器配置
        
        Args:
            server_name: 服务器名称
            
        Returns:
            Optional[MCPServerConfig]: 服务器配置，如果不存在则返回 None
        """
        return self.servers.get(server_name)
    
    def get_all_servers(self) -> Dict[str, MCPServerConfig]:
        """
        获取所有服务器配置
        
        Returns:
            Dict[str, MCPServerConfig]: 所有服务器配置
        """
        return self.servers.copy()
    
    def create_default_config(self) -> bool:
        """
        创建默认配置文件
        
        Returns:
            bool: 是否创建成功
        """
        default_servers = {
            "example_stdio": MCPServerConfig(
                name="example_stdio",
                transport_type=MCPTransportType.STDIO,
                command="python",
                args=["-m", "mcp_server.example"],
                env={"DEBUG": "true"},
                disabled=True
            ),
            "example_sse": MCPServerConfig(
                name="example_sse",
                transport_type=MCPTransportType.SSE,
                url="http://localhost:8000/sse",
                disabled=True
            )
        }
        
        return self.save_config(default_servers)
    
    def validate_config(self) -> List[str]:
        """
        验证当前配置的有效性
        
        Returns:
            List[str]: 错误消息列表，如果为空则表示配置有效
        """
        errors = []
        
        for server_name, server_config in self.servers.items():
            try:
                # 验证必需字段
                if not server_name:
                    errors.append(f"服务器名称不能为空")
                
                # 验证传输类型特定配置
                if server_config.transport_type == MCPTransportType.STDIO:
                    if not server_config.command:
                        errors.append(f"服务器 {server_name}: stdio 传输需要指定 command")
                elif server_config.transport_type in [MCPTransportType.SSE, MCPTransportType.STREAMABLE_HTTP]:
                    if not server_config.url:
                        errors.append(f"服务器 {server_name}: {server_config.transport_type.value} 传输需要指定 url")
                
                # 验证超时和重试次数
                if server_config.timeout <= 0:
                    errors.append(f"服务器 {server_name}: timeout 必须大于 0")
                
                if server_config.retry_count < 0:
                    errors.append(f"服务器 {server_name}: retry_count 必须大于等于 0")
                    
            except Exception as e:
                errors.append(f"服务器 {server_name}: 验证失败 - {str(e)}")
        
        return errors