import os
import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class OCRProcessor:
    """OCR 处理器，支持 PaddleOCR"""
    def __init__(self, ocr_engine: str = "paddleocr"):
        self.ocr_engine = ocr_engine.lower()
        self.ocr_instance = None
        self._initialize_ocr()

    def _initialize_ocr(self):
        try:
            if self.ocr_engine == "paddleocr":
                # 保存当前日志配置
                original_level = logging.root.level
                original_handlers = logging.root.handlers[:]
                
                from paddleocr import PaddleOCR
                self.ocr_instance = PaddleOCR(lang='ch')
                
                # 恢复原始日志配置
                logging.root.setLevel(original_level)
                logging.root.handlers = original_handlers
                
            else:
                raise ValueError(f"不支持的 OCR 引擎: {self.ocr_engine}")
            logger.info(f"OCR 引擎 '{self.ocr_engine}' 初始化成功")
        except Exception as e:
            logger.error(f"初始化 OCR 引擎 '{self.ocr_engine}' 失败: {str(e)}")
            raise

    def is_image_file(self, file_path: str) -> bool:
        supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        return Path(file_path).suffix.lower() in supported_extensions

    def extract_text(self, file_path: str) -> Dict[str, Any]:
        logger.info("[DEBUG] OCRProcessor extract_text entering ...")

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"图片文件不存在: {file_path}")
        if not self.is_image_file(file_path):
            raise ValueError(f"不支持的图片格式: {file_path}")
        try:
            if self.ocr_instance is None:
                raise RuntimeError("OCR 实例未初始化")

            logger.info(f"[DEBUG] OCRProcessor extract_text ...")

            results = self.ocr_instance.ocr(file_path)

            logger.info(f"[DEBUG] OCRProcessor extract_text results {results}")

            extracted_text = []
            confidence_scores = []
            if results:
                for page_result in results:
                    if isinstance(page_result, dict):
                        rec_texts = page_result.get('rec_texts', [])
                        rec_scores = page_result.get('rec_scores', [])
                        for i, text in enumerate(rec_texts):
                            if text and text.strip():
                                extracted_text.append(text.strip())
                                confidence = rec_scores[i] if i < len(rec_scores) else 1.0
                                confidence_scores.append(float(confidence))
                    elif isinstance(page_result, (list, tuple)):
                        for line in page_result:
                            try:
                                if isinstance(line, (list, tuple)) and len(line) >= 2:
                                    text_info = line[1]
                                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                        text = str(text_info[0])
                                        confidence = float(text_info[1])
                                    elif isinstance(text_info, str):
                                        text = text_info
                                        confidence = 1.0
                                    else:
                                        continue
                                    if text.strip():
                                        extracted_text.append(text.strip())
                                        confidence_scores.append(confidence)
                            except Exception as e:
                                logger.warning(f"解析 OCR 行失败: {e}")
                                continue
            full_text = '\n'.join(extracted_text)
            avg_conf = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            return {
                'text': full_text,
                'confidence': avg_conf,
                'line_count': len(extracted_text),
                'engine': 'paddleocr',
                'success': True
            }
        except Exception as e:
            logger.error(f"OCR 识别失败: {str(e)}")
            return {
                'text': '',
                'confidence': 0,
                'line_count': 0,
                'engine': self.ocr_engine,
                'success': False,
                'error': str(e)
            }

# 全局 OCR 实例
_ocr_processor = None

def get_ocr_processor(ocr_engine: str = "paddleocr") -> OCRProcessor:
    global _ocr_processor
    if _ocr_processor is None:
        _ocr_processor = OCRProcessor(ocr_engine)
    return _ocr_processor