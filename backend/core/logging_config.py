"""
日志配置 - 配置详细的日志记录以便调试
"""

import logging
import os
from datetime import datetime

def setup_logging():
    """设置日志配置"""
    # 注意：此函数已被移动到 main.py 中统一配置
    # 日志配置现在在 main.py 中使用 colorlog 进行
    # 此处保留函数定义以避免导入错误，但实际配置在 main.py 中完成
    
    # 设置特定模块的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    
    # 记录日志配置完成
    logger = logging.getLogger(__name__)
    logger.info("日志配置已在 main.py 中统一完成")

def get_logger(name):
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)

# 日志记录器装饰器，用于记录函数调用和执行时间
def log_function_calls(logger):
    """装饰器，用于记录函数调用和执行时间"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            logger.debug(f"[FUNCTION] 开始调用函数: {func_name}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"[FUNCTION] 函数 {func_name} 执行成功")
                return result
            except Exception as e:
                logger.error(f"[FUNCTION] 函数 {func_name} 执行失败: {str(e)}", exc_info=True)
                raise
        
        return wrapper
    return decorator

# 数据库操作日志记录器
def log_db_operation(operation, table_name, data=None, result=None, error=None):
    """记录数据库操作"""
    logger = logging.getLogger("DB_OPERATION")
    
    if error:
        logger.error(f"[DB] {operation} 操作失败 - 表: {table_name}, 错误: {str(error)}", exc_info=True)
    else:
        logger.info(f"[DB] {operation} 操作成功 - 表: {table_name}")
        if data:
            logger.debug(f"[DB] 操作数据: {data}")
        if result:
            logger.debug(f"[DB] 操作结果: {result}")

# AI流程日志记录器
def log_ai_flow(step, input_data=None, output_data=None, error=None):
    """记录AI流程"""
    logger = logging.getLogger("AI_FLOW")
    
    if error:
        logger.error(f"[AI] {step} 步骤失败: {str(error)}", exc_info=True)
    else:
        logger.info(f"[AI] {step} 步骤开始")
        if input_data:
            logger.debug(f"[AI] 输入数据: {input_data}")
        if output_data:
            logger.debug(f"[AI] 输出数据: {output_data}")
        logger.info(f"[AI] {step} 步骤完成")

# RAG操作日志记录器
def log_rag_operation(operation, collection_name, query=None, results=None, error=None):
    """记录RAG操作"""
    logger = logging.getLogger("RAG_OPERATION")
    
    if error:
        logger.error(f"[RAG] {operation} 操作失败 - 集合: {collection_name}, 错误: {str(error)}", exc_info=True)
    else:
        logger.info(f"[RAG] {operation} 操作 - 集合: {collection_name}")
        if query:
            logger.debug(f"[RAG] 查询内容: {query}")
        if results:
            logger.debug(f"[RAG] 结果数量: {len(results) if isinstance(results, list) else 'N/A'}")

# 审核流程日志记录器
def log_audit_flow(step, document_type=None, document_content=None, audit_result=None, error=None):
    """记录审核流程"""
    logger = logging.getLogger("AUDIT_FLOW")
    
    if error:
        logger.error(f"[AUDIT] {step} 步骤失败: {str(error)}", exc_info=True)
    else:
        logger.info(f"[AUDIT] {step} 步骤开始")
        if document_type:
            logger.debug(f"[AUDIT] 单据类型: {document_type}")
        if document_content:
            logger.debug(f"[AUDIT] 单据内容长度: {len(document_content)} 字符")
        if audit_result:
            logger.debug(f"[AUDIT] 审核结果: {audit_result}")
        logger.info(f"[AUDIT] {step} 步骤完成")