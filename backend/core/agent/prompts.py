"""
提示管理系统 - 管理和组装各种提示模板
"""

import logging
from typing import Dict, Any, List, Optional
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.prompts.base import BasePromptTemplate
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class PromptTemplate:
    """提示模板类"""
    
    def __init__(self, name: str, template: str, variables: List[str], description: str = ""):
        self.name = name
        self.template = template
        self.variables = variables
        self.description = description
        self.created_at = datetime.now()
    
    def format(self, **kwargs) -> str:
        """格式化模板"""
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            logger.error(f"模板变量缺失: {e}")
            raise ValueError(f"模板 {self.name} 缺少必需变量: {e}")
    
    def validate_variables(self, variables: Dict[str, Any]) -> bool:
        """验证变量是否完整"""
        missing = set(self.variables) - set(variables.keys())
        if missing:
            logger.warning(f"模板 {self.name} 缺少变量: {missing}")
            return False
        return True


class PromptManager:
    """提示管理器"""
    
    def __init__(self):
        self._templates: Dict[str, PromptTemplate] = {}
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """初始化默认模板（从文本文件读取）"""
        import os
        base_dir = os.path.join(os.path.dirname(__file__), 'prompt_templates')
        templates_info = [
            {
                "name": "system_base",
                "file": "system_base.txt",
                "variables": ["current_date", "user_id"],
                "description": "基础系统提示"
            },
            {
                "name": "voucher_generation",
                "file": "voucher_generation.txt",
                "variables": ["user_input", "subjects", "assets", "staff", "experience", "current_date", "user_id"],
                "description": "会计凭证生成模板（自动自洽，无需用户确认按钮）"
            },
            {
                "name": "document_analysis",
                "file": "document_analysis.txt",
                "variables": ["document_type", "document_content", "current_date", "user_id"],
                "description": "文档分析模板"
            },
            {
                "name": "conversation_summary",
                "file": "conversation_summary.txt",
                "variables": ["conversation_history", "current_date", "user_id"],
                "description": "对话总结模板"
            },
            {
                "name": "error_handling",
                "file": "error_handling.txt",
                "variables": ["error_type", "error_message", "user_input"],
                "description": "错误处理模板"
            },
            {
                "name": "accounting_agent",
                "file": "accounting_agent.txt",
                "variables": ["conversation_history", "current_date", "user_id", "user_input"],
                "description": "会计智能体主提示（自动自洽，无需用户确认按钮，不输出cards字段）"
            },
            {
                "name": "transaction_analysis",
                "file": "transaction_analysis.txt",
                "variables": ["user_input", "current_date", "user_id"],
                "description": "交易分析模板"
            },
            {
                "name": "subject_matching",
                "file": "subject_matching.txt",
                "variables": ["transaction_type", "content", "context", "amount", "subjects", "current_date", "user_id"],
                "description": "科目匹配模板"
            },
            {
                "name": "amount_calculation",
                "file": "amount_calculation.txt",
                "variables": ["transaction_type", "content", "context", "primary_amount", "secondary_amounts", "current_date", "user_id"],
                "description": "金额计算模板"
            },
            {
                "name": "voucher_generation_improved",
                "file": "voucher_generation_improved.txt",
                "variables": ["date", "summary", "debit_subjects", "credit_subjects", "amount_breakdown", "tax_details", "other_fees", "current_date", "user_id"],
                "description": "改进的凭证生成模板"
            },
            {
                "name": "balance_verification",
                "file": "balance_verification.txt",
                "variables": ["debit_entries", "credit_entries", "current_date", "user_id"],
                "description": "平衡验证模板"
            },
            {
                "name": "audit_system_base",
                "file": "audit_system_base.txt",
                "variables": ["current_date", "user_id"],
                "description": "单据审核基础系统提示"
            },
            {
                "name": "audit_document",
                "file": "audit_document.txt",
                "variables": ["user_input", "rag_results", "current_date", "user_id"],
                "description": "单据审核文档分析模板"
            },
            {
                "name": "consultation_system_base",
                "file": "consultation_system_base.txt",
                "variables": ["current_date", "user_id"],
                "description": "财务咨询基础系统提示"
            },
            {
                "name": "financial_consultation",
                "file": "financial_consultation.txt",
                "variables": ["user_input", "current_date", "user_id", "company_info", "subjects", "assets", "staff"],
                "description": "财务咨询模板"
            },
        ]
        for info in templates_info:
            file_path = os.path.join(base_dir, info["file"])
            with open(file_path, "r", encoding="utf-8") as f:
                template_str = f.read()
            self.register_template(
                name=info["name"],
                template=template_str,
                variables=info["variables"],
                description=info["description"]
            )
    
    def register_template(self, name: str, template: str, variables: List[str], description: str = "") -> None:
        """注册新模板"""
        self._templates[name] = PromptTemplate(name, template, variables, description)
        logger.info(f"注册提示模板: {name}")
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """获取模板"""
        return self._templates.get(name)
    
    def list_templates(self) -> List[str]:
        """列出所有模板名称"""
        return list(self._templates.keys())
    
    def delete_template(self, name: str) -> bool:
        """删除模板"""
        if name in self._templates:
            del self._templates[name]
            logger.info(f"删除提示模板: {name}")
            return True
        return False
    
    def build_prompt(self, template_name: str, **kwargs) -> str:
        """构建提示"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板不存在: {template_name}")
        
        # 添加默认变量
        default_vars = {
            "current_date": datetime.now().strftime("%Y-%m-%d"),
            "user_id": kwargs.get("user_id", "anonymous")
        }
        
        # 合并变量
        variables = {**default_vars, **kwargs}
        
        # 验证变量
        if not template.validate_variables(variables):
            missing = set(template.variables) - set(variables.keys())
            raise ValueError(f"缺少必需变量: {missing}")
        
        return template.format(**variables)
    
    def build_chat_prompt(self, system_template: str, user_template: str, **kwargs) -> ChatPromptTemplate:
        """构建聊天提示"""
        system_prompt = self.build_prompt(system_template, **kwargs)
        user_prompt = self.build_prompt(user_template, **kwargs)
        
        return ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template(user_prompt)
        ])
    
    def export_templates(self) -> Dict[str, Any]:
        """导出所有模板"""
        return {
            name: {
                "template": template.template,
                "variables": template.variables,
                "description": template.description,
                "created_at": template.created_at.isoformat()
            }
            for name, template in self._templates.items()
        }
    
    def import_templates(self, templates_data: Dict[str, Any]) -> None:
        """导入模板"""
        for name, data in templates_data.items():
            self.register_template(
                name=name,
                template=data["template"],
                variables=data["variables"],
                description=data.get("description", "")
            )


# 全局提示管理器实例
_prompt_manager = None

def get_prompt_manager() -> PromptManager:
    """获取全局提示管理器实例"""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = PromptManager()
    return _prompt_manager
