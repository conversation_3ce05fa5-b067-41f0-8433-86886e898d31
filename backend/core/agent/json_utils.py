"""
JSON解析工具模块
提供统一的JSON解析和清理功能
"""

import json
import re
import logging
from typing import Any, Dict, Union
from langchain_core.output_parsers import JsonOutputParser

logger = logging.getLogger(__name__)


def clean_json_text(text: str) -> str:
    """
    清理JSON文本，移除注释和格式问题
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的JSON文本
    """
    cleaned_text = text
    
    # 提取JSON部分（处理markdown代码块）
    if "```json" in text:
        cleaned_text = text.split("```json")[1].split("```")[0].strip()
    elif "```" in text:
        cleaned_text = text.split("```")[1].split("```")[0].strip()
    
    # 移除JavaScript风格的注释
    # 移除单行注释 (// ...)
    cleaned_text = re.sub(r'//.*$', '', cleaned_text, flags=re.MULTILINE)
    # 移除多行注释 (/* ... */)
    cleaned_text = re.sub(r'/\*.*?\*/', '', cleaned_text, flags=re.DOTALL)
    
    # 移除可能由注释留下的空行和多余的逗号
    cleaned_text = re.sub(r',\s*}', '}', cleaned_text)
    cleaned_text = re.sub(r',\s*]', ']', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)
    
    return cleaned_text


def safe_json_parse(text: str) -> Dict[str, Any]:
    """
    安全地解析JSON文本，处理常见的格式问题
    
    Args:
        text: 要解析的文本
        
    Returns:
        解析后的JSON对象，如果解析失败则返回错误信息
    """
    # 清理文本
    cleaned_text = clean_json_text(text)
    
    try:
        return json.loads(cleaned_text)
    except json.JSONDecodeError as e:
        # 尝试修复常见的JSON错误
        fixed_text = cleaned_text.replace("'", '"')
        try:
            return json.loads(fixed_text)
        except json.JSONDecodeError as e2:
            logger.error(f"JSON解析失败: {cleaned_text}")
            logger.error(f"解析错误详情: {str(e2)}")
            return {
                "error": "无法解析响应",
                "raw_text": text,
                "error_details": str(e2)
            }


class SafeJsonOutputParser(JsonOutputParser):
    """
    安全的JSON输出解析器基类
    处理注释和常见JSON格式问题
    继承自LangChain的JsonOutputParser以保持兼容性
    """
    
    def parse(self, text: str) -> Union[Dict[str, Any], Any]:
        """
        解析文本为JSON对象
        
        Args:
            text: 要解析的文本
            
        Returns:
            解析后的JSON对象
        """
        return safe_json_parse(text)