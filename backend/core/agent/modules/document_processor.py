"""
文档处理模块 - 处理文档处理相关的方法
"""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
import json
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, agent_executor):
        self.agent = agent_executor
    
    async def process_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档"""
        logger.info(f"Entering process_document with document_type={document_type}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 执行文档分析链
            chain_inputs = {
                "document_type": document_type,
                "document_content": document_content,
                "user_id": user_id
            }
            
            logger.info(f"[CHAIN_REQUEST] document_analysis 链式调用输入: {json.dumps(chain_inputs, ensure_ascii=False)}")
            result = await self.agent.chain_manager.run_chain("document_analysis", chain_inputs)
            
            if not result.success:
                return {"success": False, "error": result.error}
            
            # 保存到记忆
            self.agent.memory.add_user_message(f"[文档] {document_type}")
            self.agent.memory.add_ai_message(result.data)
            
            return {
                "success": True,
                "data": result.data,
                "session_id": self.agent.session_id
            }
        except Exception as e:
            logger.error(f"处理文档失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def stream_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理文档"""
        logger.info(f"Entering stream_document with document_type={document_type}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 保存到记忆
            self.agent.memory.add_user_message(f"[文档] {document_type}")
            
            # 准备系统消息和用户消息
            system_prompt = self.agent.prompt_manager.build_prompt("system_base", user_id=user_id)
            document_prompt = self.agent.prompt_manager.build_prompt(
                "document_analysis",
                document_type=document_type,
                document_content=document_content,
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=document_prompt)
            ]
            
            # 流式生成
            full_response = ""
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[STREAM_DOCUMENT_REQUEST] 发送给AI服务器的文档流式消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            async for token in self.agent.llm_manager.stream(messages):
                full_response += token
                logger.info(f"[STREAM_DOCUMENT_RESPONSE] 收到流式响应token: {token}")
                yield token
            
            # 保存完整回复到记忆
            self.agent.memory.add_ai_message(full_response)
            
        except Exception as e:
            logger.error(f"流式处理文档失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg