"""
智能体执行器基础类 - 协调各个组件，执行智能体任务的基础功能
"""

import logging
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
import json
import asyncio
from datetime import datetime
import uuid

from ..llm import get_llm_manager
from ..memory import get_memory
from ..tools import get_tools, get_tool_registry
from ..prompts import get_prompt_manager
from ..chains import get_chain_manager
from ..monitoring import monitor_performance, get_cache_manager

from core.db import AsyncSessionLocal, engine
from models.subject import SubjectAccount
from models.asset import Asset
from models.role_staff import Staff
from models.company import Company
from sqlalchemy.future import select

# 其余代码已全部通过数据库异步获取，无需全局变量

logger = logging.getLogger(__name__)

class AgentExecutor:
    """智能体执行器"""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.llm_manager = get_llm_manager()
        self.memory = get_memory(self.session_id)
        self.prompt_manager = get_prompt_manager()
        self.chain_manager = get_chain_manager()
        self.tool_registry = get_tool_registry()
        self.cache_manager = get_cache_manager()

        # 设置链式调用的LLM
        if self.llm_manager.is_configured():
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
    
    def configure_llm(self, api_key: str, base_url: str, model: str, **kwargs) -> bool:
        """配置语言模型"""
        logger.info(f"Entering configure_llm with api_key={api_key[:5]}..., base_url={base_url}, model={model}")
        try:
            self.llm_manager.configure(api_key, base_url, model, **kwargs)
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
            return True
        except Exception as e:
            logger.error(f"配置LLM失败: {str(e)}")
            return False
    
    async def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        logger.info(f"Entering execute_tool with tool_name={tool_name}, kwargs={kwargs}")
        try:
            result = await self.tool_registry.execute_tool(tool_name, **kwargs)

            logger.info(f"execute_tool, tool_name: {tool_name}, result: {result}")

            return {
                "success": result.success,
                "data": result.data,
                "error": result.error,
                "execution_time": result.execution_time,
                "tool_name": result.tool_name
            }
        except Exception as e:
            logger.error(f"执行工具失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        logger.info("Entering get_conversation_history")
        messages = self.memory.chat_history.get_messages()
        history = []
        
        for msg in messages:
            if isinstance(msg, HumanMessage):
                history.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                history.append({"role": "assistant", "content": msg.content})
            elif isinstance(msg, SystemMessage):
                history.append({"role": "system", "content": msg.content})
        
        return history
    
    def clear_history(self) -> None:
        """清空对话历史"""
        logger.info("Entering clear_history")
        self.memory.clear()
    
    async def _process_files(self, files: List[Dict]) -> str:
        """处理文件内容"""
        logger.info(f"Entering _process_files with {len(files) if files else 0} files")
        file_contents = []
        for file_info in files:
            try:
                file_path = file_info.get("file_path")
                logger.info(f"处理文件: {file_path}")  # 路径调试日志，改为 info
                file_name = file_info.get("file_name", "未知文件")
                file_type = file_info.get("file_type", "未知类型")
                
                if file_type == "image":
                    # OCR处理图片
                    result = await self.execute_tool("ocr_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (图片)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (图片)\n处理失败：{result['error']}\n")
                elif file_type == "pdf":
                    # PDF处理
                    result = await self.execute_tool("pdf_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (PDF)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (PDF)\n处理失败：{result['error']}\n")
                else:
                    file_contents.append(f"文件：{file_name}\n不支持的文件类型：{file_type}\n")
            except Exception as e:
                logger.error(f"处理文件失败: {str(e)}")
                file_contents.append(f"文件：{file_info.get('file_name', '未知文件')}\n处理失败：{str(e)}\n")
        
        return "\n".join(file_contents)

    async def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """异步收集上下文数据"""
        logger.info(f"Entering _gather_context_data with user_id={user_id}")
        db = AsyncSessionLocal(bind=engine)
        try:
            subjects_result = await db.execute(select(SubjectAccount))
            assets_result = await db.execute(select(Asset))
            staffs_result = await db.execute(select(Staff))
            # NEW: Collect company information
            company_result = await db.execute(select(Company))
            company = company_result.scalars().first()  # Assuming single company for now
            
            subjects = [
                {c.name: getattr(s, c.name) for c in s.__table__.columns}
                for s in subjects_result.scalars().all()
            ]
            assets = [
                {c.name: getattr(a, c.name) for c in a.__table__.columns}
                for a in assets_result.scalars().all()
            ]
            staff = [
                {c.name: getattr(st, c.name) for c in st.__table__.columns}
                for st in staffs_result.scalars().all()
            ]
        except asyncio.CancelledError:
            logger.warning("Database operation was cancelled, gracefully closing connection")
            try:
                await db.close()
            except Exception as e:
                logger.error(f"Error closing database connection during cancellation: {e}")
            raise
        except Exception as e:
            logger.error(f"Error gathering context data: {e}")
            try:
                await db.close()
            except Exception as close_error:
                logger.error(f"Error closing database connection: {close_error}")
            raise
        else:
            await db.close()
        from api.experience import get_experience
        experience = get_experience(user_id, limit=5) if user_id else []
        return {
            "subjects": subjects,
            "assets": assets,
            "staff": staff,
            "experience": experience,
            "company": {  # NEW: Add company data
                "name": company.name if company else "",
                "business_scope": company.business_scope if company else "",
                "industry": company.industry if company else "",
                "accounting_standards": company.accounting_standards if company else "",
                "tax_info": company.tax_id if company else ""
            } if company else None
        }


# 全局智能体执行器实例字典
_agent_executors: Dict[str, AgentExecutor] = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    logger.info(f"Entering get_agent_executor with session_id={session_id}")
    global _agent_executors
    
    if not session_id:
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]