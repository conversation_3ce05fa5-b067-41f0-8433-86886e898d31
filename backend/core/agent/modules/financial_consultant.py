"""
财务咨询模块 - 处理财务咨询相关的方法
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
import json
import re
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

class FinancialConsultant:
    """财务咨询器"""
    
    def __init__(self, agent_executor):
        self.agent = agent_executor
    
    async def stream_consultation_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理财务咨询消息"""
        logger.info(f"Entering stream_consultation_message with message={message}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 处理文件内容
            file_content = ""
            if files and len(files) > 0:
                file_content = await self.agent._process_files(files)
                # 将文件内容添加到用户消息中
                enhanced_message = f"{message}\n\n附件内容：\n{file_content}" if message.strip() else f"请分析以下文件内容并提供财务咨询：\n{file_content}"
            else:
                enhanced_message = message

            logger.info(f"enhanced_message : {enhanced_message}")
            
            self.agent.memory.add_user_message(enhanced_message)
            
            # 收集上下文数据
            context_data = await self.agent._gather_context_data(user_id)
            
            # 使用RAG搜索相关的财务知识
            rag_results = await self._search_consultation_rag(enhanced_message)
            
            # 格式化RAG结果为字符串
            rag_results_str = ""
            if rag_results:
                rag_results_str = "\n".join([
                    f"- {result.get('title', '无标题')} ({result.get('category', '无分类')}): {result.get('content', '')}"
                    for result in rag_results
                ])
            
            # 准备系统提示和用户提示
            system_prompt = self.agent.prompt_manager.build_prompt("consultation_system_base", user_id=user_id)
            
            # 构建咨询提示
            consultation_prompt = self.agent.prompt_manager.build_prompt(
                "financial_consultation",
                user_input=enhanced_message,
                rag_results=rag_results_str,
                current_date=datetime.now().strftime("%Y-%m-%d"),
                user_id=user_id,
                company_info=context_data.get("company", {}),
                subjects=context_data.get("subjects", []),
                assets=context_data.get("assets", []),
                staff=context_data.get("staff", [])
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=consultation_prompt)
            ]
            
            # 流式生成
            full_response = ""
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[STREAM_CONSULTATION_REQUEST] 发送给AI服务器的财务咨询流式消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            # 收集完整响应
            async for token in self.agent.llm_manager.stream(messages):
                full_response += token
                # 流式返回原始token给前端显示
                yield token
            
            # 保存完整回复到记忆
            self.agent.memory.add_ai_message(full_response)
            
            # 解析AI响应并生成结构化JSON
            try:
                # 尝试从响应中提取JSON结构
                consultation_result = await self._parse_consultation_response(full_response)
                
                # 生成最终的JSON响应
                final_response = {
                    "success": True,
                    "action": consultation_result.get("action", "none"),
                    "answer": consultation_result.get("answer", full_response),
                    "consultation_conclusion": consultation_result.get("consultation_conclusion", "咨询完成"),
                    "needs_more_info": consultation_result.get("needs_more_info", False),
                    "required_info": consultation_result.get("required_info", []),
                    "is_finished": consultation_result.get("is_finished", True),
                    "analysis": consultation_result.get("analysis", ""),
                    "recommendations": consultation_result.get("recommendations", [])
                }
                
                # 返回结构化JSON响应，添加换行符分隔
                yield "\n" + json.dumps(final_response, ensure_ascii=False)
                
            except Exception as e:
                logger.error(f"解析财务咨询响应失败: {str(e)}")
                # 如果解析失败，返回基本结构
                fallback_response = {
                    "success": True,
                    "action": "none",
                    "answer": full_response,
                    "consultation_conclusion": "咨询完成",
                    "needs_more_info": False,
                    "required_info": [],
                    "is_finished": True,
                    "analysis": full_response,
                    "recommendations": []
                }
                yield "\n" + json.dumps(fallback_response, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"流式处理财务咨询消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def _parse_consultation_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI财务咨询响应，提取结构化信息"""
        try:
            import re
            import json
            
            # 默认值
            result = {
                "action": "none",
                "answer": response_text,
                "consultation_conclusion": "咨询完成",
                "needs_more_info": False,
                "required_info": [],
                "is_finished": True,
                "analysis": response_text,
                "recommendations": []
            }
            
            # 首先尝试提取标记格式的响应
            tag_extracted = False
            
            # 标记提取模式
            tag_patterns = {
                'consultation_conclusion': r'<\|consultation_conclusion\|>\s*(.*?)\s*<\|/consultation_conclusion\|>',
                'action': r'<\|action\|>\s*(.*?)\s*<\|/action\|>',
                'needs_more_info': r'<\|needs_more_info\|>\s*(.*?)\s*<\|/needs_more_info\|>',
                'required_info': r'<\|required_info\|>\s*(.*?)\s*<\|/required_info\|>',
                'is_finished': r'<\|is_finished\|>\s*(.*?)\s*<\|/is_finished\|>',
                'recommendations': r'<\|recommendations\|>\s*(.*?)\s*<\|/recommendations\|>'
            }
            
            extracted_data = {}
            for key, pattern in tag_patterns.items():
                # 使用 DOTALL 标志支持多行匹配
                match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
                if match:
                    extracted_value = match.group(1).strip()
                    # 清理内容中的多余空白字符
                    extracted_value = re.sub(r'\s+', ' ', extracted_value)
                    extracted_data[key] = extracted_value
                    logger.info(f"提取到标记 {key}: {extracted_data[key]}")
            
            # 如果提取到了关键标记，则认为是结构化响应
            if extracted_data and ('action' in extracted_data or 'consultation_conclusion' in extracted_data):
                # 处理action字段
                action = extracted_data.get("action", "none").lower().strip()
                # 标准化action值
                if action in ["provide_advice", "提供建议", "建议"]:
                    action = "provide_advice"
                elif action in ["request_more_info", "需要更多信息", "补充信息"]:
                    action = "request_more_info"
                else:
                    action = "none"
                
                # 处理needs_more_info字段
                needs_more_info_str = extracted_data.get("needs_more_info", "false").lower().strip()
                needs_more_info = needs_more_info_str in ["true", "是", "需要", "1"]
                
                # 处理required_info字段
                required_info_str = extracted_data.get("required_info", "").strip()
                required_info = []
                if required_info_str:
                    # 支持多种分隔符
                    items = re.split(r'[，,、；;|\n]', required_info_str)
                    for item in items:
                        cleaned = item.strip()
                        if len(cleaned) > 1:
                            required_info.append(cleaned)
                
                # 处理is_finished字段
                is_finished_str = extracted_data.get("is_finished", "true").lower().strip()
                is_finished = is_finished_str not in ["false", "否", "未完成", "0"]
                
                # 处理recommendations字段
                recommendations_str = extracted_data.get("recommendations", "[]").strip()
                recommendations = []
                if recommendations_str:
                    try:
                        # 尝试解析JSON格式的推荐
                        if recommendations_str.startswith('[') and recommendations_str.endswith(']'):
                            recommendations = json.loads(recommendations_str)
                        else:
                            # 如果不是JSON格式，尝试按分隔符分割
                            items = re.split(r'[，,、；;|\n]', recommendations_str)
                            for item in items:
                                cleaned = item.strip()
                                if len(cleaned) > 1:
                                    recommendations.append(cleaned)
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，尝试按分隔符分割
                        items = re.split(r'[，,、；;|\n]', recommendations_str)
                        for item in items:
                            cleaned = item.strip()
                            if len(cleaned) > 1:
                                recommendations.append(cleaned)
                
                # 如果action是request_more_info，自动设置相关字段
                if action == "request_more_info":
                    needs_more_info = True
                    is_finished = False
                    if not required_info:
                        required_info = ["请提供更完整的财务信息"]
                
                # 更新结果
                result.update({
                    "action": action,
                    "consultation_conclusion": extracted_data.get("consultation_conclusion", "咨询完成"),
                    "needs_more_info": needs_more_info,
                    "required_info": required_info,
                    "is_finished": is_finished,
                    "analysis": response_text,
                    "recommendations": recommendations
                })
                tag_extracted = True
                logger.info(f"成功解析标记格式响应: action={action}, needs_more_info={needs_more_info}, required_info={required_info}")
            
            # 如果标记解析成功，直接返回结果
            if tag_extracted:
                return result
            
            # 备用：尝试提取JSON格式的响应
            json_extracted = False
            
            # 尝试提取```json代码块中的JSON
            json_block_pattern = r'```json\s*\n?([\s\S]*?)```'
            json_match = re.search(json_block_pattern, response_text, re.IGNORECASE)
            
            if json_match:
                try:
                    json_str = json_match.group(1).strip()
                    logger.info(f"提取到JSON代码块: {json_str}")
                    json_data = json.loads(json_str)
                    
                    # 更新结果
                    if isinstance(json_data, dict):
                        result.update({
                            "action": json_data.get("action", "none"),
                            "consultation_conclusion": json_data.get("consultation_conclusion", "咨询完成"),
                            "needs_more_info": json_data.get("needs_more_info", False),
                            "required_info": json_data.get("required_info", []),
                            "is_finished": json_data.get("is_finished", True),
                            "analysis": json_data.get("analysis", response_text),
                            "recommendations": json_data.get("recommendations", [])
                        })
                        json_extracted = True
                        logger.info(f"成功解析JSON格式响应: {result}")
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON代码块解析失败: {e}")
            
            # 如果JSON解析成功，直接返回结果
            if json_extracted:
                return result
            
            # 最后的文本分析备用方案
            logger.info("使用文本分析备用方案解析响应")
            
            # 检查咨询结论关键词
            if any(keyword in response_text for keyword in ["咨询完成", "建议如下", "综上所述", "根据分析"]):
                result.update({
                    "consultation_conclusion": "咨询完成",
                    "action": "provide_advice",
                    "needs_more_info": False,
                    "is_finished": True
                })
            elif any(keyword in response_text for keyword in ["需要补充", "请提供", "缺少", "信息不足", "需要更多"]):
                # 简化的信息提取逻辑
                required_info = []
                
                # 查找编号列表
                numbered_matches = re.findall(r'(?:^|\n)\s*\d+[\.、]\s*([^\n]+)', response_text, re.MULTILINE)
                for match in numbered_matches:
                    cleaned = match.strip()
                    if len(cleaned) > 2:
                        required_info.append(cleaned)
                
                # 查找项目符号列表
                bullet_matches = re.findall(r'(?:^|\n)\s*[-*•]\s*([^\n]+)', response_text, re.MULTILINE)
                for match in bullet_matches:
                    cleaned = match.strip()
                    if len(cleaned) > 2:
                        required_info.append(cleaned)
                
                if not required_info:
                    required_info = ["请提供更完整的财务信息"]
                
                result.update({
                    "consultation_conclusion": "需要更多信息",
                    "action": "request_more_info",
                    "needs_more_info": True,
                    "required_info": required_info,
                    "is_finished": False
                })
            else:
                # 默认情况
                result.update({
                    "action": "none",
                    "consultation_conclusion": "咨询完成",
                    "is_finished": True
                })
            
            # 尝试提取建议
            recommendations = []
            # 查找建议相关的段落
            advice_patterns = [
                r'(?:建议|推荐|应|应该)[\s:：]*(.*?)(?=\n\n|\n[0-9]+\.|\n-|\n•|$)',
                r'(?:[0-9]+\.|\s*[-*•])\s*(.*?)(?=\n\n|\n[0-9]+\.|\n-|\n•|$)'
            ]
            
            for pattern in advice_patterns:
                matches = re.findall(pattern, response_text, re.DOTALL)
                for match in matches:
                    cleaned = match.strip()
                    if len(cleaned) > 5:  # 只保留有意义的建议
                        recommendations.append(cleaned)
            
            if recommendations:
                result["recommendations"] = recommendations
            
            logger.info(f"文本分析结果: action={result['action']}, consultation_conclusion={result['consultation_conclusion']}")
            return result
            
        except Exception as e:
            logger.error(f"解析财务咨询响应失败: {str(e)}")
            return {
                "action": "none",
                "answer": response_text,
                "consultation_conclusion": "解析失败，请重试",
                "needs_more_info": True,
                "required_info": ["请重新提交咨询请求"],
                "is_finished": False,
                "analysis": response_text,
                "recommendations": []
            }
    
    async def _search_consultation_rag(self, query: str) -> List[Dict]:
        """搜索财务咨询相关的知识库"""
        logger.info(f"Searching consultation RAG with query: {query}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 搜索相关文档
            result = rag_manager.search_documents(query, n_results=5)
            
            if result["success"]:
                # 格式化搜索结果
                rag_results = []
                for doc in result["data"]:
                    rag_results.append({
                        "title": doc["metadata"].get("title", ""),
                        "content": doc["content"],
                        "category": doc["metadata"].get("category", ""),
                        "source": doc["metadata"].get("source", "")
                    })
                return rag_results
            else:
                logger.error(f"RAG搜索失败: {result.get('error', '未知错误')}")
                return []
        except Exception as e:
            logger.error(f"搜索财务咨询RAG失败: {str(e)}")
            return []