"""
智能体执行器 - 协调各个组件，执行智能体任务
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator

from .modules.agent_base import AgentExecutor as BaseAgentExecutor, get_agent_executor
from .modules.voucher_generator import VoucherGenerator
from .modules.document_auditor import DocumentAuditor
from .modules.document_processor import DocumentProcessor
from .modules.regulation_parser import RegulationParser, parse_audit_regulations, save_parsed_regulations
from .modules.financial_consultant import FinancialConsultant

logger = logging.getLogger(__name__)

class AgentExecutor(BaseAgentExecutor):
    """智能体执行器 - 扩展基础类，添加各个功能模块"""
    
    def __init__(self, session_id: Optional[str] = None):
        super().__init__(session_id)
        
        # 初始化各个功能模块
        self.voucher_generator = VoucherGenerator(self)
        self.document_auditor = DocumentAuditor(self)
        self.document_processor = DocumentProcessor(self)
        self.regulation_parser = RegulationParser(self)
        self.financial_consultant = FinancialConsultant(self)
    
    # 凭证生成相关方法
    async def process_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """处理用户消息"""
        return await self.voucher_generator.process_message(message, user_id, files)
    
    async def stream_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理用户消息"""
        async for token in self.voucher_generator.stream_message(message, user_id, files):
            yield token
    
    # 文档处理相关方法
    async def process_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档"""
        return await self.document_processor.process_document(document_type, document_content, user_id)
    
    async def stream_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理文档"""
        async for token in self.document_processor.stream_document(document_type, document_content, user_id):
            yield token
    
    # 单据审核相关方法
    async def stream_audit_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理单据审核消息"""
        async for token in self.document_auditor.stream_audit_message(message, user_id, files):
            yield token
    
    async def _search_audit_rag(self, query: str) -> List[Dict]:
        """搜索单据审核相关的规章制度"""
        return await self.document_auditor._search_audit_rag(query)
    
    async def add_audit_rag_data(self, data: List[Dict]) -> Dict:
        """添加单据审核相关的RAG数据"""
        return await self.document_auditor.add_audit_rag_data(data)
    
    async def list_audit_rag_data(self) -> Dict:
        """列出单据审核相关的RAG数据"""
        return await self.document_auditor.list_audit_rag_data()
    
    async def delete_audit_rag_data(self, item_id: str) -> Dict:
        """删除单据审核相关的RAG数据"""
        return await self.document_auditor.delete_audit_rag_data(item_id)
    
    async def update_audit_rag_data(self, item_id: str, data: Dict) -> Dict:
        """更新单据审核相关的RAG数据"""
        return await self.document_auditor.update_audit_rag_data(item_id, data)
    
    async def _parse_audit_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI审核响应，提取结构化信息"""
        return await self.document_auditor._parse_audit_response(response_text)
    
    # 财务咨询相关方法
    async def stream_consultation_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理财务咨询消息"""
        async for token in self.financial_consultant.stream_consultation_message(message, user_id, files):
            yield token
    
    async def _parse_consultation_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI财务咨询响应，提取结构化信息"""
        return await self.financial_consultant._parse_consultation_response(response_text)
    
    # 规章制度解析相关方法
    async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
        """解析规章制度文档"""
        return await self.regulation_parser.parse_audit_regulations(document_content)
    
    async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
        """保存解析后的规章制度数据"""
        return await self.regulation_parser.save_parsed_regulations(parsed_data, original_filename)


# 更新全局智能体执行器实例字典，使用新的AgentExecutor类
_agent_executors = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    logger.info(f"Entering get_agent_executor with session_id={session_id}")
    global _agent_executors
    
    if not session_id:
        import uuid
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]
