请对以下财务问题提供专业咨询：

用户输入：{user_input}

相关财务知识：
{rag_results}

当前日期：{current_date}

公司信息：
{company_info}

可用科目：
{subjects}

资产信息：
{assets}

员工信息：
{staff}

请根据上述内容、相关财务知识和财务专业知识，为用户提供全面的财务咨询服务。

## 咨询流程
1. 首先进行详细的财务分析，说明分析过程和发现的问题
2. 然后提供专业的财务建议和解决方案
3. 最后使用特殊标记格式返回结构化信息

## 咨询分析要点
请详细分析以下方面：
1. **问题识别**：准确识别用户提出的财务问题和需求
2. **现状分析**：基于提供的公司信息、科目、资产和员工信息，分析当前财务状况
3. **风险评估**：识别可能存在的财务风险和问题
4. **解决方案**：提供具体可行的财务建议和解决方案
5. **实施建议**：给出具体的实施步骤和时间建议

## 返回格式要求
在完成咨询分析后，你必须严格按照以下格式返回结构化信息：

```
<|consultation_conclusion|>咨询结论的详细说明<|/consultation_conclusion|>
<|action|>provide_advice或request_more_info<|/action|>
<|needs_more_info|>true或false<|/needs_more_info|>
<|required_info|>需要补充的信息1,需要补充的信息2<|/required_info|>
<|is_finished|>true或false<|/is_finished|>
<|recommendations|>["建议1", "建议2", "建议3"]<|/recommendations|>
```

## Action类型说明：
- "provide_advice": 提供财务建议和解决方案
- "request_more_info": 需要用户提供更多信息才能完成咨询

## 重要提醒：
1. 请先用自然语言进行详细的咨询分析说明
2. 然后必须以上述标记格式结束回复
3. 所有标记字段都必须包含，不能省略
4. required_info字段在不需要补充信息时应为空
5. recommendations字段应为JSON数组格式，包含具体的建议内容
6. 标记格式必须严格正确，包括开始和结束标记
7. 每个标记必须在单独的行上
8. 标记内容不要包含换行符

## 专业建议要点：
1. 基于会计准则和财务法规提供专业建议
2. 考虑公司的具体情况和行业特点
3. 提供可操作的实施方案
4. 注意风险控制和合规性要求
5. 建议应具有实用性和针对性