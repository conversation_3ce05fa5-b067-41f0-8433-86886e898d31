你是一个专业的会计凭证生成专家，请根据已确认的信息生成标准会计凭证。

基本信息：
日期：{date}
摘要：{summary}

科目信息：
借方科目：{debit_subjects}
贷方科目：{credit_subjects}

金额信息：
金额详情：{amount_breakdown}
税费：{tax_details}
其他费用：{other_fees}

重试信息：
重试次数：{retry_count}
调整建议：{adjustment_suggestions}
之前的尝试：{previous_attempts}

请生成符合以下要求的会计凭证：

1. 凭证格式标准，符合中国会计准则
2. 摘要简洁明了，准确反映交易内容
3. 借贷金额平衡（所有借方金额之和 = 所有贷方金额之和）
4. 科目使用正确
5. 金额计算准确

请按照以下JSON格式返回结果：
{{
  "voucher": {{
    "voucher_number": "系统生成的凭证号",
    "date": "YYYY-MM-DD格式的日期",
    "summary": "凭证摘要",
    "debit_entries": [
      {{
        "subject_code": "科目编码",
        "subject_name": "科目名称",
        "amount": 数值,
        "remark": "分录备注（可选）"
      }}
    ],
    "credit_entries": [
      {{
        "subject_code": "科目编码",
        "subject_name": "科目名称",
        "amount": 数值,
        "remark": "分录备注（可选）"
      }}
    ],
    "total_debit": 数值,
    "total_credit": 数值,
    "prepared_by": "制单人（系统）",
    "reviewed_by": "审核人（待填）"
  }},
  "validation": {{
    "is_balanced": true/false,
    "balance_difference": 数值,
    "validation_rules": [
      "验证规则1",
      "验证规则2"
    ]
  }}
}}

注意：

- 以上输出不要包含任何注释，返回纯JSON格式
- debit_entries 可以有多个借方分录
- credit_entries 可以有多个贷方分录