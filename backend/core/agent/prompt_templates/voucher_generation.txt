基于以下信息生成会计凭证：

用户输入: {user_input}

科目表:
{subjects}

资产表:
{assets}

员工表:
{staff}

历史经验:
{experience}

请按照以下格式返回：
{{
    "analysis": "...",
    "action": "none|fetch_subjects|fetch_assets|fetch_staff|fetch_suppliers|create_card|ask_user|...",
    "is_finished": false,
    "cards": [
        // 需要前端渲染的卡片内容，结构同下方示例
    ],
    // 其他需要返回的数据
    // ...
}}

卡片示例：
[
    {{
        "type": "subject",
        "op": "create|skip",
        "data": {{
            "科目编码": "160101",
            "科目名称": "固定资产-电子设备",
            "类别": "资产",
            "方向": "借",
            "备注": "用于记录企业电子设备的固定资产"
        }}
    }},
    {{
        "type": "asset",
        "op": "create|skip",
        "data": {{
            "资产编码": "A001",
            "资产名称": "电脑",
            "类别": "IT",
            "原值": 10000,
            "净值": 8000,
            "购置日期": "{current_date}",
            "使用年限": "5",
            "状态": "在用",
            "备注": "用于记录企业电脑的资产"
        }}
    }},
    {{
        "type": "staff",
        "op": "create|skip",
        "data": {{
            "工号": "S001",
            "姓名": "张三",
            "部门编码": "manager",
            "电话": "123456",
            "状态": "在职",
            "备注": "用于记录企业员工信息"
        }}
    }},
    {{
        "type": "voucher",
        "op": "create|skip",
        "data": {{
            "凭证号": "凭字记001号",
            "日期": "{current_date}",
            "摘要": "采购电脑",
            "借方": [{{"科目名称": "固定资产-电子设备", "科目编码": "160101", "金额": 10000}}],
            "贷方": [{{"科目名称": "银行存款", "科目编码": "1002", "金额": 10000}}]
        }}
    }}
]

重要规则：
0. 所有内容请以JSON格式返回，不要有 `json:xxx` 这样的开头字样
1. 严格按照顺序生成：科目 -> 资产 -> 员工 -> 凭证
2. 确保借贷平衡
3. 日期格式为YYYY-MM-DD，如果用户只提供'日'，请自动补充当前年月
4. 金额保留两位小数
5. 凭证示例中的科目编码、资产编码、员工等信息需要引用已有或正在创建的信息
6. 科目编码的规则为 `xxxx xx xx`，其中前4位为一级科目，不需要创建，后面每2位为次一级科目，可以根据需要进行创建
7. 科目卡片请务必返回所有必填字段（科目编码、科目名称、类别、方向、备注），如缺失请自动补全或建议用户填写
8. 资产卡片请务必返回所有必填字段（资产编码、资产名称、类别、原值、净值、购置日期、使用年限、状态、备注），如缺失请自动补全或建议用户填写
9. 员工卡片请务必返回所有必填字段（工号、姓名、部门编码、电话、状态、备注），如缺失请自动补全或建议用户填写
10. 科目卡片生成时，务必保证科目编码长度与名称层级严格一致（如1601-固定资产，160101-固定资产-电子设备，16010101-固定资产-电子设备-电脑）
11. 如需新建三级及以上科目，必须同时返回所有缺失的上级科目
12. 系统中已经提供的科目、资产、员工信息请不要重复创建
13. 如果你需要系统中的数据（如科目表、资产表、员工表、供应商表等），请在返回的JSON中增加"action"字段，值为"fetch_xxx"，如"fetch_subjects"、"fetch_assets"等，并在"analysis"字段中说明原因。
14. 如果你需要前端创建卡片，请在"action"字段中返回"create_card"，并在"cards"字段中给出卡片内容。
15. 如果对话已完成，请在JSON中增加"is_finished": true，否则为false。
16. 如果信息不全或有歧义，action 字段请返回 "ask_user"，并在 answer 字段中用简洁明了的中文向用户追问所需信息。信息齐全时，直接生成卡片，无需用户确认。 