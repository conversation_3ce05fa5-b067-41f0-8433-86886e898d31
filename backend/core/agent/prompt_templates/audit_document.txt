请对以下单据进行专业审核：

用户输入：{user_input}

相关规章制度：
{rag_results}

当前日期：{current_date}

请根据上述内容和相关规章制度，对单据进行全面审核。

## 审核流程
1. 首先进行详细的审核分析，说明审核过程和发现的问题
2. 然后使用特殊标记格式返回结构化信息

## 审核分析要点
请详细分析单据的以下方面：
1. **单据完整性检查**：检查单据是否包含必要的信息和要素
2. **合规性审查**：对照相关规章制度，检查单据是否符合要求
3. **准确性验证**：检查单据中的数据、日期、金额等是否准确合理
4. **风险评估**：识别可能存在的风险点和问题

## 返回格式要求
在完成审核分析后，你必须严格按照以下格式返回结构化信息：

```
<|audit_conclusion|>审核结论的详细说明<|/audit_conclusion|>
<|action|>audit_complete或audit_rejected或request_more_info<|/action|>
<|needs_more_info|>true或false<|/needs_more_info|>
<|required_info|>需要补充的信息1,需要补充的信息2<|/required_info|>
<|is_finished|>true或false<|/is_finished|>
```

## Action类型说明：
- "audit_complete": 单据审核通过，符合所有要求
- "audit_rejected": 单据审核不通过，存在严重问题
- "request_more_info": 需要用户提供更多信息才能完成审核

## 重要提醒：
1. 请先用自然语言进行详细的审核分析说明
2. 然后必须以上述标记格式结束回复
3. 所有标记字段都必须包含，不能省略
4. required_info字段在不需要补充信息时应为空
5. 标记格式必须严格正确，包括开始和结束标记
6. 每个标记必须在单独的行上
7. 标记内容不要包含换行符