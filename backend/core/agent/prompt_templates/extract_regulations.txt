你是一个专业的财务规章制度解析专家，擅长从文档中提取和分类各种报销规定。

请仔细分析以下规章制度文档内容，提取其中的报销规定，并按照报销类型进行分类整理。

文档内容：
---
{document_content}
---

请按照以下JSON格式返回解析结果：
```json
{{
  "regulation_categories": [
    {{
      "type": "差旅费",
      "description": "与员工出差相关的费用报销规定",
      "rules": [
        "交通标准：飞机经济舱、高铁二等座、普通列车硬卧",
        "住宿标准：一线城市不超过600元/天，二线城市不超过500元/天，三线城市不超过400元/天",
        "伙食补贴：100元/天",
        "交通补贴：50元/天"
      ]
    }},
    {{
      "type": "办公用品",
      "description": "办公用品采购和报销相关规定",
      "rules": [
        "单价超过1000元的需部门经理审批",
        "超过5000元的需分管副总审批",
        "采购后需在3个工作日内完成报销",
        "需提供正规发票和采购清单"
      ]
    }},
    {{
      "type": "业务招待费",
      "description": "业务招待相关费用报销规定",
      "rules": [
        "需提前申请，注明招待对象、人数、预算标准",
        "一般客户招待标准不超过200元/人",
        "重要客户不超过500元/人",
        "需提供招待对象名单、消费清单和发票"
      ]
    }}
  ]
}}
```

要求：
1. 仔细阅读文档内容，识别所有报销类型
2. 为每种类型创建一个分类对象
3. 提取该类型下的具体规定作为rules数组
4. 确保rules数组中的每条规则都是清晰、完整的句子
5. 如果文档中没有提到某种类型，请勿创建该类型
6. 确保返回的是有效的JSON格式
7. 不要在JSON格式前后添加任何其他文本或解释