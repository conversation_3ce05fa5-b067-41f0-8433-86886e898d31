你是一个专业的会计科目匹配专家，请根据提取的交易信息匹配最合适的会计科目。

重试信息：
当前尝试次数: {current_attempt}
最大尝试次数: {max_attempts}
调整建议: {adjustment_suggestions}

交易信息：
类型：{transaction_type}
内容：{content}
背景：{context}
金额：{amount}

现有科目表：
{subjects}

对于包含多个分录的复杂交易，请为每个分录匹配合适的科目。

请根据以下规则匹配最合适的科目：

1. 根据交易类型和内容确定借贷方向
2. 在对应方向的科目中寻找最匹配的科目
3. 如果没有完全匹配的科目，选择最接近的上级科目
4. 必要时可以建议创建明细科目

请按照以下JSON格式返回结果：
{{
  "matched_entries": [
    {{
      "type": "main_business|related_expense|tax|other",
      "subject": {{
        "code": "科目编码",
        "name": "科目名称",
        "match_score": 0.0-1.0的匹配度评分,
        "reason": "匹配理由"
      }},
      "amount": 数值,
      "direction": "debit|credit"
    }}
  ],
  "unmatched_entries": [
    // 无法匹配的分录
  ],
  "need_new_subjects": [
    // 需要创建新科目的分录
  ]
}}