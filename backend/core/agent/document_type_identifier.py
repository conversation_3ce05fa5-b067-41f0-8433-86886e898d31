"""
单据类型识别器 - 识别单据类型并提取相关信息
"""

import logging
import json
from typing import Dict, Any, Optional, List

from core.logging_config import log_ai_flow, log_audit_flow

logger = logging.getLogger(__name__)

class DocumentTypeIdentifier:
    """单据类型识别器"""
    
    def __init__(self, llm_manager):
        """初始化单据类型识别器"""
        logger.info("[IDENTIFIER] 初始化单据类型识别器")
        self.llm_manager = llm_manager
    
    async def identify_document_type(self, document_content: str) -> Dict[str, Any]:
        """识别单据类型"""
        logger.info("[IDENTIFIER] 开始识别单据类型")
        log_audit_flow("识别单据类型", document_content=document_content)
        
        try:
            if not self.llm_manager.is_configured():
                logger.error("[IDENTIFIER] LLM未配置")
                return {"success": False, "error": "LLM未配置"}
            
            # 构建识别提示
            logger.debug("[IDENTIFIER] 构建识别提示")
            identify_prompt = self._build_identify_prompt(document_content)
            logger.debug(f"[IDENTIFIER] 识别提示长度: {len(identify_prompt)} 字符")
            
            # 调用LLM进行识别
            logger.info("[IDENTIFIER] 调用LLM进行识别")
            log_ai_flow("LLM识别单据类型", input_data={"content_length": len(document_content), "prompt_length": len(identify_prompt)})
            response = await self.llm_manager.generate(identify_prompt)
            logger.debug(f"[IDENTIFIER] LLM响应长度: {len(response)} 字符")
            log_ai_flow("LLM识别单据类型", output_data={"response_length": len(response)})
            
            # 解析响应
            logger.debug("[IDENTIFIER] 解析LLM响应")
            try:
                # 尝试从响应中提取JSON
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end]
                    logger.debug(f"[IDENTIFIER] 提取的JSON字符串长度: {len(json_str)} 字符")
                    result = json.loads(json_str)
                    logger.info("[IDENTIFIER] 成功解析单据类型")
                    log_audit_flow("识别单据类型", audit_result=result)
                    
                    return {
                        "success": True,
                        "data": result
                    }
                else:
                    logger.error(f"[IDENTIFIER] 无法从响应中提取JSON: {response}")
                    return {"success": False, "error": "无法从响应中提取JSON"}
                    
            except json.JSONDecodeError as e:
                logger.error(f"[IDENTIFIER] 解析JSON失败: {str(e)}, 响应: {response}")
                return {"success": False, "error": f"解析JSON失败: {str(e)}"}
                
        except Exception as e:
            logger.error(f"[IDENTIFIER] 识别单据类型失败: {str(e)}", exc_info=True)
            log_audit_flow("识别单据类型", error=str(e))
            return {"success": False, "error": str(e)}
    
    def _build_identify_prompt(self, document_content: str) -> str:
        """构建单据类型识别提示"""
        logger.debug("[IDENTIFIER] 构建单据类型识别提示")
        
        # 读取prompt模板文件
        try:
            with open("backend/core/agent/prompt_templates/identify_document_type.txt", "r", encoding="utf-8") as f:
                prompt_template = f.read()
            
            # 使用模板替换文档内容
            result = prompt_template.format(document_content=document_content)
            logger.debug("[IDENTIFIER] 成功构建单据类型识别提示")
            return result
        except Exception as e:
            logger.error(f"[IDENTIFIER] 读取单据类型识别prompt模板失败: {str(e)}")
            # 如果读取模板失败，使用硬编码的prompt作为后备
            return f"""
请仔细分析以下单据内容，识别单据类型，并提取相关信息。

单据内容：
---
{document_content}
---

请按照以下JSON格式返回识别结果：
```json
{{
  "document_type": "差旅费报销单",
  "confidence": 0.95,
  "description": "员工出差产生的交通、住宿、餐饮等费用报销单据",
  "key_information": {{
    "employee_name": "张三",
    "department": "销售部",
    "trip_dates": "2023-10-01至2023-10-05",
    "destination": "北京",
    "total_amount": 2500.00
  }},
  "relevant_regulation_categories": ["差旅费"]
}}
```

要求：
1. 仔细阅读单据内容，识别单据类型
2. 常见的单据类型包括：差旅费报销单、办公用品采购单、业务招待费报销单、交通费报销单、餐饮费报销单等
3. 为识别结果提供置信度（0-1之间的数值）
4. 提取单据中的关键信息，如员工姓名、部门、日期、金额等
5. 根据单据类型，确定相关的规章制度分类
6. 确保返回的是有效的JSON格式
7. 不要在JSON格式前后添加任何其他文本或解释
"""
    
    async def get_relevant_regulations(self, document_type: str, regulation_categories: List[str]) -> Dict[str, Any]:
        """获取相关的规章制度"""
        logger.info(f"[IDENTIFIER] 获取相关规章制度，单据类型: {document_type}, 分类: {regulation_categories}")
        
        try:
            from core.rag import get_rag_manager
            rag_manager = get_rag_manager()
            
            # 收集所有相关的规章制度
            relevant_regulations = []
            
            # 按分类搜索规章制度
            for category in regulation_categories:
                logger.debug(f"[IDENTIFIER] 按分类搜索规章制度: {category}")
                search_result = rag_manager.search_by_category(category, n_results=3)
                if search_result["success"]:
                    category_regulations = search_result["data"]
                    logger.info(f"[IDENTIFIER] 找到 {len(category_regulations)} 条 '{category}' 分类的规章制度")
                    relevant_regulations.extend(category_regulations)
                else:
                    logger.warning(f"[IDENTIFIER] 搜索 '{category}' 分类的规章制度失败: {search_result['error']}")
            
            # 如果没有找到相关分类的规章制度，尝试按单据类型搜索
            if not relevant_regulations:
                logger.debug(f"[IDENTIFIER] 未找到分类相关的规章制度，尝试按单据类型搜索: {document_type}")
                search_result = rag_manager.search_documents(document_type, n_results=5)
                if search_result["success"]:
                    relevant_regulations = search_result["data"]
                    logger.info(f"[IDENTIFIER] 按单据类型找到 {len(relevant_regulations)} 条规章制度")
                else:
                    logger.warning(f"[IDENTIFIER] 按单据类型搜索规章制度失败: {search_result['error']}")
            
            logger.info(f"[IDENTIFIER] 共获取到 {len(relevant_regulations)} 条相关规章制度")
            
            return {
                "success": True,
                "data": {
                    "document_type": document_type,
                    "regulation_categories": regulation_categories,
                    "relevant_regulations": relevant_regulations
                }
            }
            
        except Exception as e:
            logger.error(f"[IDENTIFIER] 获取相关规章制度失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}


# 全局单据类型识别器实例
_document_type_identifier = None

def get_document_type_identifier(llm_manager) -> DocumentTypeIdentifier:
    """获取全局单据类型识别器实例"""
    global _document_type_identifier
    if _document_type_identifier is None:
        logger.info("[IDENTIFIER] 创建全局单据类型识别器实例")
        _document_type_identifier = DocumentTypeIdentifier(llm_manager)
    return _document_type_identifier