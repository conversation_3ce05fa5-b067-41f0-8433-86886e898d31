"""
LLM管理器 - 统一管理不同的语言模型
"""

import logging
import asyncio
from typing import Optional, Dict, Any, AsyncGenerator
from langchain_core.language_models.llms import BaseLLM
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from pydantic import Field
import aiohttp
import json
from core.config import config
from core.llm_retry import get_retry_handler, LLMRetryConfig

logger = logging.getLogger(__name__)


class CustomChatModel(BaseChatModel):
    """自定义聊天模型，兼容OpenAI API格式"""

    api_key: str = Field(...)
    base_url: str = Field(...)
    model: str = Field(...)
    temperature: float = Field(default=config.LLM_TEMPERATURE)
    top_p: float = Field(default=config.LLM_TOP_P)
    use_ollama: bool = Field(default=False)

    def __init__(self, api_key: str, base_url: str, model: str, use_ollama: bool = False, **kwargs):
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            model=model,
            temperature=kwargs.get('temperature', 0.2),
            top_p=kwargs.get('top_p', 0.8),
            use_ollama=use_ollama,
            **kwargs
        )
    
    @property
    def _llm_type(self) -> str:
        return "custom_chat"
    
    def _generate(self, messages, stop=None, run_manager=None, **kwargs):
        # 同步版本，暂时抛出异常，强制使用异步版本
        raise NotImplementedError("请使用异步版本 _agenerate")
    
    async def _agenerate(self, messages, stop=None, run_manager=None, **kwargs):
        """异步生成响应"""
        retry_handler = get_retry_handler()
        
        async def _make_request():
            # 转换消息格式
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            headers = {
                "Content-Type": "application/json"
            }
            
            # Ollama不需要Authorization header
            if not self.use_ollama:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            data = {
                "model": self.model,
                "messages": formatted_messages,
                "stream": False
            }
            
            # Ollama使用不同的参数格式
            if self.use_ollama:
                data["options"] = {
                    "temperature": self.temperature,
                    "top_p": self.top_p
                }
            else:
                data["temperature"] = self.temperature
                data["top_p"] = self.top_p
                data["enable_thinking"] = False
            
            # 记录发送给AI服务器的消息内容
            logger.info(f"[LLM_REQUEST] 发送给AI服务器的消息: {json.dumps(data, ensure_ascii=False)}")
            
            async with aiohttp.ClientSession() as session:
                # Ollama使用不同的API端点
                if self.use_ollama:
                    api_url = f"{self.base_url}/api/chat"
                else:
                    api_url = f"{self.base_url}/chat/completions"
                
                async with session.post(
                    api_url,
                    headers=headers,
                    json=data
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API请求失败: {response.status}, {error_text}")
                    
                    result = await response.json()
                    
                    if self.use_ollama:
                        # Ollama的/api/chat端点响应格式
                        content = result.get("message", {}).get("content", "")
                    else:
                        # OpenAI兼容格式的响应
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    
                    # 记录AI服务器返回的消息内容
                    logger.info(f"[LLM_RESPONSE] AI服务器返回的消息: {json.dumps({'content': content, 'full_response': result}, ensure_ascii=False)}")
                    
                    from langchain_core.outputs import ChatGeneration, ChatResult
                    message = AIMessage(content=content)
                    generation = ChatGeneration(message=message)
                    return ChatResult(generations=[generation])
        
        try:
            return await retry_handler.execute_with_retry(_make_request)
        except Exception as e:
            logger.error(f"LLM生成失败（重试后仍然失败）: {str(e)}")
            raise
    
    async def astream(self, messages, stop=None, run_manager=None, **kwargs) -> AsyncGenerator[str, None]:
        print('DEBUG astream called')
        retry_handler = get_retry_handler()
        
        async def _make_stream_request():
            # 转换消息格式
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            headers = {
                "Content-Type": "application/json"
            }
            
            # Ollama不需要Authorization header
            if not self.use_ollama:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            data = {
                "model": self.model,
                "messages": formatted_messages,
                "stream": True
            }
            
            # Ollama使用不同的参数格式
            if self.use_ollama:
                data["options"] = {
                    "temperature": self.temperature,
                    "top_p": self.top_p
                }
            else:
                data["temperature"] = self.temperature
                data["top_p"] = self.top_p
                data["enable_thinking"] = True
            
            # 记录发送给AI服务器的消息内容
            logger.info(f"[LLM_STREAM_REQUEST] 发送给AI服务器的流式消息: {json.dumps(data, ensure_ascii=False)}")
            
            async with aiohttp.ClientSession() as session:
                # Ollama使用不同的API端点
                if self.use_ollama:
                    api_url = f"{self.base_url}/api/chat"
                else:
                    api_url = f"{self.base_url}/chat/completions"
                
                async with session.post(
                    api_url,
                    headers=headers,
                    json=data
                ) as response:
                    print('DEBUG astream response status:', response.status)
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API请求失败: {response.status}, {error_text}")
                    
                    buffer = ""
                    print('DEBUG 开始处理Ollama流式响应')
                    async for chunk in response.content.iter_chunked(1024):
                        chunk_str = chunk.decode('utf-8')
                        buffer += chunk_str
                        print(f'DEBUG 收到chunk，buffer长度: {len(buffer)}, chunk内容: {chunk_str}')
                        print(f'DEBUG 当前buffer内容: {buffer}')
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.strip()
                            print(f'DEBUG 处理line: {line}')
                            if self.use_ollama:
                                # Ollama的/api/chat端点流式响应格式不同
                                try:
                                    data = json.loads(line)
                                    print(f'DEBUG 解析Ollama响应成功: {json.dumps(data, ensure_ascii=False)}')
                                    # /api/chat端点的响应格式不同，内容在message.content中
                                    content = data.get('message', {}).get('content', None)
                                    print(f'DEBUG 提取的content: {content}')
                                    if content is not None:
                                        print(f'DEBUG Ollama返回内容: {content}')
                                        yield content
                                    # 检查是否完成
                                    if data.get('done', False):
                                        print('DEBUG Ollama流式响应完成')
                                        return
                                except json.JSONDecodeError as e:
                                    print(f'DEBUG 解析Ollama响应失败: {e}, 行内容: {line}')
                                    continue
                            else:
                                # OpenAI兼容格式的响应，使用data:前缀
                                if line.startswith('data:'):
                                    payload = line.lstrip('data:').strip()
                                    if payload == '[DONE]':
                                        return
                                    try:
                                        data = json.loads(payload)
                                        delta = data.get('choices', [{}])[0].get('delta', {})
                                        content = delta.get('content', None)
                                        reasoning_content = delta.get('reasoning_content', None)
                                        
                                        # 如果有推理内容，先输出推理内容
                                        if reasoning_content is not None:
                                            # 记录AI服务器返回的推理内容
                                            logger.info(f"[LLM_STREAM_RESPONSE] AI服务器返回的推理内容: {reasoning_content}")
                                            yield reasoning_content
                                        
                                        if content is not None:
                                            # 记录AI服务器返回的流式消息内容
                                            logger.info(f"[LLM_STREAM_RESPONSE] AI服务器返回的流式消息内容: {content}")
                                            yield content
                                    except json.JSONDecodeError as e:
                                        print('DEBUG astream OpenAI JSONDecodeError:', e)
                                        continue
        
        try:
            # 对于流式请求，我们需要在重试时重新开始整个流
            # 这里我们使用一个标志来跟踪是否已经成功开始流式传输
            stream_started = False
            
            for attempt in range(retry_handler.config.max_retries + 1):
                try:
                    async for content in _make_stream_request():
                        stream_started = True
                        yield content
                    
                    # 如果成功完成流式传输，跳出重试循环
                    if stream_started:
                        break
                        
                except Exception as e:
                    if attempt == retry_handler.config.max_retries:
                        # 最后一次尝试失败，抛出异常
                        print('DEBUG astream exception:', e)
                        logger.error(f"流式生成失败（重试后仍然失败）: {str(e)}")
                        raise
                    
                    # 检查是否应该重试
                    should_retry, error_type, delay = retry_handler._should_retry(e, attempt)
                    if should_retry:
                        logger.info(f"流式请求等待 {delay:.2f} 秒后进行第 {attempt + 2} 次重试...")
                        await asyncio.sleep(delay)
                    else:
                        # 不可重试的错误，立即抛出
                        print('DEBUG astream exception:', e)
                        logger.error(f"流式生成失败: {str(e)}")
                        raise
            
        except Exception as e:
            print('DEBUG astream exception:', e)
            logger.error(f"流式生成失败: {str(e)}")
            raise


class LLMManager:
    """LLM管理器"""
    
    def __init__(self):
        self._llm = None
        self._config = {}
    
    def configure(self, api_key: str, base_url: str, model: str, use_ollama: bool = False, **kwargs):
        """配置LLM"""
        self._config = {
            'api_key': api_key,
            'base_url': base_url,
            'model': model,
            'use_ollama': use_ollama,
            **kwargs
        }
        
        # 如果启用Ollama，使用前端传递的Ollama配置
        if use_ollama:
            # 使用前端传递的配置，而不是从配置文件中读取
            # 这样用户可以在前端设置中自定义Ollama模型
            api_key = "ollama"  # Ollama不需要API key
        
        # 创建自定义聊天模型
        self._llm = CustomChatModel(
            api_key=api_key,
            base_url=base_url,
            model=model,
            use_ollama=use_ollama,  # 确保传递use_ollama参数
            **kwargs
        )
        
        logger.info(f"LLM配置成功: {model} ({'Ollama' if use_ollama else 'OpenAI兼容'})")
    
    def get_llm(self) -> Optional[BaseChatModel]:
        """获取LLM实例"""
        return self._llm
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return self._llm is not None
    
    async def generate(self, messages, **kwargs):
        """生成响应"""
        if not self.is_configured():
            raise Exception("LLM未配置")
        return await self._llm._agenerate(messages, **kwargs)
    
    async def stream(self, messages, **kwargs) -> AsyncGenerator[str, None]:
        print('DEBUG LLMManager.stream called')
        if not self.is_configured():
            print('DEBUG LLMManager.stream: not configured')
            raise Exception("LLM未配置")
        async for token in self._llm.astream(messages, **kwargs):
            yield token


# 全局LLM管理器实例
_llm_manager = None

def get_llm_manager() -> LLMManager:
    """获取全局LLM管理器实例"""
    global _llm_manager
    if _llm_manager is None:
        _llm_manager = LLMManager()
    return _llm_manager
