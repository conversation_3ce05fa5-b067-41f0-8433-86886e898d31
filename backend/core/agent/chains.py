"""
链式调用引擎 - 管理和执行各种链式调用
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Type, Union
from langchain_core.runnables import Runnable
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
import json
import asyncio
from datetime import datetime
import colorlog
import os
from .json_utils import SafeJsonOutputParser

logger = logging.getLogger(__name__)

# 从环境变量或默认值获取链颜色配置
CHAIN_COLORS = {
    'voucher_generation': 'purple',
    'document_analysis': 'blue',
    'conversation_summary': 'cyan',
    'transaction_analysis': 'yellow',
    'subject_matching': 'green',
    'amount_calculation': 'purple',
    'voucher_generation_improved': 'bold_purple',
    'balance_verification': 'bold_blue'
}


class ChainResult:
    """链式调用结果"""
    
    def __init__(self, success: bool, data: Any = None, error: Optional[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "timestamp": self.timestamp.isoformat()
        }


class BaseChain:
    """基础链式调用"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.chain = None
        
        # 为每个链创建自定义logger
        self.logger = colorlog.getLogger(f"chain.{self.name}")
        
        # 设置链特定的颜色
        if self.name in CHAIN_COLORS:
            color = CHAIN_COLORS[self.name]
            formatter = colorlog.ColoredFormatter(
                f"%(log_color)s%(asctime)s - %(name)s - %(levelname)s - [CHAIN:{self.name}] %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
                log_colors={
                    'DEBUG':    'cyan',
                    'INFO':     color,
                    'WARNING': 'yellow',
                    'ERROR':    'red',
                    'CRITICAL': 'red,bg_white',
                }
            )
            # 为这个logger创建专门的handler
            handler = colorlog.StreamHandler()
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(colorlog.getLogger().level)
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        raise NotImplementedError("子类必须实现 build 方法")
    
    async def run(self, inputs: Dict[str, Any]) -> ChainResult:
        """执行链式调用"""
        self.logger.info(f'-------------------------------- 执行链式调用 {self.__class__} --------------------------------')
        self.logger.info(f"[PROMPT] LLM实际输入: {inputs}")
        if not self.chain:
            return ChainResult(
                success=False,
                error="链式调用未构建"
            )
        # 尝试打印最终 prompt 内容（仅适用于 ChatPromptTemplate）
        try:
            # 仅对常见链类型处理
            if hasattr(self, 'chat_prompt') and self.chat_prompt:
                messages = self.chat_prompt.format_messages(**inputs)
                self.logger.info(f"[PROMPT] LLM实际输入: {messages}")
        except Exception as e:
            self.logger.warning(f"[PROMPT] 打印失败: {e}")
        try:
            result = await self.chain.ainvoke(inputs)
            return ChainResult(
                success=True,
                data=result
            )
        except Exception as e:
            self.logger.error(f"链式调用 {self.name} 执行失败: {str(e)}")
            return ChainResult(
                success=False,
                error=str(e)
            )
    
    async def stream(self, inputs: Dict[str, Any]):
        """流式执行链式调用"""
        if not self.chain:
            yield ChainResult(
                success=False,
                error="链式调用未构建"
            )
            return
        
        try:
            async for chunk in self.chain.astream(inputs):
                yield chunk
        except Exception as e:
            self.logger.error(f"链式调用 {self.name} 流式执行失败: {str(e)}")
            yield ChainResult(
                success=False,
                error=str(e)
            )


class VoucherGenerationChain(BaseChain):
    """凭证生成链"""
    
    def __init__(self):
        super().__init__(
            name="voucher_generation",
            description="生成会计凭证"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        voucher_prompt = prompt_manager.get_template("voucher_generation")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", voucher_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"user_input": lambda x: x["user_input"],
             "subjects": lambda x: json.dumps(x.get("subjects", []), ensure_ascii=False),
             "assets": lambda x: json.dumps(x.get("assets", []), ensure_ascii=False),
             "staff": lambda x: json.dumps(x.get("staff", []), ensure_ascii=False),
             "experience": lambda x: json.dumps(x.get("experience", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class DocumentAnalysisChain(BaseChain):
    """文档分析链"""
    
    def __init__(self):
        super().__init__(
            name="document_analysis",
            description="分析文档内容"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        document_prompt = prompt_manager.get_template("document_analysis")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", document_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"document_type": lambda x: x["document_type"],
             "document_content": lambda x: x["document_content"],
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | StrOutputParser()
        )


class ConversationSummaryChain(BaseChain):
    """对话总结链"""
    
    def __init__(self):
        super().__init__(
            name="conversation_summary",
            description="总结对话内容"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        summary_prompt = prompt_manager.get_template("conversation_summary")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", summary_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"conversation_history": lambda x: x["conversation_history"],
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | StrOutputParser()
        )


class TransactionAnalysisChain(BaseChain):
    """交易分析链"""
    
    def __init__(self):
        super().__init__(
            name="transaction_analysis",
            description="分析交易信息"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        analysis_prompt = prompt_manager.get_template("transaction_analysis")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", analysis_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"user_input": lambda x: x["user_input"],
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous"),
             "company_name": lambda x: x.get("company_name", ""),
             "business_scope": lambda x: x.get("business_scope", ""),
             "industry": lambda x: x.get("industry", ""),
             "current_attempt": lambda x: x.get("current_attempt", 1),
             "max_attempts": lambda x: x.get("max_attempts", 3),
             "adjustment_suggestions": lambda x: x.get("adjustment_suggestions", "")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class SubjectMatchingChain(BaseChain):
    """科目匹配链"""
    
    def __init__(self):
        super().__init__(
            name="subject_matching",
            description="匹配会计科目"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        matching_prompt = prompt_manager.get_template("subject_matching")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", matching_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"transaction_type": lambda x: x["transaction_type"],
             "content": lambda x: x["content"],
             "context": lambda x: x["context"],
             "amount": lambda x: x["amount"],
             "subjects": lambda x: json.dumps(x.get("subjects", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous"),
             "company_name": lambda x: x.get("company_name", ""),
             "business_scope": lambda x: x.get("business_scope", ""),
             "industry": lambda x: x.get("industry", ""),
             "accounting_standards": lambda x: x.get("accounting_standards", ""),
             "current_attempt": lambda x: x.get("current_attempt", 1),
             "max_attempts": lambda x: x.get("max_attempts", 3),
             "adjustment_suggestions": lambda x: x.get("adjustment_suggestions", "")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class AmountCalculationChain(BaseChain):
    """金额计算链"""
    
    def __init__(self):
        super().__init__(
            name="amount_calculation",
            description="计算会计金额"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        calculation_prompt = prompt_manager.get_template("amount_calculation")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", calculation_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"transaction_type": lambda x: x["transaction_type"],
             "content": lambda x: x["content"],
             "context": lambda x: x["context"],
             "primary_amount": lambda x: x["primary_amount"],
             "secondary_amounts": lambda x: json.dumps(x.get("secondary_amounts", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous"),
             "company_name": lambda x: x.get("company_name", ""),
             "industry": lambda x: x.get("industry", ""),
             "tax_info": lambda x: x.get("tax_info", ""),
             "current_attempt": lambda x: x.get("current_attempt", 1),
             "max_attempts": lambda x: x.get("max_attempts", 3),
             "adjustment_suggestions": lambda x: x.get("adjustment_suggestions", "")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class VoucherGenerationImprovedChain(BaseChain):
    """改进的凭证生成链"""
    
    def __init__(self):
        super().__init__(
            name="voucher_generation_improved",
            description="生成改进的会计凭证"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        voucher_prompt = prompt_manager.get_template("voucher_generation_improved")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", voucher_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"date": lambda x: x["date"],
             "summary": lambda x: x["summary"],
             "debit_subjects": lambda x: json.dumps(x.get("debit_subjects", []), ensure_ascii=False),
             "credit_subjects": lambda x: json.dumps(x.get("credit_subjects", []), ensure_ascii=False),
             "amount_breakdown": lambda x: json.dumps(x.get("amount_breakdown", {}), ensure_ascii=False),
             "tax_details": lambda x: json.dumps(x.get("tax_details", {}), ensure_ascii=False),
             "other_fees": lambda x: json.dumps(x.get("other_fees", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous"),
             "retry_count": lambda x: x.get("retry_count", 0),
             "adjustment_suggestions": lambda x: x.get("adjustment_suggestions", ""),
             "previous_attempts": lambda x: x.get("previous_attempts", "[]")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class BalanceVerificationChain(BaseChain):
    """平衡验证链"""
    
    def __init__(self):
        super().__init__(
            name="balance_verification",
            description="验证会计分录平衡"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        verification_prompt = prompt_manager.get_template("balance_verification")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", verification_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"debit_entries": lambda x: json.dumps(x.get("debit_entries", []), ensure_ascii=False),
             "credit_entries": lambda x: json.dumps(x.get("credit_entries", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | SafeJsonOutputParser()
        )


class ChainManager:
    """链式调用管理器"""
    
    def __init__(self):
        self._chains: Dict[str, BaseChain] = {}
        self._llm = None
    
    def set_llm(self, llm: BaseChatModel) -> None:
        """设置语言模型"""
        self._llm = llm
        # 重新构建所有链
        for chain in self._chains.values():
            chain.build(llm)
    
    def register_chain(self, chain: BaseChain) -> None:
        """注册链式调用"""
        self._chains[chain.name] = chain
        if self._llm:
            chain.build(self._llm)
        logger.info(f"注册链式调用: {chain.name}")
    
    def get_chain(self, name: str) -> Optional[BaseChain]:
        """获取链式调用"""
        return self._chains.get(name)
    
    def list_chains(self) -> List[str]:
        """列出所有链式调用名称"""
        return list(self._chains.keys())
    
    def remove_chain(self, name: str) -> bool:
        """移除链式调用"""
        if name in self._chains:
            del self._chains[name]
            logger.info(f"移除链式调用: {name}")
            return True
        return False
    
    async def run_chain(self, name: str, inputs: Dict[str, Any]) -> ChainResult:
        """执行链式调用"""
        chain = self.get_chain(name)
        if not chain:
            return ChainResult(
                success=False,
                error=f"链式调用不存在: {name}"
            )
        
        if not self._llm:
            return ChainResult(
                success=False,
                error="语言模型未设置"
            )
        
        return await chain.run(inputs)
    
    async def stream_chain(self, name: str, inputs: Dict[str, Any]):
        """流式执行链式调用"""
        chain = self.get_chain(name)
        if not chain:
            yield ChainResult(
                success=False,
                error=f"链式调用不存在: {name}"
            )
            return
        
        if not self._llm:
            yield ChainResult(
                success=False,
                error="语言模型未设置"
            )
            return
        
        async for chunk in chain.stream(inputs):
            yield chunk
    
    def initialize_default_chains(self) -> None:
        """初始化默认链式调用"""
        default_chains = [
            VoucherGenerationChain(),
            DocumentAnalysisChain(),
            ConversationSummaryChain(),
            TransactionAnalysisChain(),
            SubjectMatchingChain(),
            AmountCalculationChain(),
            VoucherGenerationImprovedChain(),
            BalanceVerificationChain()
        ]
        
        for chain in default_chains:
            self.register_chain(chain)


# 全局链式调用管理器实例
_chain_manager = None

def get_chain_manager() -> ChainManager:
    """获取全局链式调用管理器实例"""
    global _chain_manager
    if _chain_manager is None:
        _chain_manager = ChainManager()
        _chain_manager.initialize_default_chains()
    return _chain_manager
