from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Body
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, AsyncGenerator
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
import os
import json
from datetime import datetime
from core.db import AsyncSessionLocal, engine
from sqlalchemy.exc import IntegrityError
from models.subject import SubjectAccount
from crud.subject import (
    get_subjects, get_subjects_count, add_subject, update_subject, delete_subject, get_subject
)
from sqlalchemy.ext.asyncio import AsyncSession
from api.subject import SUBJECT_TEMPLATES
from models.subject import SubjectAccount as ORMSubjectAccount
from sqlalchemy import text

router = APIRouter()

# Pydantic模型用于请求体校验
class SubjectAccountIn(BaseModel):
    code: str
    name: str
    level: int
    parent_code: Optional[str] = None
    category: str
    direction: str
    aux: Optional[str] = None
    is_leaf: bool = True
    status: str = "启用"
    remark: Optional[str] = ""
    quantity: bool = False

class TemplateKey(BaseModel):
    key: str

# 获取数据库Session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal(bind=engine) as db:
        yield db

@router.get("/subjects/tree")
async def get_subject_tree(db=Depends(get_db)):
    subjects = await get_subjects(db, skip=0, limit=10000)
    # 构建树结构
    code_map = {s.code: s for s in subjects}
    tree = []
    for s in subjects:
        node = s.__dict__.copy()
        node["children"] = []
        code_map[s.code] = node
    for s in subjects:
        if s.parent_code and s.parent_code in code_map:
            code_map[s.parent_code]["children"].append(code_map[s.code])
        else:
            tree.append(code_map[s.code])
    return {"tree": tree}

@router.get("/subjects")
async def get_subjects_api(page: int = 1, size: int = 50, keyword: Optional[str] = None, db=Depends(get_db)):
    skip = (page - 1) * size
    subjects = await get_subjects(db, skip=skip, limit=size, keyword=keyword)
    total = await get_subjects_count(db, keyword=keyword)
    return {"total": total, "page": page, "size": size, "subjects": [s.__dict__ for s in subjects]}

@router.post("/subjects")
async def add_subject_api(subject: SubjectAccountIn, db=Depends(get_db)):
    obj = SubjectAccount(**subject.dict())
    try:
        await add_subject(db, obj)
        return {"success": True}
    except IntegrityError:
        raise HTTPException(status_code=400, detail="科目编码已存在")

@router.put("/subjects/{code}")
async def update_subject_api(code: str, subject: SubjectAccountIn, db=Depends(get_db)):
    await update_subject(db, code, subject.dict())
    return {"success": True}

@router.delete("/subjects/{code}")
async def delete_subject_api(code: str, db=Depends(get_db)):
    await delete_subject(db, code)
    return {"success": True}

@router.patch("/subjects/{code}/status")
async def patch_subject_status(code: str, status: str, db=Depends(get_db)):
    subj = await get_subject(db, code)
    if not subj:
        raise HTTPException(status_code=404, detail="科目未找到")
    await update_subject(db, code, {"status": status})
    return {"success": True}

@router.get("/subjects/templates")
async def get_subject_templates():
    return [{"key": t["key"], "name": t["name"], "desc": t["desc"]} for t in SUBJECT_TEMPLATES]

@router.post("/subjects/use-template")
async def use_subject_template(data: dict = Body(...)):
    key = data.get("key")
    template = next((t for t in SUBJECT_TEMPLATES if t["key"] == key), None)
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    # 清空表并批量插入
    async with AsyncSessionLocal(bind=engine) as db:
        await db.execute(text("DELETE FROM subject_accounts"))
        for s in template["subjects"]:
            obj = ORMSubjectAccount(
                code=s["科目编码"],
                name=s["科目名称"],
                level=s["级次"],
                parent_code=s["父级编码"],
                category=s["类别"],
                direction=s["方向"],
                aux=",".join(s["辅助核算"]) if s.get("辅助核算") else None,
                is_leaf=s["末级"],
                status=s["状态"],
                remark=s["备注"],
                quantity=s["数量核算"]
            )
            db.add(obj)
        await db.commit()
    return {"success": True, "count": len(template["subjects"])}

# 其余如导入/导出/模板等接口可后续补充异步实现 