"""
MCP (Model Context Protocol) 管理 API 路由
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import logging
from core.mcp import mcp_client, MCPServerConfig, MCPTransportType, mcp_integration

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/mcp", tags=["MCP"])

class MCPServerConfigRequest(BaseModel):
    """MCP 服务器配置请求模型"""
    name: str
    transport: str = "stdio"
    command: Optional[str] = None
    args: List[str] = []
    url: Optional[str] = None
    env: Dict[str, str] = {}
    disabled: bool = False
    auto_approve: List[str] = []
    timeout: int = 30
    retry_count: int = 3

class MCPToolCallRequest(BaseModel):
    """MCP 工具调用请求模型"""
    tool_name: str
    parameters: Dict[str, Any] = {}

@router.get("/config")
async def get_mcp_config():
    """获取 MCP 配置"""
    try:
        config = {}
        for name, server in mcp_client.servers.items():
            server_config = {
                "name": server.name,
                "transport": server.transport_type.value,
                "env": server.env,
                "disabled": server.disabled,
                "auto_approve": server.auto_approve,
                "timeout": server.timeout,
                "retry_count": server.retry_count
            }

            if server.transport_type == MCPTransportType.STDIO:
                server_config.update({
                    "command": server.command,
                    "args": server.args
                })
            else:
                server_config["url"] = server.url

            config[name] = server_config

        return {
            "success": True,
            "config": config
        }
    except Exception as e:
        logger.error(f"获取 MCP 配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config")
async def update_mcp_config(servers: Dict[str, MCPServerConfigRequest]):
    """更新 MCP 配置"""
    try:
        # 使用 MCPIntegration 来管理配置
        # 先清空现有配置并停止所有服务器
        await mcp_client.stop_all_servers()
        mcp_client.fastmcp_wrapper.servers = {}
        mcp_client.fastmcp_wrapper.server_status = {}

        # 添加新配置到客户端
        server_configs = {}
        for name, server_req in servers.items():
            transport_type = MCPTransportType(server_req.transport)

            server_config = MCPServerConfig(
                name=name,
                transport_type=transport_type,
                command=server_req.command,
                args=server_req.args,
                url=server_req.url,
                env=server_req.env,
                disabled=server_req.disabled,
                auto_approve=server_req.auto_approve,
                timeout=server_req.timeout,
                retry_count=server_req.retry_count
            )

            mcp_client.add_server(server_config)
            server_configs[name] = server_config

        # 使用 MCPIntegration 保存配置到文件
        mcp_integration.save_config(server_configs)

        # 启动未禁用的服务器
        await mcp_client.start_all_servers()

        return {
            "success": True,
            "message": "MCP 配置已更新并保存"
        }
    except Exception as e:
        logger.error(f"更新 MCP 配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_name}/start")
async def start_mcp_server(server_name: str, background_tasks: BackgroundTasks):
    """启动 MCP 服务器"""
    try:
        if server_name not in mcp_client.servers:
            raise HTTPException(status_code=404, detail=f"未找到服务器配置: {server_name}")
        
        success = await mcp_client.start_server(server_name)
        
        if success:
            return {
                "success": True,
                "message": f"服务器 {server_name} 启动成功"
            }
        else:
            status = mcp_client.get_server_status(server_name)
            raise HTTPException(
                status_code=400, 
                detail=f"服务器启动失败: {status.get('error_message', '未知错误')}"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动 MCP 服务器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/{server_name}/stop")
async def stop_mcp_server(server_name: str):
    """停止 MCP 服务器"""
    try:
        success = await mcp_client.stop_server(server_name)
        
        if success:
            return {
                "success": True,
                "message": f"服务器 {server_name} 已停止"
            }
        else:
            raise HTTPException(status_code=400, detail="停止服务器失败")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止 MCP 服务器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/start-all")
async def start_all_mcp_servers():
    """启动所有 MCP 服务器"""
    try:
        await mcp_client.start_all_servers()
        return {
            "success": True,
            "message": "所有服务器启动完成"
        }
    except Exception as e:
        logger.error(f"启动所有 MCP 服务器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/servers/stop-all")
async def stop_all_mcp_servers():
    """停止所有 MCP 服务器"""
    try:
        await mcp_client.stop_all_servers()
        return {
            "success": True,
            "message": "所有服务器已停止"
        }
    except Exception as e:
        logger.error(f"停止所有 MCP 服务器失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servers/status")
async def get_servers_status():
    """获取所有服务器状态"""
    try:
        status = mcp_client.get_server_status()
        return {
            "success": True,
            "servers": status
        }
    except Exception as e:
        logger.error(f"获取服务器状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servers/{server_name}/status")
async def get_server_status(server_name: str):
    """获取单个服务器状态"""
    try:
        status = mcp_client.get_server_status(server_name)
        return {
            "success": True,
            "server": status
        }
    except Exception as e:
        logger.error(f"获取服务器状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tools")
async def get_available_tools():
    """获取所有可用工具"""
    try:
        tools = mcp_client.get_available_tools()
        return {
            "success": True,
            "tools": tools
        }
    except Exception as e:
        logger.error(f"获取可用工具失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tools/call")
async def call_mcp_tool(request: MCPToolCallRequest):
    """调用 MCP 工具"""
    try:
        result = await mcp_client.call_tool(request.tool_name, request.parameters)
        return {
            "success": True,
            "result": result
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except RuntimeError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"调用 MCP 工具失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reload")
async def reload_mcp_config():
    """重新加载 MCP 配置"""
    try:
        # 停止所有服务器
        await mcp_client.stop_all_servers()

        # 重新启动服务器（配置已在内存中）
        await mcp_client.start_all_servers()

        return {
            "success": True,
            "message": "MCP 配置已重新加载"
        }
    except Exception as e:
        logger.error(f"重新加载 MCP 配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# MCP 初始化函数（将在主应用中调用）
async def initialize_mcp():
    """初始化 MCP 客户端"""
    try:
        logger.info("初始化 MCP 客户端...")
        # 新的 FastMCP 客户端不需要 load_config，配置通过 add_server 添加
        # 这里可以添加默认的服务器配置
        await mcp_client.start_all_servers()
        logger.info("MCP 客户端初始化完成")
    except Exception as e:
        logger.error(f"MCP 客户端初始化失败: {e}")

async def shutdown_mcp():
    """关闭 MCP 客户端"""
    try:
        logger.info("关闭 MCP 客户端...")
        await mcp_client.stop_all_servers()
        logger.info("MCP 客户端已关闭")
    except Exception as e:
        logger.error(f"MCP 客户端关闭失败: {e}")