"""
智能体路由 - 整合原有AI功能和新的智能体功能
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Request, Header
from fastapi.responses import StreamingResponse
from typing import Optional, AsyncGenerator, List, Dict, Any
import os
import uuid
import tempfile
import logging
import json
from pydantic import BaseModel

# 导入原有的AI客户端（向后兼容）
from core.ai_client import get_ai_client
from core.ocr_utils import get_ocr_processor
from core.pdf_utils import get_pdf_parser

# 导入新的智能体系统
from core.agent import get_agent_executor

# 导入 MCP 集成
from core.mcp import mcp_integration

router = APIRouter()
logger = logging.getLogger(__name__)

# 上传文件保存目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_DIR = os.path.abspath(os.path.join(BASE_DIR, "../uploaded_files"))
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 全局AI配置存储（内存存储，后续可改为数据库）
global_ai_config = {
    "version": "2.0",
    "services": [],
    "activeServiceId": None,
    "globalSettings": {}
}

def get_active_ai_service():
    """获取当前活动的AI服务配置"""
    if not global_ai_config["services"]:
        return None

    # 如果有指定的活动服务ID，使用它
    if global_ai_config["activeServiceId"]:
        for service in global_ai_config["services"]:
            if service["id"] == global_ai_config["activeServiceId"] and service.get("enabled", True):
                return service

    # 否则使用第一个启用的服务
    for service in global_ai_config["services"]:
        if service.get("enabled", True):
            return service

    return None

def get_ai_client_from_config():
    """根据当前配置获取AI客户端"""
    active_service = get_active_ai_service()
    if not active_service:
        # 回退到环境变量配置
        from core.config import config
        return get_ai_client(config.LLM_API_KEY, config.LLM_BASE_URL, config.LLM_MODEL, config.OLLAMA_ENABLED)

    # 根据服务类型构建配置
    if active_service["type"] == "ollama":
        api_key = ""
        base_url = active_service["config"].get("baseUrl", "http://localhost:11434")
        model = active_service["config"].get("model", "llama3.1:8b")
        use_ollama = True
    else:  # openai_compatible 或 custom
        api_key = active_service["config"].get("apiKey", "")
        base_url = active_service["config"].get("baseUrl", "")
        model = active_service["config"].get("model", "")
        use_ollama = False

    return get_ai_client(api_key, base_url, model, use_ollama)


class MessageRequest(BaseModel):
    """消息请求模型"""
    content: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    use_enhanced: bool = True  # 是否使用增强智能体
    files: Optional[List[Dict]] = None  # 文件信息列表


class DocumentRequest(BaseModel):
    """文档请求模型"""
    document_type: str
    document_content: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None


class ToolRequest(BaseModel):
    """工具请求模型"""
    tool_name: str
    parameters: Dict[str, Any]
    session_id: Optional[str] = None


# ============ 向后兼容的原有API ============

@router.post("/ai/test")
async def test_ai_connection(
    request: Request,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    x_use_ollama: Optional[str] = Header(None)
):
    """测试AI连接（原有API，向后兼容）"""
    try:
        # 尝试从请求体获取配置信息
        api_key = x_api_key
        base_url = x_base_url
        model = x_model
        use_ollama = (x_use_ollama == "true")

        try:
            body = await request.json()
            # 明确检查是否使用Ollama
            use_ollama = (x_use_ollama == "true") or body.get("use_ollama", False)
            
            if use_ollama:
                # 使用Ollama配置
                api_key = ""  # Ollama不需要API密钥
                base_url = body.get("ollama_base_url") or "http://localhost:11434"
                model = body.get("ollama_model") or "llama3.1:8b"
            else:
                # 使用远程AI服务配置
                api_key = api_key or body.get("api_key")
                base_url = base_url or body.get("base_url")
                model = model or body.get("model")
        except:
            use_ollama = False  # 如果没有请求体，默认不使用Ollama

        logger.info(f"测试AI连接: use_ollama={use_ollama}, base_url={base_url}, model={model}")
        
        ai_client = get_ai_client(api_key, base_url, model, use_ollama)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        result = await ai_client.analyze_receipt_content("你好，这是测试", None)
        if result.get("success"):
            return {"success": True, "message": "连接测试成功"}
        else:
            raise Exception(result.get("error", "AI测试失败"))
    except Exception as e:
        logger.error(f"测试AI连接时出错: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/message")
async def agent_message(
    request: Request,
    content: Optional[str] = None,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    """发送消息（原有API，向后兼容）"""
    try:
        use_ollama = False
        if not content:
            body = await request.json()
            content = body.get("content", "")
            use_ollama = body.get("use_ollama", False)
        
        ai_client = get_ai_client(x_api_key, x_base_url, x_model, use_ollama)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        if not content:
            raise Exception("消息内容不能为空")
        
        async def token_stream() -> AsyncGenerator[str, None]:
            queue = []
            def stream_callback(token):
                queue.append(token)
            await ai_client.analyze_receipt_content(content, None, stream_callback)
            while queue:
                yield queue.pop(0)
        
        return StreamingResponse(token_stream(), media_type="text/plain")
    except Exception as e:
        logger.error(f"发送消息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/upload")
async def agent_upload(
    file: UploadFile = File(...),
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    """上传文件（原有API，向后兼容）"""
    try:
        use_ollama = False
        try:
            body = await request.json()
            use_ollama = body.get("use_ollama", False)
        except:
            pass  # 如果没有请求体，默认不使用Ollama
        
        ai_client = get_ai_client(x_api_key, x_base_url, x_model, use_ollama)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        
        file_content = await file.read()
        filename = (file.filename or "").lower()
        file_ext = os.path.splitext(filename)[1]
        unique_name = f"{uuid.uuid4()}{file_ext}"
        save_path = os.path.join(UPLOAD_DIR, unique_name)
        
        with open(save_path, "wb") as f:
            f.write(file_content)
        
        if filename.endswith((".png", ".jpg", ".jpeg", ".bmp", ".tiff", ".tif", ".webp")):
            ocr_processor = get_ocr_processor()
            ocr_result = ocr_processor.extract_text(save_path)
            if not ocr_result.get("success"):
                raise Exception(ocr_result.get("error", "图片识别失败"))
            file_text = ocr_result["text"]
        elif filename.endswith(".pdf"):
            pdf_parser = get_pdf_parser()
            file_text = pdf_parser.extract_text(save_path)
        else:
            raise Exception("不支持的文件类型，请上传图片(PNG/JPG/BMP/TIFF/WEBP)或PDF文件")
        
        prompt = f"请根据以下文档内容生成会计凭证：\n{file_text}"
        
        async def token_stream() -> AsyncGenerator[str, None]:
            queue = []
            def stream_callback(token):
                queue.append(token)
            await ai_client.analyze_receipt_content(prompt, None, stream_callback)
            while queue:
                yield queue.pop(0)
        
        headers = {
            "X-File-Name": unique_name,
            "X-File-URL": f"/files/{unique_name}"
        }
        return StreamingResponse(token_stream(), media_type="text/plain", headers=headers)
    except Exception as e:
        logger.error(f"上传文件失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 新的增强智能体API ============

@router.post("/agent/v2/configure")
async def configure_enhanced_agent(
    request: Request,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    x_use_ollama: Optional[str] = Header(None),
    session_id: Optional[str] = None
):
    """配置增强智能体"""
    try:
        # 从请求体获取配置信息
        body = await request.json()
        api_key = x_api_key or body.get("api_key")
        base_url = x_base_url or body.get("base_url")
        model = x_model or body.get("model")
        session_id = session_id or body.get("session_id")
        use_ollama = (x_use_ollama == "true") or body.get("use_ollama", False)
        
        # 如果使用Ollama，API键可以为空
        if use_ollama:
            api_key = api_key or ""
        
        if not base_url or not model:
            raise HTTPException(status_code=400, detail="缺少必需的配置参数")
        
        # 获取智能体执行器
        agent = get_agent_executor(session_id)
        
        # 配置LLM
        success = agent.configure_llm(api_key, base_url, model, use_ollama=use_ollama)
        
        if not success:
            raise HTTPException(status_code=400, detail="配置失败")
        
        return {
            "success": True,
            "message": "增强智能体配置成功",
            "session_id": agent.session_id
        }
    except Exception as e:
        logger.error(f"配置增强智能体失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/test")
async def test_enhanced_agent_connection(
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    session_id: Optional[str] = None
):
    """测试增强智能体连接"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor(session_id)
        
        # 配置LLM
        if x_api_key and x_base_url and x_model:
            use_ollama = False
            try:
                body = await request.json()
                use_ollama = body.get("use_ollama", False)
            except:
                pass  # 如果没有请求体，默认不使用Ollama
            
            agent.configure_llm(x_api_key, x_base_url, x_model, use_ollama=use_ollama)
        
        # 测试消息
        result = await agent.process_message("你好，这是连接测试")
        
        if result.get("success"):
            return {"success": True, "message": "增强智能体连接测试成功"}
        else:
            raise Exception(result.get("error", "测试失败"))
    except Exception as e:
        logger.error(f"测试增强智能体连接失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/message")
async def send_enhanced_message(
    request: MessageRequest,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    """发送消息给增强智能体"""
    try:
        if request.use_enhanced:
            # 使用增强智能体
            agent = get_agent_executor(request.session_id)
            
            # 配置LLM（如果提供了配置信息）
            if x_api_key and x_base_url and x_model:
                use_ollama = getattr(request, 'use_ollama', False)
                agent.configure_llm(x_api_key, x_base_url, x_model, use_ollama=use_ollama)
            
            # 处理消息
            result = await agent.process_message(request.content, request.user_id)
            
            return {
                "success": result.get("success", False),
                "data": result.get("data"),
                "error": result.get("error"),
                "session_id": agent.session_id,
                "version": "v2"
            }
        else:
            # 回退到原有AI客户端
            ai_client = get_ai_client(x_api_key, x_base_url, x_model)
            if not ai_client.is_configured():
                raise Exception("AI服务器未配置")
            
            result = await ai_client.analyze_receipt_content(request.content, None)
            return {
                "success": result.get("success", False),
                "data": result.get("raw_response"),
                "error": result.get("error"),
                "version": "v1"
            }
    except Exception as e:
        logger.error(f"发送消息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/stream")
async def stream_enhanced_message(
    request: MessageRequest,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    x_use_ollama: Optional[str] = Header(None)
):
    print('DEBUG /agent/v2/stream called')
    try:
        if request.use_enhanced:
            print('>>> use_enhanced')
            # 使用增强智能体
            agent = get_agent_executor(request.session_id)
            
            # 配置LLM（优先使用请求头中的配置，否则使用全局配置）
            if x_base_url and x_model:
                use_ollama = (x_use_ollama == "true") or getattr(request, 'use_ollama', False)
                api_key = x_api_key or ""
                agent.configure_llm(api_key, x_base_url, x_model, use_ollama=use_ollama)
            else:
                # 使用全局配置
                ai_client = get_ai_client_from_config()
                if ai_client and ai_client.is_configured():
                    agent.configure_llm(ai_client.api_key, ai_client.base_url, ai_client.model, use_ollama=ai_client.use_ollama)
            
            async def token_stream() -> AsyncGenerator[str, None]:
                try:
                    async for token in agent.stream_message(request.content, request.user_id, request.files):
                        yield token
                except Exception as e:
                    # 如果流式生成失败，返回错误信息
                    error_response = json.dumps({
                        "success": False,
                        "error": str(e)
                    })
                    yield error_response
            
            return StreamingResponse(token_stream(), media_type="text/plain")
        else:
            print('>>> use original')
            # 回退到原有AI客户端
            if x_base_url and x_model:
                use_ollama = (x_use_ollama == "true") or getattr(request, 'use_ollama', False)
                ai_client = get_ai_client(x_api_key, x_base_url, x_model, use_ollama)
            else:
                # 使用全局配置
                ai_client = get_ai_client_from_config()

            if not ai_client.is_configured():
                raise Exception("AI服务器未配置")
            
            async def token_stream() -> AsyncGenerator[str, None]:
                queue = []
                def stream_callback(token):
                    queue.append(token)
                await ai_client.analyze_receipt_content(request.content, None, stream_callback)
                while queue:
                    yield queue.pop(0)
            
            return StreamingResponse(token_stream(), media_type="text/plain")
    except Exception as e:
        logger.error(f"流式发送消息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/upload")
async def upload_enhanced_document(
    file: UploadFile = File(...),
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    use_enhanced: bool = True
):
    """只保存文件，不做分析，返回文件url和本地路径"""
    try:
        # 保存文件
        file_content = await file.read()
        filename = (file.filename or "").lower()
        file_ext = os.path.splitext(filename)[1]
        unique_name = f"{uuid.uuid4()}{file_ext}"
        save_path = os.path.join(UPLOAD_DIR, unique_name)

        with open(save_path, "wb") as f:
            f.write(file_content)

        return {
            "success": True,
            "file_info": {
                "filename": unique_name,
                "original_name": file.filename,
                "type": file_ext.lower(),
                "url": f"/files/{unique_name}",
                "path": save_path,
            },
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/test-stream")
async def test_stream_response():
    """测试流式响应（用于调试）"""
    async def test_token_stream() -> AsyncGenerator[str, None]:
        test_message = "这是一个测试响应，用于验证流式功能是否正常工作。"
        for char in test_message:
            yield char
            # 模拟延迟
            import asyncio
            await asyncio.sleep(0.1)

    return StreamingResponse(test_token_stream(), media_type="text/plain")


@router.post("/agent/v2/upload/stream")
async def stream_upload_enhanced_document(
    file: UploadFile = File(...),
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    session_id: Optional[str] = None,
    user_id: Optional[str] = None
):
    """流式上传文档给增强智能体处理"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor(session_id)

        # 配置LLM（如果提供了配置信息）
        if x_api_key and x_base_url and x_model:
            use_ollama = False
            try:
                body = await request.json()
                use_ollama = body.get("use_ollama", False)
            except:
                pass  # 如果没有请求体，默认不使用Ollama
            
            agent.configure_llm(x_api_key, x_base_url, x_model, use_ollama=use_ollama)

        # 保存文件
        file_content = await file.read()
        filename = (file.filename or "").lower()
        file_ext = os.path.splitext(filename)[1]
        unique_name = f"{uuid.uuid4()}{file_ext}"
        save_path = os.path.join(UPLOAD_DIR, unique_name)

        with open(save_path, "wb") as f:
            f.write(file_content)

        # 处理文件内容
        document_content = ""
        document_type = ""

        if filename.endswith((".png", ".jpg", ".jpeg", ".bmp", ".tiff", ".tif", ".webp")):
            # OCR处理
            result = await agent.execute_tool("ocr_processor", file_path=save_path)
            if not result["success"]:
                raise Exception(result["error"])
            document_content = result["data"]["text"]
            document_type = "图片"
        elif filename.endswith(".pdf"):
            # PDF处理
            result = await agent.execute_tool("pdf_processor", file_path=save_path)
            if not result["success"]:
                raise Exception(result["error"])
            document_content = result["data"]["text"]
            document_type = "PDF文档"
        else:
            raise Exception("不支持的文件类型，请上传图片(PNG/JPG/BMP/TIFF/WEBP)或PDF文件")

        async def token_stream() -> AsyncGenerator[str, None]:
            # 首先发送文件信息
            file_info = {
                "type": "file_info",
                "data": {
                    "filename": unique_name,
                    "original_name": file.filename,
                    "document_type": document_type,
                    "url": f"/files/{unique_name}"
                }
            }
            yield f"data: {json.dumps(file_info)}\n\n"

            # 然后流式处理文档
            async for token in agent.stream_document(document_type, document_content, user_id):
                yield f"data: {token}\n\n"

        return StreamingResponse(token_stream(), media_type="text/event-stream")
    except Exception as e:
        logger.error(f"流式上传文档失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/tool")
async def execute_enhanced_tool(request: ToolRequest):
    """执行增强智能体工具"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor(request.session_id)

        # 执行工具
        result = await agent.execute_tool(request.tool_name, **request.parameters)

        return result
    except Exception as e:
        logger.error(f"执行工具失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/agent/v2/history/{session_id}")
async def get_enhanced_conversation_history(session_id: str):
    """获取增强智能体对话历史"""
    try:
        agent = get_agent_executor(session_id)
        history = agent.get_conversation_history()

        return {
            "success": True,
            "session_id": session_id,
            "history": history,
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"获取对话历史失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/agent/v2/history/{session_id}")
async def clear_enhanced_conversation_history(session_id: str):
    """清空增强智能体对话历史"""
    try:
        agent = get_agent_executor(session_id)
        agent.clear_history()

        return {
            "success": True,
            "message": "对话历史已清空",
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"清空对话历史失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/agent/v2/tools")
async def list_enhanced_tools():
    """列出所有可用的增强智能体工具"""
    try:
        from core.agent.tools import get_tool_registry

        tool_registry = get_tool_registry()
        tools = tool_registry.list_tools()

        tool_info = []
        for tool_name in tools:
            tool = tool_registry.get_tool(tool_name)
            if tool:
                tool_info.append({
                    "name": tool.name,
                    "description": tool.description,
                    "args_schema": tool.args_schema.schema() if tool.args_schema else None
                })

        return {
            "success": True,
            "tools": tool_info,
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"列出工具失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/agent/v2/performance")
async def get_enhanced_performance_metrics():
    """获取增强智能体性能监控指标"""
    try:
        from core.agent.monitoring import get_performance_monitor

        monitor = get_performance_monitor()
        report = monitor.get_performance_report()

        return {
            "success": True,
            "performance": report,
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/agent/v2/performance/alerts")
async def clear_enhanced_performance_alerts():
    """清空增强智能体性能警报"""
    try:
        from core.agent.monitoring import get_performance_monitor

        monitor = get_performance_monitor()
        monitor.clear_alerts()

        return {
            "success": True,
            "message": "性能警报已清空",
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"清空性能警报失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 单据审核模式API ============

@router.post("/agent/v2/audit/stream")
async def stream_audit_message(
    request: MessageRequest,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    x_use_ollama: Optional[str] = Header(None)
):
    """流式处理单据审核消息"""
    print('DEBUG /agent/v2/audit/stream called')
    try:
        # 使用增强智能体
        agent = get_agent_executor(request.session_id)
        
        # 配置LLM（如果提供了配置信息）
        if x_base_url and x_model:
            use_ollama = (x_use_ollama == "true") or getattr(request, 'use_ollama', False)
            api_key = x_api_key or ""
            agent.configure_llm(api_key, x_base_url, x_model, use_ollama=use_ollama)
        
        async def audit_token_stream() -> AsyncGenerator[str, None]:
            try:
                async for token in agent.stream_audit_message(request.content, request.user_id, request.files):
                    yield token
            except Exception as e:
                # 如果流式生成失败，返回错误信息
                error_response = json.dumps({
                    "success": False,
                    "error": str(e)
                })
                yield error_response
        
        return StreamingResponse(audit_token_stream(), media_type="text/plain")
    except Exception as e:
        logger.error(f"流式处理单据审核消息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/consultation/stream")
async def stream_consultation_message(
    request: MessageRequest,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None),
    x_use_ollama: Optional[str] = Header(None)
):
    """流式处理财务咨询消息"""
    try:
        # 使用增强智能体
        agent = get_agent_executor(request.session_id)
        
        # 配置LLM（如果提供了配置信息）
        if x_base_url and x_model:
            use_ollama = (x_use_ollama == "true") or getattr(request, 'use_ollama', False)
            api_key = x_api_key or ""
            agent.configure_llm(api_key, x_base_url, x_model, use_ollama=use_ollama)
        
        async def consultation_token_stream() -> AsyncGenerator[str, None]:
            try:
                async for token in agent.stream_consultation_message(request.content, request.user_id, request.files):
                    yield token
            except Exception as e:
                # 如果流式生成失败，返回错误信息
                error_response = json.dumps({
                    "success": False,
                    "error": str(e)
                })
                yield error_response
        
        return StreamingResponse(consultation_token_stream(), media_type="text/plain")
    except Exception as e:
        logger.error(f"流式处理财务咨询消息失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/audit/rag/add")
async def add_audit_rag_data(
    request: Request,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    """添加单据审核相关的RAG数据"""
    try:
        body = await request.json()
        data = body.get("data", [])
        
        if not data:
            raise HTTPException(status_code=400, detail="缺少数据")
        
        # 获取智能体执行器
        agent = get_agent_executor()
        
        # 配置LLM（如果提供了配置信息）
        if x_api_key and x_base_url and x_model:
            agent.configure_llm(x_api_key, x_base_url, x_model)
        
        # 添加RAG数据
        result = await agent.add_audit_rag_data(data)
        
        return {
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "error": result.get("error"),
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"添加单据审核RAG数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/agent/v2/audit/rag/upload")
async def upload_audit_regulations(
    file: UploadFile = File(...),
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    """上传规章制度文档并自动解析"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor()
        
        # 配置LLM（如果提供了配置信息）
        if x_api_key and x_base_url and x_model:
            agent.configure_llm(x_api_key, x_base_url, x_model)
        
        # 保存文件
        file_content = await file.read()
        filename = (file.filename or "").lower()
        file_ext = os.path.splitext(filename)[1]
        unique_name = f"{uuid.uuid4()}{file_ext}"
        save_path = os.path.join(UPLOAD_DIR, unique_name)
        
        with open(save_path, "wb") as f:
            f.write(file_content)
        
        # 解析文档内容
        document_content = ""
        if filename.endswith((".png", ".jpg", ".jpeg", ".bmp", ".tiff", ".tif", ".webp")):
            # OCR处理
            result = await agent.execute_tool("ocr_processor", file_path=save_path)
            if not result["success"]:
                raise Exception(result["error"])
            document_content = result["data"]["text"]
        elif filename.endswith(".pdf"):
            # PDF处理
            result = await agent.execute_tool("pdf_processor", file_path=save_path)
            if not result["success"]:
                raise Exception(result["error"])
            document_content = result["data"]["text"]
        elif filename.endswith((".txt", ".doc", ".docx")):
            # 文本文件处理
            with open(save_path, "r", encoding="utf-8") as f:
                document_content = f.read()
        else:
            raise Exception("不支持的文件类型，请上传图片、PDF、TXT或Word文件")
        
        # 使用AI解析规章制度
        parsed_result = await agent.parse_audit_regulations(document_content)
        
        if not parsed_result["success"]:
            raise Exception(parsed_result.get("error", "解析规章制度失败"))
        
        # 保存解析后的规则数据
        save_result = await agent.save_parsed_regulations(parsed_result["data"], file.filename)
        
        return {
            "success": True,
            "message": f"成功上传并解析文档：{filename}",
            "parsed_data": parsed_result["data"],
            "save_result": save_result,
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"上传规章制度文档失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/agent/v2/audit/rag/list")
async def list_audit_rag_data():
    """列出单据审核相关的RAG数据"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor()
        
        # 获取RAG数据列表
        result = await agent.list_audit_rag_data()
        
        return {
            "success": result.get("success", False),
            "data": result.get("data", []),
            "error": result.get("error"),
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"列出单据审核RAG数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/agent/v2/audit/rag/{item_id}")
async def delete_audit_rag_data(
    item_id: str
):
    """删除单据审核相关的RAG数据"""
    try:
        # 获取智能体执行器
        agent = get_agent_executor()
        
        # 删除RAG数据
        result = await agent.delete_audit_rag_data(item_id)
        
        return {
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "error": result.get("error"),
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"删除单据审核RAG数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/agent/v2/audit/rag/{item_id}")
async def update_audit_rag_data(
    item_id: str,
    request: Request
):
    """更新单据审核相关的RAG数据"""
    try:
        body = await request.json()
        data = body.get("data")
        
        if not data:
            raise HTTPException(status_code=400, detail="缺少数据")
        
        # 获取智能体执行器
        agent = get_agent_executor()
        
        # 更新RAG数据
        result = await agent.update_audit_rag_data(item_id, data)
        
        return {
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "error": result.get("error"),
            "version": "v2"
        }
    except Exception as e:
        logger.error(f"更新单据审核RAG数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# ============ MCP 工具集成API ============

@router.get("/agent/v2/mcp/tools")
async def get_mcp_tools():
    """获取可用的 MCP 工具列表"""
    try:
        tools = await mcp_integration.get_available_tools_for_agent()
        return {
            "success": True,
            "tools": tools,
            "count": len(tools)
        }
    except Exception as e:
        logger.error(f"获取 MCP 工具列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent/v2/mcp/tool/call")
async def call_mcp_tool(request: ToolRequest):
    """调用 MCP 工具"""
    try:
        result = await mcp_integration.call_mcp_tool_for_agent(
            request.tool_name, 
            request.parameters
        )
        return result
    except Exception as e:
        logger.error(f"调用 MCP 工具失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent/v2/mcp/process")
async def process_mcp_action(request: Dict[str, Any]):
    """处理 Agent 的 MCP 工具请求"""
    try:
        result = await mcp_integration.process_agent_tool_request(request)
        return result
    except Exception as e:
        logger.error(f"处理 MCP 动作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# MCP 集成初始化函数
async def initialize_mcp_integration():
    """初始化 MCP 集成"""
    try:
        logger.info("初始化 MCP 集成...")
        await mcp_integration.initialize()
        logger.info("MCP 集成初始化完成")
    except Exception as e:
        logger.error(f"MCP 集成初始化失败: {e}")


# ==================== 新的AI服务配置API ====================

class AIServiceConfigModel(BaseModel):
    """AI服务配置模型"""
    id: Optional[str] = None
    name: str
    type: str  # 'openai_compatible', 'ollama', 'custom'
    enabled: bool = True
    isDefault: bool = False
    supportsMultimodal: bool = False
    priority: int = 0
    config: Dict[str, Any]
    connectionStatus: str = 'none'
    lastTested: Optional[str] = None


class AIConfigModel(BaseModel):
    """AI配置模型"""
    services: List[AIServiceConfigModel]
    activeServiceId: Optional[str] = None
    globalSettings: Dict[str, Any] = {}


@router.get("/ai/services")
async def get_ai_services():
    """获取所有AI服务配置"""
    try:
        return {"success": True, "services": global_ai_config["services"]}
    except Exception as e:
        logger.error(f"获取AI服务配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai/services")
async def create_ai_service(service: AIServiceConfigModel):
    """创建新的AI服务配置"""
    try:
        # 验证配置
        if not service.name.strip():
            raise HTTPException(status_code=400, detail="服务名称不能为空")

        # 生成ID
        if not service.id:
            service.id = f"ai_service_{uuid.uuid4().hex[:8]}"

        # 添加到全局配置
        service_dict = service.dict()
        global_ai_config["services"].append(service_dict)

        # 如果是第一个服务，设为活动服务
        if len(global_ai_config["services"]) == 1:
            global_ai_config["activeServiceId"] = service.id

        logger.info(f"创建AI服务配置: {service.name}")

        return {"success": True, "service": service_dict}
    except Exception as e:
        logger.error(f"创建AI服务配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/ai/services/{service_id}")
async def update_ai_service(service_id: str, service: AIServiceConfigModel):
    """更新AI服务配置"""
    try:
        service.id = service_id

        # 在全局配置中查找并更新服务
        for i, existing_service in enumerate(global_ai_config["services"]):
            if existing_service["id"] == service_id:
                global_ai_config["services"][i] = service.dict()
                break
        else:
            raise HTTPException(status_code=404, detail="服务配置不存在")

        logger.info(f"更新AI服务配置: {service_id}")

        return {"success": True, "service": service.dict()}
    except Exception as e:
        logger.error(f"更新AI服务配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/ai/services/{service_id}")
async def delete_ai_service(service_id: str):
    """删除AI服务配置"""
    try:
        # 从全局配置中删除服务
        for i, service in enumerate(global_ai_config["services"]):
            if service["id"] == service_id:
                del global_ai_config["services"][i]

                # 如果删除的是活动服务，重新设置活动服务
                if global_ai_config["activeServiceId"] == service_id:
                    if global_ai_config["services"]:
                        global_ai_config["activeServiceId"] = global_ai_config["services"][0]["id"]
                    else:
                        global_ai_config["activeServiceId"] = None

                break
        else:
            raise HTTPException(status_code=404, detail="服务配置不存在")

        logger.info(f"删除AI服务配置: {service_id}")

        return {"success": True, "message": "服务配置已删除"}
    except Exception as e:
        logger.error(f"删除AI服务配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai/services/{service_id}/test")
async def test_ai_service(service_id: str, service: AIServiceConfigModel):
    """测试AI服务连接"""
    try:
        # 根据服务类型构建测试配置
        if service.type == 'ollama':
            api_key = ""
            base_url = service.config.get('baseUrl', 'http://localhost:11434')
            model = service.config.get('model', 'llama3.1:8b')
            use_ollama = True
        else:  # openai_compatible 或 custom
            api_key = service.config.get('apiKey', '')
            base_url = service.config.get('baseUrl', '')
            model = service.config.get('model', '')
            use_ollama = False

        logger.info(f"测试AI服务连接: {service.name}, type={service.type}")

        # 使用现有的AI客户端进行测试
        ai_client = get_ai_client(api_key, base_url, model, use_ollama)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")

        result = await ai_client.analyze_receipt_content("你好，这是连接测试", None)
        if result.get("success"):
            return {"success": True, "message": "连接测试成功"}
        else:
            raise Exception(result.get("error", "AI测试失败"))
    except Exception as e:
        logger.error(f"测试AI服务连接失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/ai/config")
async def save_ai_config(config: AIConfigModel):
    """保存完整的AI配置"""
    try:
        # 更新全局配置
        global_ai_config["services"] = [service.dict() if hasattr(service, 'dict') else service for service in config.services]
        global_ai_config["activeServiceId"] = config.activeServiceId
        global_ai_config["globalSettings"] = config.globalSettings

        logger.info(f"保存AI配置，包含 {len(config.services)} 个服务")

        return {"success": True, "message": "配置保存成功"}
    except Exception as e:
        logger.error(f"保存AI配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai/config")
async def get_ai_config():
    """获取完整的AI配置"""
    try:
        return {"success": True, "config": global_ai_config}
    except Exception as e:
        logger.error(f"获取AI配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))