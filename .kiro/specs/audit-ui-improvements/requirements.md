# 单据审核UI改进需求文档

## 简介

当前单据审核功能存在显示问题：只能显示单一类型的对话框，缺乏流式显示，用户体验差。需要改进前端UI以支持多种审核状态的流式显示，提供更好的用户反馈和交互体验。

## 需求

### 需求 1：流式审核分析显示

**用户故事：** 作为用户，我希望在单据审核过程中能够实时看到AI的分析过程，这样我就能了解审核进度并获得更好的体验。

#### 验收标准

1. WHEN 用户上传单据并开始审核 THEN 系统应立即显示审核分析消息框并开始流式输出分析内容
2. WHEN AI正在进行审核分析 THEN 前端应实时显示分析文本内容，而不是等待完整响应
3. WHEN 审核分析完成 THEN 系统应保持分析内容显示并继续处理后续结果

### 需求 2：多类型审核结果显示

**用户故事：** 作为用户，我希望根据不同的审核结果看到不同样式的消息框，这样我就能快速理解审核状态和所需行动。

#### 验收标准

1. WHEN 审核通过 THEN 系统应显示绿色样式的"审核通过"消息框，包含通过原因
2. WHEN 审核不通过 THEN 系统应显示红色样式的"审核不通过"消息框，包含拒绝原因
3. WHEN 需要补充信息 THEN 系统应显示蓝色样式的"需要补充信息"消息框，包含所需信息列表
4. WHEN 审核结果为其他状态 THEN 系统应显示相应样式的消息框

### 需求 3：结构化信息请求显示

**用户故事：** 作为用户，当系统需要我补充信息时，我希望看到清晰的信息列表和指导，这样我就能准确提供所需信息。

#### 验收标准

1. WHEN 系统需要补充信息 THEN 应显示结构化的信息请求列表，每项信息单独列出
2. WHEN 显示信息请求 THEN 应包含序号、具体要求和操作提示
3. WHEN 用户查看信息请求 THEN 应提供清晰的下一步操作指导

### 需求 4：响应时间优化

**用户故事：** 作为用户，我希望在提交审核请求后能够快速看到系统响应，而不是长时间等待，这样我就能获得更流畅的使用体验。

#### 验收标准

1. WHEN 用户提交审核请求 THEN 系统应在1秒内显示"思考中"或"分析中"状态
2. WHEN AI开始返回内容 THEN 前端应立即开始显示流式内容，不等待完整响应
3. WHEN 后端有日志输出 THEN 前端应同步显示相应的用户界面更新

### 需求 5：审核状态管理

**用户故事：** 作为用户，我希望清楚地了解当前审核处于什么阶段，这样我就能合理安排等待时间和后续操作。

#### 验收标准

1. WHEN 审核开始 THEN 系统应显示"审核分析中"状态指示器
2. WHEN 审核分析完成但结果处理中 THEN 系统应显示"处理结果中"状态
3. WHEN 审核完全结束 THEN 系统应清除所有加载状态并显示最终结果
4. WHEN 用户取消审核 THEN 系统应正确清理所有相关状态和消息

### 需求 6：错误处理和用户反馈

**用户故事：** 作为用户，当审核过程中出现错误时，我希望看到清晰的错误信息和解决建议，这样我就能知道如何处理问题。

#### 验收标准

1. WHEN 审核过程中发生错误 THEN 系统应显示具体的错误信息而不是通用错误
2. WHEN 网络连接问题导致审核失败 THEN 系统应提供重试选项
3. WHEN 文件上传失败 THEN 系统应指导用户检查文件格式和大小
4. WHEN 后端服务不可用 THEN 系统应显示服务状态信息和预计恢复时间

### 需求 7：消息类型扩展

**用户故事：** 作为开发者，我希望消息系统能够支持更多的审核相关消息类型，这样就能为不同的审核场景提供合适的UI展示。

#### 验收标准

1. WHEN 定义新的消息类型 THEN 系统应支持AUDIT_ANALYSIS、AUDIT_RESULT、AUDIT_INFO_REQUEST等专用类型
2. WHEN 渲染不同消息类型 THEN 每种类型应有独特的视觉样式和布局
3. WHEN 消息类型包含结构化数据 THEN 系统应正确解析和显示结构化内容
4. WHEN 消息需要用户交互 THEN 系统应提供相应的交互元素

### 需求 8：流式数据处理优化

**用户故事：** 作为开发者，我希望前端能够正确处理后端的流式响应，包括结构化标记和JSON数据，这样就能提供准确的用户界面更新。

#### 验收标准

1. WHEN 后端返回带有结构化标记的流式数据 THEN 前端应正确解析标记内容
2. WHEN 流式数据包含JSON结构 THEN 前端应在流式结束后处理JSON数据
3. WHEN 流式数据同时包含文本和结构化数据 THEN 前端应分别处理并正确显示
4. WHEN 流式连接中断 THEN 前端应优雅处理并提供恢复选项