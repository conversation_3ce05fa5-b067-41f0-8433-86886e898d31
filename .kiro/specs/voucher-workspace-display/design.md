# Design Document

## Overview

本设计文档描述了凭证工作区显示功能的技术架构和实现方案。该功能将为凭证模块添加类似 VSCode 文件标签页的工作区显示特性，允许用户通过点击和双击操作管理多个凭证记录的显示状态。设计采用通用化架构，便于后续扩展到其他功能模块。

## Architecture

### 系统架构图

```mermaid
graph TB
    A[Voucher Component] --> B[WorkspaceManager]
    B --> C[WorkspaceProvider Context]
    C --> D[Enhanced Workspace Component]
    D --> E[Original Workspace Display]
    D --> F[Interactive Workspace Panel]
    F --> G[WorkspaceItem Component]
    G --> H[VoucherRenderer]
    
    B --> I[WorkspaceStorage]
    I --> J[LocalStorage API]
    
    K[Other Modules] -.-> B
    K -.-> L[CustomRenderer]
    L -.-> G
    
    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#e8f5e8
```

### 核心架构原则

1. **向后兼容**：保持现有工作区组件的完整功能，新功能作为增强层添加
2. **通用性设计**：工作区管理器采用泛型设计，支持不同类型的记录显示
3. **状态管理**：使用 React Context 进行全局状态管理，确保组件间数据同步
4. **渲染分离**：记录渲染逻辑与工作区管理逻辑分离，支持自定义渲染器
5. **持久化存储**：工作区状态自动保存到本地存储，支持页面刷新恢复
6. **渐进增强**：现有工作区功能保持不变，新的交互式工作区作为可选功能

## Components and Interfaces

### 1. WorkspaceProvider Context

```typescript
interface WorkspaceItem {
  id: string | number;
  type: string; // 'voucher', 'subject', 'asset', etc.
  data: any;
  isPinned: boolean;
  timestamp: number;
}

interface WorkspaceContextValue {
  items: WorkspaceItem[];
  addItem: (item: Omit<WorkspaceItem, 'timestamp'>) => void;
  removeItem: (id: string | number) => void;
  pinItem: (id: string | number) => void;
  unpinItem: (id: string | number) => void;
  clearWorkspace: () => void;
  getItemsByType: (type: string) => WorkspaceItem[];
}
```

### 2. WorkspaceManager Hook

```typescript
interface WorkspaceManagerOptions {
  maxItems?: number;
  storageKey?: string;
  autoSave?: boolean;
}

interface WorkspaceManager {
  handleSingleClick: (item: any, type: string) => void;
  handleDoubleClick: (item: any, type: string) => void;
  isItemInWorkspace: (id: string | number) => boolean;
  getWorkspaceItems: () => WorkspaceItem[];
}
```

### 3. WorkspaceDisplay Component

```typescript
interface WorkspaceDisplayProps {
  type?: string; // 过滤显示特定类型的记录
  renderItem: (item: WorkspaceItem) => React.ReactNode;
  emptyStateComponent?: React.ComponentType;
  maxHeight?: string;
  className?: string;
}
```

### 4. ClickableRecord HOC

```typescript
interface ClickableRecordProps {
  item: any;
  type: string;
  children: React.ReactNode;
  onSingleClick?: (item: any) => void;
  onDoubleClick?: (item: any) => void;
  className?: string;
}
```

## Data Models

### WorkspaceItem 数据模型

```typescript
interface WorkspaceItem {
  id: string | number;           // 记录唯一标识
  type: string;                  // 记录类型 ('voucher', 'subject', 'asset')
  data: any;                     // 记录原始数据
  isPinned: boolean;             // 是否固定到工作区
  timestamp: number;             // 添加到工作区的时间戳
  displayName?: string;          // 显示名称（可选）
  metadata?: {                   // 元数据（可选）
    [key: string]: any;
  };
}
```

### 凭证记录扩展

```typescript
interface VoucherWorkspaceItem extends WorkspaceItem {
  type: 'voucher';
  data: {
    id: number;
    voucher_no: string;
    date: string;
    summary: string;
    total_amount: number;
    status: string;
    creator: string;
    reviewer?: string;
    entries: VoucherEntry[];
  };
}
```

## Error Handling

### 错误类型定义

```typescript
enum WorkspaceErrorType {
  STORAGE_ERROR = 'STORAGE_ERROR',
  ITEM_NOT_FOUND = 'ITEM_NOT_FOUND',
  INVALID_ITEM_TYPE = 'INVALID_ITEM_TYPE',
  MAX_ITEMS_EXCEEDED = 'MAX_ITEMS_EXCEEDED'
}

interface WorkspaceError {
  type: WorkspaceErrorType;
  message: string;
  details?: any;
}
```

### 错误处理策略

1. **存储错误**：当本地存储失败时，显示警告但不阻止功能使用
2. **记录不存在**：当尝试操作不存在的记录时，自动从工作区移除
3. **类型错误**：当记录类型不匹配时，显示错误提示并忽略操作
4. **容量限制**：当工作区记录超出限制时，自动移除最旧的非固定记录

### 错误恢复机制

```typescript
interface ErrorRecoveryOptions {
  fallbackToEmptyWorkspace: boolean;
  showUserNotification: boolean;
  logError: boolean;
  retryCount: number;
}
```

## Testing Strategy

### 单元测试

1. **WorkspaceProvider 测试**
   - Context 状态管理正确性
   - 添加/移除/固定操作功能
   - 本地存储同步机制

2. **WorkspaceManager Hook 测试**
   - 单击/双击事件处理
   - 记录状态判断逻辑
   - 工作区容量管理

3. **组件渲染测试**
   - WorkspaceDisplay 组件渲染
   - ClickableRecord HOC 事件绑定
   - 空状态显示

### 集成测试

1. **用户交互流程测试**
   - 完整的点击->显示->固定->移除流程
   - 多记录管理场景
   - 页面刷新状态恢复

2. **跨模块兼容性测试**
   - 凭证模块集成测试
   - 其他模块扩展测试
   - 渲染器自定义测试

### 性能测试

1. **大量记录处理**
   - 工作区显示100+记录的性能
   - 滚动流畅性测试
   - 内存使用监控

2. **存储性能**
   - 本地存储读写性能
   - 状态同步延迟测试

## Implementation Details

### 文件结构

```
frontend/src/
├── components/
│   ├── workspace/
│   │   ├── WorkspaceProvider.jsx
│   │   ├── WorkspaceDisplay.jsx
│   │   ├── WorkspaceItem.jsx
│   │   ├── ClickableRecord.jsx
│   │   └── index.js
│   ├── voucher/
│   │   ├── VoucherWorkspaceRenderer.jsx
│   │   └── VoucherClickableRow.jsx
│   └── ...
├── hooks/
│   ├── useWorkspace.js
│   └── useWorkspaceManager.js
├── utils/
│   ├── workspaceStorage.js
│   └── workspaceHelpers.js
└── ...
```

### 关键实现要点

1. **事件处理优化**
   - 使用防抖处理双击事件，避免与单击冲突
   - 事件委托减少事件监听器数量

2. **虚拟滚动**
   - 当工作区记录超过50个时启用虚拟滚动
   - 提升大量记录时的渲染性能

3. **动画效果**
   - 记录添加/移除使用平滑过渡动画
   - 固定状态切换的视觉反馈

4. **键盘支持**
   - 支持键盘导航工作区记录
   - 快捷键支持（Ctrl+W 关闭记录等）

### 兼容性设计

#### 现有工作区保持不变
- 保留 `Workspace.jsx` 组件的所有现有功能
- 保持现有的 `vouchers`, `subjects`, `assets`, `staffs` 状态管理
- 现有的工作区显示逻辑完全不受影响

#### 增强型工作区设计
- 新增 `InteractiveWorkspace` 组件作为可选的增强功能
- 通过 props 控制是否启用交互式功能
- 可以与现有工作区并存或替换显示

```typescript
// 兼容性接口
interface EnhancedWorkspaceProps {
  // 保持现有 props
  vouchers: any[];
  subjects: any[];
  assets: any[];
  staffs: any[];
  
  // 新增交互式功能 props
  enableInteractive?: boolean;
  onVoucherClick?: (voucher: any) => void;
  onVoucherDoubleClick?: (voucher: any) => void;
  workspaceItems?: WorkspaceItem[];
}
```

### 扩展接口设计

```typescript
// 模块注册接口
interface ModuleWorkspaceConfig {
  type: string;
  displayName: string;
  renderer: React.ComponentType<{item: WorkspaceItem}>;
  clickHandler?: (item: any) => void;
  doubleClickHandler?: (item: any) => void;
  maxItems?: number;
}

// 工作区扩展管理器
class WorkspaceExtensionManager {
  static registerModule(config: ModuleWorkspaceConfig): void;
  static getModuleConfig(type: string): ModuleWorkspaceConfig | null;
  static getAllModules(): ModuleWorkspaceConfig[];
}
```

### 性能优化策略

1. **记录缓存**：使用 React.memo 缓存记录组件渲染
2. **懒加载**：工作区记录详情按需加载
3. **批量更新**：多个记录操作合并为单次状态更新
4. **内存管理**：定期清理过期的临时记录

### 可访问性支持

1. **ARIA 标签**：为工作区记录添加适当的 ARIA 标签
2. **键盘导航**：支持 Tab 键和方向键导航
3. **屏幕阅读器**：提供语义化的状态描述
4. **高对比度**：支持高对比度主题模式