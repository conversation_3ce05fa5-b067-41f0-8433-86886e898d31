import React, { useEffect, useState } from 'react';

const REPORT_TYPES = [
  { key: 'balance-sheet', label: '资产负债表' },
  { key: 'income-statement', label: '利润表' },
  { key: 'cash-flow', label: '现金流量表' },
];

const API_MAP = {
  'balance-sheet': {
    fetch: 'http://localhost:8000/reports/balance-sheet',
    export: 'http://localhost:8000/reports/balance-sheet/export',
  },
  'income-statement': {
    fetch: 'http://localhost:8000/reports/income-statement',
    export: 'http://localhost:8000/reports/income-statement/export',
  },
  'cash-flow': {
    fetch: 'http://localhost:8000/reports/cash-flow',
    export: 'http://localhost:8000/reports/cash-flow/export',
  },
};

const Report = () => {
  const [reportType, setReportType] = useState('balance-sheet');
  const [report, setReport] = useState([]);

  const fetchReport = async () => {
    const url = API_MAP[reportType].fetch;
    const res = await fetch(url);
    const data = await res.json();
    setReport(data.report || []);
  };

  useEffect(() => {
    fetchReport();
    // eslint-disable-next-line
  }, [reportType]);

  const handleExport = () => {
    const url = API_MAP[reportType].export;
    window.open(url, '_blank');
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">报表管理</h1>
        <p className="text-gray-600">查看和管理各类财务报表</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">报表项目数</p>
              <p className="text-2xl font-semibold text-gray-900">{report.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">本期总金额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {report.reduce((sum, row) => {
                  const current = row.current ?? row.amount ?? 0;
                  return sum + (typeof current === 'number' ? current : 0);
                }, 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">上期总金额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {report.reduce((sum, row) => {
                  const last = row.last ?? 0;
                  return sum + (typeof last === 'number' ? last : 0);
                }, 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总差额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {report.reduce((sum, row) => {
                  const current = row.current ?? row.amount ?? 0;
                  const last = row.last ?? 0;
                  const diff = (typeof current === 'number' && typeof last === 'number') ? (current - last) : 0;
                  return sum + diff;
                }, 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            <div className="flex items-center space-x-4">
              {REPORT_TYPES.map(t => (
                <button
                  key={t.key}
                  className={`px-4 py-2 rounded-lg ${reportType === t.key ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setReportType(t.key)}
                >
                  {t.label}
                </button>
              ))}
            </div>
          </div>
          <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center" onClick={handleExport}>
            导出
          </button>
        </div>
      </div>

      {/* 报表表格 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">本期金额</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上期金额</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">去年同期金额</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">差额</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {report.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center py-12">
                    <p className="text-gray-500">暂无数据</p>
                  </td>
                </tr>
              ) : report.map((row, idx) => {
                const current = row.current ?? row.amount ?? '';
                const last = row.last ?? '';
                const lastYear = row.lastYear ?? row.last_year ?? '';
                // 差额 = 本期 - 上期（如有），否则空
                const diff = (current !== '' && last !== '') ? (current - last) : '';
                return (
                  <tr key={idx} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{row.item}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {current !== '' ? Number(current).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {last !== '' ? Number(last).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {lastYear !== '' ? Number(lastYear).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}
                    </td>
                    <td className={`px-6 py-4 whitespace-nowrap text-sm text-right ${diff > 0 ? 'text-green-600' : diff < 0 ? 'text-red-600' : 'text-gray-900'}`}>
                      {diff !== '' ? Number(diff).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : '-'}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Report;
