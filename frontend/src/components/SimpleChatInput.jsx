import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Send, 
  Image, 
  X, 
  Plus,
  StopCircle,
  CheckCircle,
  FileText,
  MessageCircle,
  ChevronDown
} from 'lucide-react';

// 自定义下拉选择器组件
const CustomSelect = ({ value, onChange, disabled, options }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownDirection, setDropdownDirection] = useState('up');
  const selectRef = useRef(null);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 计算下拉框应该向上还是向下展开
  const calculateDropdownDirection = () => {
    if (!selectRef.current) return 'up';
    
    const rect = selectRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = options.length * 40 + 8; // 估算下拉框高度
    
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    
    // 如果下方空间足够，向下展开；否则向上展开
    return spaceBelow >= dropdownHeight ? 'down' : 'up';
  };

  const selectedOption = options.find(option => option.value === value);

  const handleToggle = () => {
    if (disabled) return;
    
    if (!isOpen) {
      // 打开时计算方向
      const direction = calculateDropdownDirection();
      setDropdownDirection(direction);
    }
    
    setIsOpen(!isOpen);
  };

  const handleSelect = (optionValue) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={selectRef}>
      {/* 选择器按钮 */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className="flex items-center gap-2 text-sm bg-transparent border border-gray-300 rounded-md px-2 py-1 text-gray-600 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer min-w-[120px]"
      >
        {selectedOption?.icon}
        <span className="flex-1 text-left">{selectedOption?.label}</span>
        <ChevronDown size={12} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* 下拉选项 */}
      {isOpen && (
        <div className={`absolute left-0 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50 ${
          dropdownDirection === 'up' 
            ? 'bottom-full mb-1' 
            : 'top-full mt-1'
        }`}>
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleSelect(option.value)}
              className={`w-full flex items-center gap-2 px-2 py-2 text-sm text-left hover:bg-gray-50 transition-colors first:rounded-t-md last:rounded-b-md ${
                option.value === value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              {option.icon}
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const SimpleChatInput = ({
  value,
  onChange,
  onSend,
  onFileSelect,
  uploadedFiles = [],
  onRemoveFile,
  isLoading = false,
  isStreaming = false,
  onStop,
  placeholder = "Ask a question or describe a task...",
  disabled = false,
  selectedFunction,
  onFunctionChange
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const imageInputRef = useRef(null);
  const textareaRef = useRef(null);

  // 自动调整textarea高度
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 120; // 最大高度
      textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [value, adjustTextareaHeight]);

  // 处理拖拽事件
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    // 只允许图片文件
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    if (imageFiles.length > 0 && onFileSelect) {
      onFileSelect(imageFiles);
    }
  }, [onFileSelect]);

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() || uploadedFiles.length > 0) {
        onSend();
      }
    }
  };

  // 处理图片上传按钮点击
  const handleImageButtonClick = () => {
    imageInputRef.current?.click();
  };

  // 重置文件输入框的值，解决重复选择问题
  const resetFileInput = (inputRef) => {
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const handleImageInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0 && onFileSelect) {
      onFileSelect(files);
    }
    // 重置输入框值，允许重新选择相同文件
    resetFileInput(imageInputRef);
  };

  return (
    <div className="relative">
      {/* 拖拽覆盖层 */}
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-2xl flex items-center justify-center z-10 backdrop-blur-sm">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <Plus className="w-6 h-6 text-white" />
            </div>
            <p className="text-blue-600 font-medium">拖拽图片到这里上传</p>
          </div>
        </div>
      )}

      {/* 主输入区域 */}
      <div 
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`relative bg-white/95 backdrop-blur-sm border rounded-2xl shadow-lg transition-all duration-200 ${
          isDragOver ? 'border-blue-500 shadow-blue-500/20' : 'border-gray-200 hover:border-gray-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {/* 已上传文件显示 */}
        {uploadedFiles.length > 0 && (
          <div className="p-3 border-b border-gray-100">
            <div className="flex flex-wrap gap-2">
              {uploadedFiles.map((file, index) => (
                <div 
                  key={file.id || index} 
                  className="flex items-center gap-2 bg-gray-50 hover:bg-gray-100 rounded-xl px-3 py-2 text-sm transition-colors group"
                >
                  <div className="flex items-center gap-2">
                    <Image size={16} className="text-blue-500" />
                    <span className="text-gray-700 max-w-32 truncate">
                      {file.name}
                    </span>
                    <span className="text-xs text-gray-400">
                      ({(file.size / 1024).toFixed(1)}KB)
                    </span>
                  </div>
                  <button
                    onClick={() => onRemoveFile && onRemoveFile(index)}
                    className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all duration-200 p-1 hover:bg-red-50 rounded-full"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 主输入区域 - 重新设计布局 */}
        <div className="p-4">
          {/* 第一行：功能按钮区域 */}
          <div className="flex items-center gap-2 mb-3">
            {/* @ 按钮 - 引用人或部门 */}
            <button
              disabled={disabled || (isLoading && !isStreaming)}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="引用人或部门（开发中）"
            >
              <span className="text-lg font-medium">@</span>
            </button>

            {/* # 按钮 - 引用记录 */}
            <button
              disabled={disabled || (isLoading && !isStreaming)}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="引用记录（开发中）"
            >
              <span className="text-lg font-medium">#</span>
            </button>

            {/* 上传图片按钮 */}
            <button
              onClick={handleImageButtonClick}
              disabled={disabled || (isLoading && !isStreaming)}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="上传图片"
            >
              <Image size={18} />
            </button>
          </div>

          {/* 第二行：文本输入区域 */}
          <div className="mb-3">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange && onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled || (isLoading && !isStreaming)}
              rows={1}
              className="w-full resize-none border-0 outline-none bg-transparent text-gray-800 placeholder-gray-400 text-base leading-relaxed overflow-hidden"
              style={{ 
                minHeight: '40px',
                maxHeight: '120px',
                paddingTop: '8px',
                paddingBottom: '8px'
              }}
            />
          </div>

          {/* 第三行：底部控制区域 */}
          <div className="flex items-center justify-between">
            {/* 左下：自定义功能选择器 */}
            <CustomSelect
              value={selectedFunction || '单据审核'}
              onChange={(value) => onFunctionChange && onFunctionChange(value)}
              disabled={disabled || (isLoading && !isStreaming)}
              options={[
                { value: '单据审核', label: '单据审核', icon: <CheckCircle size={14} /> },
                { value: '记账凭证', label: '记账凭证', icon: <FileText size={14} /> },
                { value: '财务咨询', label: '财务咨询', icon: <MessageCircle size={14} /> }
              ]}
            />

            {/* 右下：发送按钮 */}
            <button
              onClick={isStreaming ? onStop : onSend}
              disabled={disabled || (!isStreaming && !value.trim() && uploadedFiles.length === 0)}
              className={`p-2.5 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center ${
                isStreaming
                  ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg hover:shadow-red-500/25'
                  : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-blue-500/25 hover:scale-105'
              }`}
              title={isStreaming ? '停止生成' : '发送消息'}
            >
              {isStreaming ? (
                <StopCircle size={20} />
              ) : isLoading ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <Send size={20} />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 隐藏的图片输入框 */}
      <input
        ref={imageInputRef}
        type="file"
        multiple
        accept="image/*"
        className="hidden"
        onChange={handleImageInputChange}
      />


    </div>
  );
};

export default SimpleChatInput;