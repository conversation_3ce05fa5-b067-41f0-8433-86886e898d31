import React, { useState, useEffect } from 'react';
import { Bot, MessageSquare, Plus, Search, Settings, User, Home, FileText, BarChart3, Calculator, DollarSign, Receipt, Briefcase, Users, Building, Cog, Sparkles, Minus, Square, X, Database, Server, ChevronDown, Cloud, HardDrive, LayoutGrid } from 'lucide-react';
import StatusBar from './StatusBar';
import SettingsManager from '../settings/SettingsManager';
import RAGManagement from '../settings/RAGManagement';
import MCPManagement from '../settings/MCPManagement';

const ElectronMainLayout = ({ children, activeFeature, onFeatureSelect, features, sessions, activeSessionId, onSessionSelect, onNewSession, aiConfig, setAiConfig, backendBase, isWorkspaceVisible }) => {
  const isElectron = typeof window !== 'undefined' && window.electronAPI;
  const [activeSetting, setActiveSetting] = useState('settings');
  const [showAIServiceSelector, setShowAIServiceSelector] = useState(false);
  const [activeAIService, setActiveAIService] = useState('remote');

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showAIServiceSelector && !event.target.closest('.ai-service-selector')) {
        setShowAIServiceSelector(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAIServiceSelector]);

  // 根据配置自动设置活动的AI服务
  useEffect(() => {
    if (aiConfig) {
      // 根据当前启用的服务和use_ollama设置来确定活动的AI服务
      // 优先级：1. 如果本地服务启用且use_ollama为true，使用本地服务
      //         2. 如果远程服务启用且use_ollama为false，使用远程服务
      //         3. 如果本地服务启用但use_ollama为false，切换到本地服务
      //         4. 如果远程服务启用但use_ollama为true，切换到远程服务
      //         5. 如果只有一个服务启用，自动切换到该服务
      //         6. 如果都没有启用，默认使用远程服务
      
      let newActiveService = activeAIService; // 默认保持当前选择
      
      if (aiConfig.local_enabled && aiConfig.use_ollama) {
        newActiveService = 'local';
      } else if (aiConfig.remote_enabled && !aiConfig.use_ollama) {
        newActiveService = 'remote';
      } else if (aiConfig.local_enabled && !aiConfig.use_ollama) {
        newActiveService = 'local';
        // 需要更新use_ollama为true
        const updatedConfig = {
          ...aiConfig,
          use_ollama: true
        };
        setAiConfig(updatedConfig);
        localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
      } else if (aiConfig.remote_enabled && aiConfig.use_ollama) {
        newActiveService = 'remote';
        // 需要更新use_ollama为false
        const updatedConfig = {
          ...aiConfig,
          use_ollama: false
        };
        setAiConfig(updatedConfig);
        localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
      } else if (aiConfig.local_enabled && !aiConfig.remote_enabled) {
        // 只有本地服务启用，切换到本地服务
        newActiveService = 'local';
        if (!aiConfig.use_ollama) {
          const updatedConfig = {
            ...aiConfig,
            use_ollama: true
          };
          setAiConfig(updatedConfig);
          localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
        }
      } else if (aiConfig.remote_enabled && !aiConfig.local_enabled) {
        // 只有远程服务启用，切换到远程服务
        newActiveService = 'remote';
        if (aiConfig.use_ollama) {
          const updatedConfig = {
            ...aiConfig,
            use_ollama: false
          };
          setAiConfig(updatedConfig);
          localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
        }
      } else if (!aiConfig.local_enabled && !aiConfig.remote_enabled) {
        // 如果都没有启用，默认使用远程服务
        newActiveService = 'remote';
        // 确保aiConfig.use_ollama为false
        if (aiConfig.use_ollama) {
          const updatedConfig = {
            ...aiConfig,
            use_ollama: false
          };
          setAiConfig(updatedConfig);
          localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
        }
      }
      
      setActiveAIService(newActiveService);
    }
  }, [aiConfig, setAiConfig, activeAIService]);

  // 切换AI服务
  const handleAIServiceSwitch = (service) => {
    console.log('[DEBUG] handleAIServiceSwitch called with service:', service);
    console.log('[DEBUG] current aiConfig:', aiConfig);
    
    // 检查要切换的服务是否已启用
    if (service === 'local' && !aiConfig?.local_enabled) {
      console.log('[DEBUG] Local service is not enabled, cannot switch');
      return;
    }
    if (service === 'remote' && !aiConfig?.remote_enabled) {
      console.log('[DEBUG] Remote service is not enabled, cannot switch');
      return;
    }
    
    setActiveAIService(service);
    
    // 只更新use_ollama字段，不改变服务的启用状态
    // 用户可能希望同时启用本地和远程服务，但在某个时刻选择使用其中一个
    const updatedConfig = {
      ...aiConfig,
      use_ollama: service === 'local'
    };
    console.log('[DEBUG] updatedConfig:', updatedConfig);
    
    setAiConfig(updatedConfig);
    
    // 保存到localStorage
    localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
    console.log('[DEBUG] saved to localStorage');
    
    // 验证保存
    const savedConfig = localStorage.getItem('aiConfig');
    console.log('[DEBUG] verified saved config:', savedConfig);
    
    // 关闭选择器
    setShowAIServiceSelector(false);
  };

  // 获取当前AI服务名称
  const getCurrentAIServiceName = () => {
    if (activeAIService === 'local') {
      return aiConfig?.ollama_model || '本地模型';
    } else {
      return aiConfig?.model || 'ERNIE-4.5';
    }
  };

  // 窗口控制函数
  const handleMinimize = () => {
    if (isElectron) {
      window.electronAPI.minimize();
    }
  };

  const handleMaximize = () => {
    if (isElectron) {
      window.electronAPI.maximize();
    }
  };

  const handleClose = () => {
    if (isElectron) {
      window.electronAPI.close();
    }
  };
  const [assistants, setAssistants] = useState([
    { id: 1, name: 'Default Assistant', avatar: '😊', active: true, lastMessage: '你好，我是智能助手，你可以向我提问任何问题' },
    { id: 2, name: 'Default Assistant', avatar: '😊', active: false, lastMessage: '' }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('assistant');

  const addAssistant = () => {
    const newAssistant = {
      id: Date.now(),
      name: 'Default Assistant',
      avatar: '😊',
      active: false,
      lastMessage: ''
    };
    setAssistants(prev => [...prev, newAssistant]);
  };

  const setActiveAssistant = (id) => {
    setAssistants(prev => prev.map(assistant => ({
      ...assistant,
      active: assistant.id === id
    })));
  };

  // 功能图标映射
  const featureIcons = {
    agent: Bot,
    approval: FileText,
    voucher: Receipt,
    bookkeeping: FileText,
    report: BarChart3,
    settlement: Calculator,
    asset: Briefcase,
    invoice: Receipt,
    cashier: DollarSign,
    salary: Users,
    tax: Calculator,
    subject: FileText,
    roleStaff: Users,
    company: Building,
    settings: Cog
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧边栏 - Cherry Studio 风格 */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
          {/* 顶部标签页 */}
          <div className="h-14 flex border-b border-gray-200 bg-white">
            <button
              onClick={() => {
                setActiveTab('assistant');
                onFeatureSelect('agent'); // 激活智能体功能
                // 如果有会话，默认选中第一个会话
                if (sessions && sessions.length > 0 && onSessionSelect) {
                  onSessionSelect(sessions[0].id);
                }
              }}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'assistant' && activeFeature !== 'settings'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              助手
            </button>
            <button
              onClick={() => {
                setActiveTab('functions');
                // 默认选中第一个功能选项
                const firstFeature = Object.entries(features)
                  .filter(([key]) => key !== 'settings' && key !== 'agent')[0];
                if (firstFeature) {
                  onFeatureSelect(firstFeature[0]);
                }
              }}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'functions' && activeFeature !== 'settings'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              功能
            </button>
          </div>

          {/* 搜索框 - 与右侧状态栏对齐 */}
          <div className="h-14 px-4 bg-white border-b border-gray-200 flex items-center">
            <div className="relative w-full">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={
                  activeTab === 'functions' ? '搜索功能...' :
                  activeTab === 'assistant' ? '搜索助手...' :
                  '搜索设置...'
                }
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50"
              />
            </div>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto bg-gray-50/50">
            {/* 当设置功能激活时显示设置选项列表 */}
            {activeFeature === 'settings' && (
              <div className="p-2">
                <div
                  onClick={() => {
                    setActiveSetting('settings');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'settings'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Cog size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">AI服务器设置</span>
                </div>
                <div
                  onClick={() => {
                    setActiveSetting('rag');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'rag'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Database size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">RAG管理</span>
                </div>
                <div
                  onClick={() => {
                    setActiveSetting('mcp');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'mcp'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Server size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">MCP服务</span>
                </div>
              </div>
            )}

            {activeTab === 'assistant' && (
              <div className="p-2">
                {/* 始终显示会话列表 */}
                {sessions && sessions.map((session) => {
                  const lastMessage = session.messages.length > 0
                    ? session.messages[session.messages.length - 1].content
                    : '新会话';
                  
                  return (
                    <div
                      key={session.id}
                      onClick={() => onSessionSelect && onSessionSelect(session.id)}
                      className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                        session.id === activeSessionId
                          ? 'bg-blue-50 border border-blue-200'
                          : 'hover:bg-white hover:shadow-sm'
                      }`}
                    >
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-lg flex-shrink-0">
                        😊
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {session.name}
                        </div>
                        <div className="text-xs text-gray-400 mt-1 line-clamp-2">
                          {lastMessage}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {activeTab === 'functions' && (
              <div className="p-2">
                {Object.entries(features)
                  .filter(([key]) => key !== 'settings' && key !== 'agent') // 过滤掉设置功能和智能体
                  .map(([key, feature]) => {
                  const IconComponent = featureIcons[key] || FileText;
                  return (
                    <div
                      key={key}
                      onClick={() => {
                        onFeatureSelect(key);
                        setActiveTab('functions'); // 确保点击功能选项时，功能tab保持激活状态
                      }}
                      className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                        activeFeature === key
                          ? 'bg-blue-50 border border-blue-200 text-blue-700'
                          : 'hover:bg-white hover:shadow-sm text-gray-700'
                      }`}
                    >
                      <IconComponent size={18} className="flex-shrink-0" />
                      <span className="text-sm font-medium">{feature.name}</span>
                    </div>
                  );
                })}
              </div>
            )}

          </div>

          {/* 底部添加按钮 */}
          {activeTab === 'assistant' && (
            <div className="p-4 border-t border-gray-200 bg-white">
              <button
                onClick={onNewSession}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors border border-gray-200"
              >
                <Plus size={16} />
                <span>新建对话</span>
              </button>
            </div>
          )}
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          {/* 顶部状态栏 - 类似 Cherry Studio */}
          <div className="h-14 border-b border-gray-200 flex items-center justify-between px-6 bg-white/95 backdrop-blur-sm drag-region relative z-[50]">
            <div className="flex items-center space-x-4">
              <div className="relative ai-service-selector z-[99999]">
                <div
                  className="flex items-center space-x-3 px-3 py-1.5 bg-gray-100 rounded-lg no-drag cursor-pointer hover:bg-gray-200 transition-colors"
                  onClick={() => setShowAIServiceSelector(!showAIServiceSelector)}
                >
                  <Sparkles size={14} className="text-blue-500" />
                  <div className="w-1.5 h-1. bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 font-medium">智能助手</span>
                  <span className="text-xs text-gray-400">|</span>
                  <span className="text-xs text-gray-500">{getCurrentAIServiceName()}</span>
                  <ChevronDown size={14} className="text-gray-500" />
                </div>
                
                {/* AI服务选择器下拉菜单 */}
                {showAIServiceSelector && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 z-[999999] no-drag">
                    <div className="p-2">
                      <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 py-1">
                        选择AI服务
                      </div>
                      
                      {/* 远程AI服务 - 只在启用时显示 */}
                      {aiConfig?.remote_enabled && (
                        <div
                          className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                            activeAIService === 'remote'
                              ? 'bg-blue-50 border border-blue-200'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => handleAIServiceSwitch('remote')}
                        >
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            activeAIService === 'remote' ? 'bg-blue-100' : 'bg-gray-200'
                          }`}>
                            <Cloud size={16} className={activeAIService === 'remote' ? 'text-blue-600' : 'text-gray-500'} />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">远程AI服务</div>
                            <div className="text-xs text-gray-500">{aiConfig?.model || 'ERNIE-4.5'}</div>
                          </div>
                          {activeAIService === 'remote' && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      )}
                      
                      {/* 本地AI服务 - 只在启用时显示 */}
                      {aiConfig?.local_enabled && (
                        <div
                          className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                            activeAIService === 'local'
                              ? 'bg-green-50 border border-green-200'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => handleAIServiceSwitch('local')}
                        >
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            activeAIService === 'local' ? 'bg-green-100' : 'bg-gray-200'
                          }`}>
                            <HardDrive size={16} className={activeAIService === 'local' ? 'text-green-600' : 'text-gray-500'} />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">本地AI服务 (Ollama)</div>
                            <div className="text-xs text-gray-500">{aiConfig?.ollama_model || 'llama3.1:8b'}</div>
                          </div>
                          {activeAIService === 'local' && (
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          )}
                        </div>
                      )}
                      
                      {/* 当没有启用的服务时显示提示 */}
                      {!aiConfig?.remote_enabled && !aiConfig?.local_enabled && (
                        <div className="p-3 text-center">
                          <div className="text-sm text-gray-500 mb-2">暂无可用的AI服务</div>
                          <div className="text-xs text-gray-400">请先在设置中启用AI服务</div>
                        </div>
                      )}
                      
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <button
                          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          onClick={() => {
                            setActiveSetting('settings');
                            setActiveTab('');
                            onFeatureSelect('settings');
                            setShowAIServiceSelector(false);
                          }}
                        >
                          <Settings size={14} />
                          <span>AI服务设置</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-1 no-drag">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                <Search size={18} />
              </button>
              <button
                onClick={() => {
                  // 触发工作区显示/隐藏事件
                  if (typeof window !== 'undefined') {
                    const event = new CustomEvent('toggle-workspace-visibility');
                    window.dispatchEvent(event);
                  }
                }}
                className={`p-2 rounded-lg transition-colors ${
                  isWorkspaceVisible
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                }`}
                title={isWorkspaceVisible ? "隐藏工作区" : "显示工作区"}
              >
                <LayoutGrid size={18} />
              </button>
              <button
                onClick={() => {
                  setActiveSetting('settings'); // 默认选中AI服务器设置
                  setActiveTab(''); // 清除左侧tab页的focus
                  onFeatureSelect('settings'); // 激活设置功能
                }}
                className={`p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors ${
                  activeFeature === 'settings' ? 'text-blue-600 bg-blue-50' : ''
                }`}
              >
                <Settings size={18} />
              </button>
              
              {/* 窗口控制按钮 */}
              {isElectron && (
                <>
                  <div className="w-px h-6 bg-gray-300 mx-2"></div>
                  <button
                    onClick={handleMinimize}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="最小化"
                  >
                    <Minus size={16} />
                  </button>
                  <button
                    onClick={handleMaximize}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="最大化"
                  >
                    <Square size={16} />
                  </button>
                  <button
                    onClick={handleClose}
                    className="p-2 text-gray-400 hover:text-white hover:bg-red-500 rounded-lg transition-colors"
                    title="关闭"
                  >
                    <X size={16} />
                  </button>
                </>
              )}
            </div>
          </div>

          {/* 主内容 */}
          <div className="flex-1 overflow-hidden">
            {activeFeature === 'settings' ? (
              <div className="h-full">
                {activeSetting === 'settings' && (
                  <SettingsManager
                    activeSetting={activeSetting}
                    aiConfig={aiConfig}
                    setAiConfig={setAiConfig}
                    backendBase={backendBase}
                  />
                )}
                {activeSetting === 'rag' && (
                  <div className="p-8 h-full overflow-hidden">
                    <RAGManagement
                      backendBase={backendBase}
                      aiConfig={aiConfig}
                    />
                  </div>
                )}
                {activeSetting === 'mcp' && (
                  <div className="p-8 h-full">
                    <MCPManagement />
                  </div>
                )}
              </div>
            ) : (
              children
            )}
          </div>
        </div>
      </div>
      
      {/* 底部状态栏 - 全页面宽度 */}
      <StatusBar />
    </div>
  );
};

export default ElectronMainLayout;