import React, { useState, useEffect } from 'react';
import { Terminal, Network, Cpu, HardDrive } from 'lucide-react';

const StatusBar = () => {
  const isElectron = typeof window !== 'undefined' && window.electronAPI;
  const [systemInfo, setSystemInfo] = useState({
    memory: { usage: 0, used: 0, total: 0, heapUsed: 0, heapTotal: 0 },
    system: { platform: '', arch: '', uptime: 0 }
  });

  useEffect(() => {
    const fetchSystemInfo = async () => {
      if (isElectron) {
        try {
          const info = await window.electronAPI.getSystemInfo();
          setSystemInfo(info);
        } catch (error) {
          console.error('Failed to fetch system info:', error);
        }
      }
    };

    fetchSystemInfo();
    
    // 每5秒更新一次系统信息
    const interval = setInterval(fetchSystemInfo, 5000);
    
    return () => clearInterval(interval);
  }, [isElectron]);

  const handleToggleConsole = () => {
    if (isElectron) {
      window.electronAPI.toggleConsole();
    }
  };

  // 格式化运行时间
  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="h-8 bg-gray-900 text-gray-400 text-xs flex items-center justify-between px-4 border-t border-gray-800 select-none">
      {/* 左侧状态信息 */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>就绪</span>
        </div>
        <div className="flex items-center space-x-1">
          <Network size={12} />
          <span>已连接</span>
        </div>
      </div>

      {/* 中间软件信息 */}
      <div className="flex items-center px-3 py-1 bg-gray-800 rounded-md">
        <span className="text-gray-100 font-medium">财务集成开发环境</span>
        <span className="text-gray-400 ml-2">v0.0.0</span>
      </div>

      {/* 右侧操作按钮 */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <span>运行: {formatUptime(systemInfo.system.uptime)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <HardDrive size={12} />
          <span>内存: {systemInfo.memory.usage}%</span>
        </div>
        <button
          onClick={handleToggleConsole}
          className="flex items-center space-x-1 px-2 py-1 hover:bg-gray-800 rounded transition-colors"
          title="打开/关闭控制台"
        >
          <Terminal size={12} />
          <span>控制台</span>
        </button>
      </div>
    </div>
  );
};

export default StatusBar;