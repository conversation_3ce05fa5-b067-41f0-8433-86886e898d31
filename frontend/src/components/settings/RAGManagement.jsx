import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, Search, FileText, Database, Upload } from 'lucide-react';

const RAGManagement = ({ backendBase, aiConfig }) => {
  const [ragData, setRagData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: '',
    source: ''
  });
  const [toast, setToast] = useState(null);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  // 显示提示信息
  const showToast = (message, type = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // 获取RAG数据列表
  const fetchRAGData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/list`, {
        method: 'GET',
        headers: {
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setRagData(result.data || []);
      } else {
        showToast(`获取RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`获取RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 上传规章制度文档
  const uploadRegulationDocument = async () => {
    if (!uploadFile) {
      showToast('请选择要上传的文档', 'error');
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', uploadFile);

      const response = await fetch(`${backendBase}/agent/v2/audit/rag/upload`, {
        method: 'POST',
        headers: {
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast(`文档上传成功：${result.message}`);
        setShowUploadForm(false);
        setUploadFile(null);
        fetchRAGData();
      } else {
        showToast(`文档上传失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`文档上传失败: ${error.message}`, 'error');
    } finally {
      setIsUploading(false);
    }
  };

  // 添加RAG数据
  const addRAGData = async () => {
    if (!formData.title || !formData.content) {
      showToast('请填写标题和内容', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          data: [formData]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据添加成功');
        setShowAddForm(false);
        setFormData({ title: '', content: '', category: '', source: '' });
        fetchRAGData();
      } else {
        showToast(`添加RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`添加RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新RAG数据
  const updateRAGData = async () => {
    if (!formData.title || !formData.content) {
      showToast('请填写标题和内容', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/${editingItem.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          data: formData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据更新成功');
        setEditingItem(null);
        setFormData({ title: '', content: '', category: '', source: '' });
        fetchRAGData();
      } else {
        showToast(`更新RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`更新RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除RAG数据
  const deleteRAGData = async (id) => {
    if (!window.confirm('确定要删除这条RAG数据吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/${id}`, {
        method: 'DELETE',
        headers: {
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据删除成功');
        fetchRAGData();
      } else {
        showToast(`删除RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`删除RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 开始编辑
  const startEdit = (item) => {
    setEditingItem(item);
    setFormData({
      title: item.metadata.title,
      content: item.content,
      category: item.metadata.category,
      source: item.metadata.source
    });
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingItem(null);
    setFormData({ title: '', content: '', category: '', source: '' });
  };

  // 处理表单变化
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 提交表单
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingItem) {
      updateRAGData();
    } else {
      addRAGData();
    }
  };

  // 处理文件选择
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setUploadFile(e.target.files[0]);
    }
  };

  // 过滤数据
  const filteredData = ragData.filter(item => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.metadata.title.toLowerCase().includes(searchLower) ||
      item.content.toLowerCase().includes(searchLower) ||
      item.metadata.category.toLowerCase().includes(searchLower)
    );
  });

  // 初始化
  useEffect(() => {
    fetchRAGData();
  }, []);

  return (
    <div className="flex flex-col h-full">
        {/* 头部 */}
        <div className="mb-6 flex-shrink-0">
          <p className="text-sm text-gray-600">管理单据审核相关的规章制度数据</p>
        </div>

        {/* 搜索和添加按钮 */}
        <div className="flex items-center justify-between mb-6 flex-shrink-0">
          <div className="relative w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
              placeholder="搜索规章制度..."
            />
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowUploadForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Upload size={18} />
              <span>上传文档</span>
            </button>
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Plus size={18} />
              <span>添加规章制度</span>
            </button>
          </div>
        </div>

        {/* 上传表单 */}
        {showUploadForm && (
          <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
            <h3 className="text-lg font-semibold mb-4">上传规章制度文档</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  选择文档 <span className="text-red-500">*</span>
                </label>
                <input
                  type="file"
                  onChange={handleFileChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 transition-all duration-200"
                  accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.bmp,.tiff,.tif,.webp"
                />
                <p className="mt-1 text-sm text-gray-500">支持PDF、Word、TXT文件和图片格式</p>
              </div>
              {uploadFile && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">已选择文件: {uploadFile.name}</p>
                </div>
              )}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowUploadForm(false);
                    setUploadFile(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="button"
                  onClick={uploadRegulationDocument}
                  disabled={isUploading || !uploadFile}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUploading ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <Upload size={16} />
                      <span>上传并解析</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 表单 */}
        {(showAddForm || editingItem) && (
          <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
            <h3 className="text-lg font-semibold mb-4">
              {editingItem ? '编辑规章制度' : '添加规章制度'}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    标题 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleFormChange}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入标题"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    分类
                  </label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleFormChange}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入分类"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  来源
                </label>
                <input
                  type="text"
                  name="source"
                  value={formData.source}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                  placeholder="请输入来源"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  内容 <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="content"
                  value={formData.content}
                  onChange={handleFormChange}
                  rows={4}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                  placeholder="请输入内容"
                  required
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    cancelEdit();
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <Save size={16} />
                      <span>{editingItem ? '更新' : '添加'}</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* 数据列表 */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-8 h-8 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"></div>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <FileText size={48} className="mb-4" />
              <p className="text-lg">暂无RAG数据</p>
              <p className="text-sm">点击"上传文档"或"添加规章制度"按钮添加数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredData.map((item) => (
                <div key={item.id} className="p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-all duration-200">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{item.metadata.title}</h3>
                        {item.metadata.category && (
                          <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                            {item.metadata.category}
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 mb-2 line-clamp-3">{item.content}</p>
                      {item.metadata.source && (
                        <p className="text-sm text-gray-500">来源: {item.metadata.source}</p>
                      )}
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <button
                        onClick={() => startEdit(item)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-200"
                        title="编辑"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => deleteRAGData(item.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200"
                        title="删除"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Toast */}
        {toast && (
          <div className={`fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white ${
            toast.type === 'error' ? 'bg-red-500' :
            toast.type === 'success' ? 'bg-green-500' :
            'bg-blue-500'
          }`}>
            {toast.message}
          </div>
        )}
    </div>
  );
};

export default RAGManagement;