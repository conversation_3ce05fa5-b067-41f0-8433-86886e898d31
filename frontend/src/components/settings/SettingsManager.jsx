import React, { useState } from 'react';
import { Save, TestTube, Settings as SettingsIcon, Sparkles, Database, Server } from 'lucide-react';
import RAGManagement from './RAGManagement';
import MCPManagement from './MCPManagement';

const SettingsManager = ({ activeSetting, aiConfig, setAiConfig, backendBase }) => {
  // 使用 aiConfig 作为配置源，如果不存在则使用默认值
  const config = {
    api_key: aiConfig?.api_key || '',
    base_url: aiConfig?.base_url || 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: aiConfig?.model || 'ernie-4.5-turbo-vl-preview',
    use_ollama: aiConfig?.use_ollama || false,
    remote_enabled: aiConfig?.remote_enabled ?? true, // 默认启用远程服务
    local_enabled: aiConfig?.local_enabled || false, // 默认禁用本地服务
    ollama_base_url: aiConfig?.ollama_base_url || 'http://localhost:11434',
    ollama_model: aiConfig?.ollama_model || 'llama3.1:8b',
    remote_connection_status: aiConfig?.remote_connection_status || 'none',
    local_connection_status: aiConfig?.local_connection_status || 'none'
  };

  // 更新配置的函数
  const updateConfig = (updates) => {
    const newConfig = { ...config, ...updates };
    setAiConfig(newConfig);
    localStorage.setItem('aiConfig', JSON.stringify(newConfig));
  };
  
  const [isTesting, setIsTesting] = useState(false);
  const [testingService, setTestingService] = useState(null); // 'remote', 'local', or null
  const [testResult, setTestResult] = useState(null);

  const handleSave = async () => {
    // 如果远程服务已启用，先测试连接
    if (config.remote_enabled) {
      try {
        const testConfig = {
          api_key: config.api_key,
          base_url: config.base_url,
          model: config.model,
          use_ollama: false
        };
        
        const response = await fetch(`${backendBase}/ai/test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testConfig),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || '连接测试失败');
        }
        
        // 更新连接状态为成功
        updateConfig({ remote_connection_status: 'success' });
        
        // 显示临时成功消息
        setTestResult({
          success: true,
          message: '远程AI服务连接测试成功！'
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
      } catch (error) {
        // 更新连接状态为错误
        updateConfig({ remote_connection_status: 'error' });
        
        // 显示临时错误消息
        setTestResult({
          success: false,
          message: `远程AI服务连接测试失败: ${error.message || '未知错误'}，请检查配置后重试`
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
        
        // 连接测试失败，不保存配置
        return;
      }
    }
    
    // 如果本地服务已启用，先测试连接
    if (config.local_enabled) {
      try {
        const testConfig = {
          ollama_base_url: config.ollama_base_url,
          ollama_model: config.ollama_model,
          use_ollama: true
        };
        
        const response = await fetch(`${backendBase}/ai/test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testConfig),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || '连接测试失败');
        }
        
        // 更新连接状态为成功
        updateConfig({ local_connection_status: 'success' });
        
        // 显示临时成功消息
        setTestResult({
          success: true,
          message: '本地AI服务连接测试成功！'
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
      } catch (error) {
        // 更新连接状态为错误
        updateConfig({ local_connection_status: 'error' });
        
        // 显示临时错误消息
        setTestResult({
          success: false,
          message: `本地AI服务连接测试失败: ${error.message || '未知错误'}，请检查配置后重试`
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
        
        // 连接测试失败，不保存配置
        return;
      }
    }
    
    // 所有启用的服务连接测试通过，保存配置
    // 根据local_enabled和remote_enabled设置use_ollama
    const finalConfig = {
      ...config,
      use_ollama: config.local_enabled && !config.remote_enabled
    };
    
    updateConfig(finalConfig);
    
    // 显示保存成功消息
    setTestResult({
      success: true,
      message: '配置保存成功！'
    });
    
    // 3秒后清除消息
    setTimeout(() => {
      setTestResult(null);
    }, 3000);
  };

  const handleTest = async (serviceType) => {
    setIsTesting(true);
    setTestingService(serviceType);
    try {
      // 根据服务类型构建测试配置
      const testConfig = serviceType === 'remote'
        ? {
            api_key: config.api_key,
            base_url: config.base_url,
            model: config.model,
            use_ollama: false
          }
        : {
            ollama_base_url: config.ollama_base_url,
            ollama_model: config.ollama_model,
            use_ollama: true
          };
      
      const response = await fetch(`${backendBase}/ai/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfig),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '连接测试失败');
      }
      
      // 更新连接状态为成功
      if (serviceType === 'remote') {
        updateConfig({ remote_connection_status: 'success' });
      } else {
        updateConfig({ local_connection_status: 'success' });
      }
      
      // 显示临时成功消息
      setTestResult({
        success: true,
        message: `${serviceType === 'remote' ? '远程' : '本地'}AI服务连接测试成功！`
      });
      
      // 3秒后清除消息
      setTimeout(() => {
        setTestResult(null);
      }, 3000);
    } catch (error) {
      // 更新连接状态为错误
      if (serviceType === 'remote') {
        updateConfig({ remote_connection_status: 'error' });
      } else {
        updateConfig({ local_connection_status: 'error' });
      }
      
      // 显示临时错误消息
      setTestResult({
        success: false,
        message: `${serviceType === 'remote' ? '远程' : '本地'}AI服务连接测试失败: ${error.message || '未知错误'}`
      });
      
      // 3秒后清除消息
      setTimeout(() => {
        setTestResult(null);
      }, 3000);
    } finally {
      setIsTesting(false);
      setTestingService(null);
    }
  };

  // AI服务器设置内容
  const renderAISettings = () => (
    <div className="flex flex-col">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center space-x-3">
          <div>
            <p className="text-sm text-gray-600">配置您的AI服务连接</p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {/* AI服务选择 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">AI服务配置</h3>
          
          {/* 远程AI服务 */}
          <div className={`space-y-4 p-4 rounded-xl border transition-all duration-200 ${
            config.remote_enabled
              ? 'bg-blue-50 border-blue-200'
              : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  config.remote_enabled
                    ? config.remote_connection_status === 'success'
                      ? 'bg-green-100'
                      : config.remote_connection_status === 'error'
                        ? 'bg-red-100'
                        : 'bg-blue-100'
                    : 'bg-gray-200'
                }`}>
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    config.remote_enabled
                      ? config.remote_connection_status === 'success'
                        ? 'border-green-500 bg-green-500'
                        : config.remote_connection_status === 'error'
                          ? 'border-red-500 bg-red-500'
                          : 'border-blue-500 bg-blue-500'
                      : 'border-gray-400'
                  }`}></div>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">远程AI服务</h4>
                  <p className="text-xs text-gray-500">使用云端AI服务</p>
                </div>
              </div>
              <div className="relative inline-block w-12 h-6">
                <input
                  type="checkbox"
                  checked={config.remote_enabled}
                  onChange={(e) => updateConfig({ remote_enabled: e.target.checked })}
                  className="opacity-0 w-0 h-0 peer"
                  id="remote-service-toggle"
                />
                <label
                  htmlFor="remote-service-toggle"
                  className="absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-all duration-300 peer-checked:bg-blue-600 peer-checked:before:translate-x-6 before:absolute before:h-4 before:w-4 before:left-1 before:bottom-1 before:bg-white before:rounded-full before:transition-all before:duration-300"
                />
              </div>
            </div>
            
            {/* 远程服务配置 (仅在启用远程服务时显示) */}
            {config.remote_enabled && (
              <div className="space-y-4 mt-4">
                {/* API Key */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    API Key
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="password"
                    value={config.api_key}
                    onChange={(e) => updateConfig({ api_key: e.target.value })}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入您的API Key"
                  />
                </div>

                {/* 基础URL */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    基础URL
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.base_url}
                    onChange={(e) => updateConfig({ base_url: e.target.value })}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入API基础URL"
                  />
                </div>

                {/* 模型名称 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    模型名称
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.model}
                    onChange={(e) => updateConfig({ model: e.target.value })}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入模型名称"
                  />
                </div>
                
                {/* 远程服务操作按钮 */}
                <div className="flex space-x-3 mt-4 pt-4 border-t border-blue-200">
                  <button
                    onClick={() => handleTest('remote')}
                    disabled={isTesting || testingService === 'local'}
                    className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white text-sm font-semibold rounded-lg shadow hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    {isTesting ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span>测试中</span>
                      </div>
                    ) : (
                      <>
                        <TestTube size={14} className="mr-1" />
                        测试连接
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={handleSave}
                    className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white text-sm font-semibold rounded-lg shadow hover:shadow-lg transition-all duration-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                  >
                    <Save size={14} className="mr-1" />
                    保存设置
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* 本地AI服务 */}
          <div className={`space-y-4 p-4 rounded-xl border transition-all duration-200 ${
            config.local_enabled
              ? 'bg-green-50 border-green-200'
              : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  config.local_enabled
                    ? config.local_connection_status === 'success'
                      ? 'bg-green-100'
                      : config.local_connection_status === 'error'
                        ? 'bg-red-100'
                        : 'bg-green-100'
                    : 'bg-gray-200'
                }`}>
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    config.local_enabled
                      ? config.local_connection_status === 'success'
                        ? 'border-green-500 bg-green-500'
                        : config.local_connection_status === 'error'
                          ? 'border-red-500 bg-red-500'
                          : 'border-green-500 bg-green-500'
                      : 'border-gray-400'
                  }`}></div>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">本地AI服务 (Ollama)</h4>
                  <p className="text-xs text-gray-500">使用本地运行的AI模型</p>
                </div>
              </div>
              <div className="relative inline-block w-12 h-6">
                <input
                  type="checkbox"
                  checked={config.local_enabled}
                  onChange={(e) => updateConfig({ local_enabled: e.target.checked })}
                  className="opacity-0 w-0 h-0 peer"
                  id="local-service-toggle"
                />
                <label
                  htmlFor="local-service-toggle"
                  className="absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-all duration-300 peer-checked:bg-green-600 peer-checked:before:translate-x-6 before:absolute before:h-4 before:w-4 before:left-1 before:bottom-1 before:bg-white before:rounded-full before:transition-all before:duration-300"
                />
              </div>
            </div>
            
            {/* 本地服务配置 (仅在启用本地服务时显示) */}
            {config.local_enabled && (
              <div className="space-y-4 mt-4">
                {/* Ollama基础URL */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Ollama服务地址
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.ollama_base_url}
                    onChange={(e) => updateConfig({ ollama_base_url: e.target.value })}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="http://localhost:11434"
                  />
                </div>

                {/* Ollama模型名称 */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Ollama模型名称
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.ollama_model}
                    onChange={(e) => updateConfig({ ollama_model: e.target.value })}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="llama3.1:8b"
                  />
                </div>

                <div className="p-3 bg-green-100 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>提示：</strong>请确保Ollama服务已启动并已下载相应的模型。可以使用命令 <code className="bg-green-200 px-1 rounded">ollama pull {config.ollama_model}</code> 下载模型。
                  </p>
                </div>
                
                {/* 本地服务操作按钮 */}
                <div className="flex space-x-3 mt-4 pt-4 border-t border-green-200">
                  <button
                    onClick={() => handleTest('local')}
                    disabled={isTesting || testingService === 'remote'}
                    className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white text-sm font-semibold rounded-lg shadow hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    {isTesting ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span>测试中</span>
                      </div>
                    ) : (
                      <>
                        <TestTube size={14} className="mr-1" />
                        测试连接
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={handleSave}
                    className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white text-sm font-semibold rounded-lg shadow hover:shadow-lg transition-all duration-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                  >
                    <Save size={14} className="mr-1" />
                    保存设置
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 测试结果 */}
        {testResult && (
          <div className={`p-4 rounded-xl border ${
            testResult.success
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center space-x-2">
              <Sparkles size={16} className={testResult.success ? 'text-green-600' : 'text-red-600'} />
              <div className="font-semibold">{testResult.success ? '连接成功' : '连接失败'}</div>
            </div>
            <div className="text-sm mt-1 opacity-90">{testResult.message}</div>
          </div>
        )}

      </div>
    </div>
  );

  // RAG管理内容
  const renderRAGManagement = () => (
    <div>
      <RAGManagement
        backendBase={backendBase}
        aiConfig={config}
      />
    </div>
  );

  // MCP服务管理内容
  const renderMCPManagement = () => (
    <div>
      <MCPManagement />
    </div>
  );

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden relative">
      <div className="flex-1 overflow-auto p-8">
        {activeSetting === 'settings' && renderAISettings()}
        {activeSetting === 'rag' && renderRAGManagement()}
        {activeSetting === 'mcp' && renderMCPManagement()}
      </div>
    </div>
  );
};

export default SettingsManager;