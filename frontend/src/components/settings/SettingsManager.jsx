import React, { useState } from 'react';
import { Save, TestTube, Settings as SettingsIcon, Sparkles, Database, Server } from 'lucide-react';
import RAGManagement from './RAGManagement';
import MCPManagement from './MCPManagement';
import AIServiceManager from './AIServiceManager';

const SettingsManager = ({ activeSetting, aiConfig, setAiConfig, backendBase }) => {
  // 使用 aiConfig 作为配置源，如果不存在则使用默认值
  const config = {
    api_key: aiConfig?.api_key || '',
    base_url: aiConfig?.base_url || 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: aiConfig?.model || 'ernie-4.5-turbo-vl-preview',
    use_ollama: aiConfig?.use_ollama || false,
    remote_enabled: aiConfig?.remote_enabled ?? true, // 默认启用远程服务
    local_enabled: aiConfig?.local_enabled || false, // 默认禁用本地服务
    ollama_base_url: aiConfig?.ollama_base_url || 'http://localhost:11434',
    ollama_model: aiConfig?.ollama_model || 'llama3.1:8b',
    remote_connection_status: aiConfig?.remote_connection_status || 'none',
    local_connection_status: aiConfig?.local_connection_status || 'none'
  };

  // 更新配置的函数
  const updateConfig = (updates) => {
    const newConfig = { ...config, ...updates };
    setAiConfig(newConfig);
    localStorage.setItem('aiConfig', JSON.stringify(newConfig));
  };
  
  const [isTesting, setIsTesting] = useState(false);
  const [testingService, setTestingService] = useState(null); // 'remote', 'local', or null
  const [testResult, setTestResult] = useState(null);

  const handleSave = async () => {
    // 如果远程服务已启用，先测试连接
    if (config.remote_enabled) {
      try {
        const testConfig = {
          api_key: config.api_key,
          base_url: config.base_url,
          model: config.model,
          use_ollama: false
        };
        
        const response = await fetch(`${backendBase}/ai/test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testConfig),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || '连接测试失败');
        }
        
        // 更新连接状态为成功
        updateConfig({ remote_connection_status: 'success' });
        
        // 显示临时成功消息
        setTestResult({
          success: true,
          message: '远程AI服务连接测试成功！'
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
      } catch (error) {
        // 更新连接状态为错误
        updateConfig({ remote_connection_status: 'error' });
        
        // 显示临时错误消息
        setTestResult({
          success: false,
          message: `远程AI服务连接测试失败: ${error.message || '未知错误'}，请检查配置后重试`
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
        
        // 连接测试失败，不保存配置
        return;
      }
    }
    
    // 如果本地服务已启用，先测试连接
    if (config.local_enabled) {
      try {
        const testConfig = {
          ollama_base_url: config.ollama_base_url,
          ollama_model: config.ollama_model,
          use_ollama: true
        };
        
        const response = await fetch(`${backendBase}/ai/test`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testConfig),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || '连接测试失败');
        }
        
        // 更新连接状态为成功
        updateConfig({ local_connection_status: 'success' });
        
        // 显示临时成功消息
        setTestResult({
          success: true,
          message: '本地AI服务连接测试成功！'
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
      } catch (error) {
        // 更新连接状态为错误
        updateConfig({ local_connection_status: 'error' });
        
        // 显示临时错误消息
        setTestResult({
          success: false,
          message: `本地AI服务连接测试失败: ${error.message || '未知错误'}，请检查配置后重试`
        });
        
        // 3秒后清除消息
        setTimeout(() => {
          setTestResult(null);
        }, 3000);
        
        // 连接测试失败，不保存配置
        return;
      }
    }
    
    // 所有启用的服务连接测试通过，保存配置
    // 根据local_enabled和remote_enabled设置use_ollama
    const finalConfig = {
      ...config,
      use_ollama: config.local_enabled && !config.remote_enabled
    };
    
    updateConfig(finalConfig);
    
    // 显示保存成功消息
    setTestResult({
      success: true,
      message: '配置保存成功！'
    });
    
    // 3秒后清除消息
    setTimeout(() => {
      setTestResult(null);
    }, 3000);
  };

  const handleTest = async (serviceType) => {
    setIsTesting(true);
    setTestingService(serviceType);
    try {
      // 根据服务类型构建测试配置
      const testConfig = serviceType === 'remote'
        ? {
            api_key: config.api_key,
            base_url: config.base_url,
            model: config.model,
            use_ollama: false
          }
        : {
            ollama_base_url: config.ollama_base_url,
            ollama_model: config.ollama_model,
            use_ollama: true
          };
      
      const response = await fetch(`${backendBase}/ai/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfig),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '连接测试失败');
      }
      
      // 更新连接状态为成功
      if (serviceType === 'remote') {
        updateConfig({ remote_connection_status: 'success' });
      } else {
        updateConfig({ local_connection_status: 'success' });
      }
      
      // 显示临时成功消息
      setTestResult({
        success: true,
        message: `${serviceType === 'remote' ? '远程' : '本地'}AI服务连接测试成功！`
      });
      
      // 3秒后清除消息
      setTimeout(() => {
        setTestResult(null);
      }, 3000);
    } catch (error) {
      // 更新连接状态为错误
      if (serviceType === 'remote') {
        updateConfig({ remote_connection_status: 'error' });
      } else {
        updateConfig({ local_connection_status: 'error' });
      }
      
      // 显示临时错误消息
      setTestResult({
        success: false,
        message: `${serviceType === 'remote' ? '远程' : '本地'}AI服务连接测试失败: ${error.message || '未知错误'}`
      });
      
      // 3秒后清除消息
      setTimeout(() => {
        setTestResult(null);
      }, 3000);
    } finally {
      setIsTesting(false);
      setTestingService(null);
    }
  };

  // AI服务器设置内容
  const renderAISettings = () => (
    <AIServiceManager
      aiConfig={aiConfig}
      setAiConfig={setAiConfig}
      backendBase={backendBase}
    />
  );
  );

  // RAG管理内容
  const renderRAGManagement = () => (
    <div>
      <RAGManagement
        backendBase={backendBase}
        aiConfig={config}
      />
    </div>
  );

  // MCP服务管理内容
  const renderMCPManagement = () => (
    <div>
      <MCPManagement />
    </div>
  );

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden relative">
      <div className="flex-1 overflow-auto p-8">
        {activeSetting === 'settings' && renderAISettings()}
        {activeSetting === 'rag' && renderRAGManagement()}
        {activeSetting === 'mcp' && renderMCPManagement()}
      </div>
    </div>
  );
};

export default SettingsManager;