import React from 'react'
import { MessageCircle, Loader } from 'lucide-react'

const ConversationList = ({ sessions, activeId }) => {
  const activeSession = sessions.find(s => s.id === activeId)
  const sessionIndex = sessions.findIndex(s => s.id === activeId)
  
  if (!activeSession) return null

  return (
    <div className="border-b border-white/10 p-4">
      <div className="flex items-center justify-center px-4 py-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-600/10 border border-blue-300/20">
        <div className="flex items-center min-w-0 flex-1 justify-center">
          <MessageCircle size={16} className="mr-2 flex-shrink-0 text-blue-600" />
          <span className="truncate font-medium text-gray-800">
            {activeSession.name || `对话 ${sessionIndex + 1}`}
          </span>
          {activeSession.isLoading && (
            <Loader className="animate-spin text-blue-500 ml-2" size={14} />
          )}
        </div>
      </div>
    </div>
  )
}

export default ConversationList 