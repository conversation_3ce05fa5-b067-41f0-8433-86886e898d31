import React, { useState, useRef, useCallback } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { useDropzone } from 'react-dropzone';
import { 
  Send, 
  Paperclip, 
  Image, 
  FileText, 
  X, 
  Plus,
  Smile,
  Hash,
  AtSign,
  StopCircle
} from 'lucide-react';

const ModernChatInput = ({
  value,
  onChange,
  onSend,
  onFileSelect,
  uploadedFiles = [],
  onRemoveFile,
  isLoading = false,
  isStreaming = false,
  onStop,
  placeholder = "Ask a question or describe a task...",
  disabled = false
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [showFileMenu, setShowFileMenu] = useState(false);
  const fileInputRef = useRef(null);
  const imageInputRef = useRef(null);

  // 处理文件拖拽
  const onDrop = useCallback((acceptedFiles) => {
    if (onFileSelect) {
      onFileSelect(acceptedFiles);
    }
    setIsDragOver(false);
  }, [onFileSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'application/pdf': ['.pdf'],
      'text/*': ['.txt', '.md'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    }
  });

  // 处理键盘事件
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (value.trim() || uploadedFiles.length > 0) {
        onSend();
      }
    }
  };

  // 处理文件选择
  const handleFileSelect = (type) => {
    if (type === 'image') {
      imageInputRef.current?.click();
    } else {
      fileInputRef.current?.click();
    }
    setShowFileMenu(false);
  };

  // 重置文件输入框的值，解决重复选择问题
  const resetFileInput = (inputRef) => {
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const handleFileInputChange = (e, type) => {
    const files = Array.from(e.target.files);
    if (files.length > 0 && onFileSelect) {
      onFileSelect(files);
    }
    // 重置输入框值，允许重新选择相同文件
    resetFileInput(type === 'image' ? imageInputRef : fileInputRef);
  };

  return (
    <div className="relative">
      {/* 拖拽覆盖层 */}
      {isDragActive && (
        <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-2xl flex items-center justify-center z-10 backdrop-blur-sm">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <Plus className="w-6 h-6 text-white" />
            </div>
            <p className="text-blue-600 font-medium">拖拽文件到这里上传</p>
          </div>
        </div>
      )}

      {/* 主输入区域 */}
      <div 
        {...getRootProps()}
        className={`relative bg-white/95 backdrop-blur-sm border rounded-2xl shadow-lg transition-all duration-200 ${
          isDragActive ? 'border-blue-500 shadow-blue-500/20' : 'border-gray-200 hover:border-gray-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} />
        
        {/* 已上传文件显示 */}
        {uploadedFiles.length > 0 && (
          <div className="p-3 border-b border-gray-100">
            <div className="flex flex-wrap gap-2">
              {uploadedFiles.map((file, index) => (
                <div 
                  key={file.id || index} 
                  className="flex items-center gap-2 bg-gray-50 hover:bg-gray-100 rounded-xl px-3 py-2 text-sm transition-colors group"
                >
                  <div className="flex items-center gap-2">
                    {file.type?.startsWith('image/') ? (
                      <Image size={16} className="text-blue-500" />
                    ) : (
                      <FileText size={16} className="text-gray-500" />
                    )}
                    <span className="text-gray-700 max-w-32 truncate">
                      {file.name}
                    </span>
                    <span className="text-xs text-gray-400">
                      ({(file.size / 1024).toFixed(1)}KB)
                    </span>
                  </div>
                  <button
                    onClick={() => onRemoveFile && onRemoveFile(index)}
                    className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all duration-200 p-1 hover:bg-red-50 rounded-full"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 输入框区域 */}
        <div className="flex items-end gap-3 p-4">
          {/* 左侧功能按钮 */}
          <div className="flex items-center gap-1">
            {/* 文件上传按钮 */}
            <div className="relative">
              <button
                onClick={() => setShowFileMenu(!showFileMenu)}
                disabled={disabled}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-200 disabled:opacity-50"
                title="上传文件"
              >
                <Paperclip size={20} />
              </button>

              {/* 文件菜单 */}
              {showFileMenu && (
                <div className="absolute bottom-full left-0 mb-2 bg-white rounded-xl shadow-lg border border-gray-200 py-2 min-w-40 z-20">
                  <button
                    onClick={() => handleFileSelect('image')}
                    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <Image size={16} className="text-blue-500" />
                    上传图片
                  </button>
                  <button
                    onClick={() => handleFileSelect('file')}
                    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <FileText size={16} className="text-gray-500" />
                    上传文档
                  </button>
                </div>
              )}
            </div>

            {/* 其他功能按钮 */}
            <button
              disabled={disabled}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-200 disabled:opacity-50"
              title="表情"
            >
              <Smile size={20} />
            </button>
            
            <button
              disabled={disabled}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-200 disabled:opacity-50"
              title="标签"
            >
              <Hash size={20} />
            </button>
          </div>

          {/* 文本输入区域 */}
          <div className="flex-1 min-w-0">
            <TextareaAutosize
              value={value}
              onChange={(e) => onChange && onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              minRows={1}
              maxRows={6}
              className="w-full resize-none border-0 outline-none bg-transparent text-gray-800 placeholder-gray-400 text-base leading-relaxed"
              style={{ 
                lineHeight: '1.5'
              }}
            />
          </div>

          {/* 发送按钮 */}
          <button
            onClick={isStreaming ? onStop : onSend}
            disabled={disabled || (!value.trim() && uploadedFiles.length === 0 && !isStreaming)}
            className={`p-2.5 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
              isStreaming
                ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg hover:shadow-red-500/25'
                : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-blue-500/25 hover:scale-105'
            }`}
            title={isStreaming ? '停止生成' : '发送消息'}
          >
            {isStreaming ? (
              <StopCircle size={20} />
            ) : isLoading ? (
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            ) : (
              <Send size={20} />
            )}
          </button>
        </div>
      </div>

      {/* 隐藏的文件输入框 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.txt,.md"
        className="hidden"
        onChange={(e) => handleFileInputChange(e, 'file')}
      />
      <input
        ref={imageInputRef}
        type="file"
        multiple
        accept="image/*"
        className="hidden"
        onChange={(e) => handleFileInputChange(e, 'image')}
      />

      {/* 点击外部关闭菜单 */}
      {showFileMenu && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setShowFileMenu(false)}
        />
      )}
    </div>
  );
};

export default ModernChatInput;