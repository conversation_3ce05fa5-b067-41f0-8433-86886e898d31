import React, { memo } from 'react';
import VoucherTable from '../VoucherTable';
import { Calendar, User, FileText, CheckCircle, Clock, XCircle } from 'lucide-react';

const VoucherWorkspaceRenderer = ({ item }) => {
  const { data } = item;
  
  if (!data) {
    return (
      <div className="text-sm text-gray-500 p-4">
        <p>凭证数据不可用</p>
      </div>
    );
  }

  // 处理凭证数据格式
  const voucherData = {
    ...data,
    date: data.date || data.日期,
    summary: data.summary || data.摘要,
    entries: data.entries || [],
    creator: data.creator || data.制单人,
    reviewer: data.reviewer || data.审核人,
    status: data.status || data.状态
  };

  // 处理分录数据
  const processEntries = (entries) => {
    if (!Array.isArray(entries)) return [];
    
    return entries.map((entry, index) => ({
      ...entry,
      account: entry.account || entry.科目 || `${entry.科目编码 || ''} ${entry.科目名称 || ''}`.trim(),
      amount: parseFloat(entry.amount || entry.金额 || 0),
      type: entry.type || (entry.借方 ? 'debit' : entry.贷方 ? 'credit' : 'debit'),
      balance: entry.balance || entry.余额
    }));
  };

  const processedEntries = processEntries(voucherData.entries);

  // 获取状态信息
  const getStatusInfo = (status) => {
    const statusMap = {
      'draft': { label: '草稿', color: 'gray', icon: FileText },
      'pending': { label: '待审核', color: 'yellow', icon: Clock },
      'approved': { label: '已审核', color: 'green', icon: CheckCircle },
      'posted': { label: '已过账', color: 'blue', icon: CheckCircle },
      'cancelled': { label: '已作废', color: 'red', icon: XCircle }
    };
    return statusMap[status] || statusMap.draft;
  };

  const statusInfo = getStatusInfo(voucherData.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="space-y-4">
      {/* 凭证头部信息 */}
      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <FileText size={16} className="text-gray-500" />
              <span className="text-sm font-medium text-gray-700">凭证号:</span>
              <span className="text-sm text-gray-900 font-medium">{voucherData.voucher_no || '-'}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Calendar size={16} className="text-gray-500" />
              <span className="text-sm font-medium text-gray-700">日期:</span>
              <span className="text-sm text-gray-900">{voucherData.date || '-'}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <User size={16} className="text-gray-500" />
              <span className="text-sm font-medium text-gray-700">制单人:</span>
              <span className="text-sm text-gray-900">{voucherData.creator || '-'}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">状态:</span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                statusInfo.color === 'red' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                <StatusIcon size={12} className="mr-1" />
                {statusInfo.label}
              </span>
            </div>
            
            {voucherData.reviewer && (
              <div className="flex items-center space-x-2">
                <User size={16} className="text-gray-500" />
                <span className="text-sm font-medium text-gray-700">审核人:</span>
                <span className="text-sm text-gray-900">{voucherData.reviewer}</span>
              </div>
            )}
            
            {voucherData.total_amount && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">金额:</span>
                <span className="text-sm text-gray-900 font-medium">
                  ¥{parseFloat(voucherData.total_amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </span>
              </div>
            )}
          </div>
        </div>
        
        {voucherData.summary && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-start space-x-2">
              <FileText size={16} className="text-gray-500 mt-0.5" />
              <div>
                <span className="text-sm font-medium text-gray-700">摘要:</span>
                <p className="text-sm text-gray-900 mt-1">{voucherData.summary}</p>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 凭证分录表格 */}
      {processedEntries.length > 0 && (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <VoucherTable
            voucher={{
              ...voucherData,
              entries: processedEntries
            }}
            index={0}
            editable={false}
          />
        </div>
      )}
      
      {/* 附加信息 */}
      {voucherData.attachments && voucherData.attachments > 0 && (
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <FileText size={14} />
          <span>附件 {voucherData.attachments} 张</span>
        </div>
      )}
      
      {/* 创建时间信息 */}
      <div className="text-xs text-gray-500 text-right">
        添加到工作区: {new Date(item.timestamp).toLocaleString('zh-CN')}
      </div>
    </div>
  );
};

export default memo(VoucherWorkspaceRenderer);