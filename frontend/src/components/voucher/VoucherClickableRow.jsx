import React, { memo } from 'react';
import { ClickableRecord } from '../workspace';

const VoucherClickableRow = ({
  voucher,
  children,
  onSingleClick,
  onDoubleClick,
  className = '',
  ...props
}) => {
  // 确保点击事件处理函数被正确传递
  const handleSingleClick = (event, item) => {
    event.preventDefault();
    event.stopPropagation();
    console.log('VoucherClickableRow handleSingleClick:', item);
    if (onSingleClick) {
      onSingleClick(event, item);
    }
  };

  const handleDoubleClick = (event, item) => {
    event.preventDefault();
    event.stopPropagation();
    console.log('VoucherClickableRow handleDoubleClick:', item);
    if (onDoubleClick) {
      onDoubleClick(event, item);
    }
  };

  return (
    <ClickableRecord
      item={voucher}
      type="voucher"
      onSingleClick={handleSingleClick}
      onDoubleClick={handleDoubleClick}
      className={`transition-all duration-200 ${className}`}
      as="tr" // Render as table row to fix DOM nesting issues
      {...props}
    >
      {children}
    </ClickableRecord>
  );
};

export default memo(VoucherClickableRow);