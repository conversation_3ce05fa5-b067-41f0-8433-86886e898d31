import React, { useState, useEffect, useCallback } from 'react';
import VoucherTable from './VoucherTable';
import CollapsibleCard from './CollapsibleCard';
import { FileText, Coins, Users, Receipt, Sparkles, LayoutGrid, List } from 'lucide-react';
import { WorkspaceProvider, InteractiveWorkspace, useWorkspace } from './workspace';
import VoucherWorkspaceRenderer from './voucher/VoucherWorkspaceRenderer';

const Workspace = ({ vouchers, subjects, assets, staffs, isVisible: propIsVisible, onVisibilityChange }) => {
  const [viewMode, setViewMode] = useState('interactive'); // 'original' or 'interactive'
  const [isVisible, setIsVisible] = useState(propIsVisible !== undefined ? propIsVisible : true); // 控制工作区显示/隐藏
  
  // 当外部传入的 isVisible 变化时，更新内部状态
  useEffect(() => {
    if (propIsVisible !== undefined) {
      setIsVisible(propIsVisible);
    }
  }, [propIsVisible]);
  
  // 当内部 isVisible 变化时，通知外部
  const handleVisibilityChange = useCallback((newIsVisible) => {
    console.log('handleVisibilityChange called with:', newIsVisible);
    setIsVisible(newIsVisible);
    if (onVisibilityChange) {
      onVisibilityChange(newIsVisible);
    }
  }, [onVisibilityChange]);
  
  // 内部组件，用于使用工作区钩子
  const WorkspaceContent = () => {
    const { items } = useWorkspace();
    
    // 设置默认视图模式为交互视图，并确保工作区在有项目时显示
    useEffect(() => {
      if (items.length > 0) {
        setViewMode('interactive');
        handleVisibilityChange(true); // 有项目时确保工作区显示
      }
    }, [items, viewMode, handleVisibilityChange]);
    
    // 从工作区项目中提取不同类型的数据
    const workspaceVouchers = items.filter(item => item.type === 'voucher').map(item => item.data);
    const workspaceSubjects = items.filter(item => item.type === 'subject').map(item => item.data);
    const workspaceAssets = items.filter(item => item.type === 'asset').map(item => item.data);
    const workspaceStaffs = items.filter(item => item.type === 'staff').map(item => item.data);
    
    // 定义工作区中的 sections
    const workspaceSections = [
      {
        key: 'vouchers',
        data: workspaceVouchers,
        title: '工作区中的凭证',
        icon: <Receipt className="w-5 h-5" />,
        color: 'from-orange-500 to-red-500',
        bgColor: 'from-orange-50 to-red-50',
        render: (voucher, index) => {
          // 处理后端返回的数据结构
          const debitEntries = voucher.借方 || voucher.debit || voucher.debit_entries || [];
          const creditEntries = voucher.贷方 || voucher.credit || voucher.credit_entries || [];

          return (
            <VoucherTable
              key={index}
              voucher={{
                ...voucher,
                date: voucher.日期 || voucher.date,
                summary: voucher.摘要 || voucher.summary,
                entries: [
                  ...(Array.isArray(debitEntries) ? debitEntries : []).map(item => ({
                    ...item,
                    account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                    amount: parseFloat(item.金额 || item.amount || 0),
                    type: 'debit'
                  })),
                  ...(Array.isArray(creditEntries) ? creditEntries : []).map(item => ({
                    ...item,
                    account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                    amount: parseFloat(item.金额 || item.amount || 0),
                    type: 'credit'
                  }))
                ]
              }}
              index={index}
              editable={false}
            />
          );
        }
      },
      {
        key: 'subjects',
        data: workspaceSubjects,
        title: '工作区中的科目',
        icon: <FileText className="w-5 h-5" />,
        color: 'from-blue-500 to-cyan-500',
        bgColor: 'from-blue-50 to-cyan-50',
        render: (subject, index) => (
          <CollapsibleCard
            key={index}
            title={`科目：${subject.科目编码 || ''} ${subject.科目名称 || ''}`.trim()}
            date={subject.创建日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">科目编码:</span> <span className="text-gray-900">{subject.科目编码}</span></div>
              <div><span className="font-medium text-gray-700">科目名称:</span> <span className="text-gray-900">{subject.科目名称}</span></div>
              <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{subject.类别}</span></div>
              <div><span className="font-medium text-gray-700">方向:</span> <span className="text-gray-900">{subject.方向}</span></div>
              {subject.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{subject.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      },
      {
        key: 'assets',
        data: workspaceAssets,
        title: '工作区中的资产',
        icon: <Coins className="w-5 h-5" />,
        color: 'from-emerald-500 to-teal-500',
        bgColor: 'from-emerald-50 to-teal-50',
        render: (asset, index) => (
          <CollapsibleCard
            key={index}
            title={`资产：${asset.资产编码 || ''} ${asset.资产名称 || ''}`.trim()}
            date={asset.购置日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">资产编码:</span> <span className="text-gray-900">{asset.资产编码}</span></div>
              <div><span className="font-medium text-gray-700">资产名称:</span> <span className="text-gray-900">{asset.资产名称}</span></div>
              <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{asset.类别}</span></div>
              <div><span className="font-medium text-gray-700">原值:</span> <span className="text-gray-900">{asset.原值}</span></div>
              <div><span className="font-medium text-gray-700">净值:</span> <span className="text-gray-900">{asset.净值}</span></div>
              <div><span className="font-medium text-gray-700">购置日期:</span> <span className="text-gray-900">{asset.购置日期}</span></div>
              <div><span className="font-medium text-gray-700">使用年限:</span> <span className="text-gray-900">{asset.使用年限}</span></div>
              <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{asset.状态}</span></div>
              {asset.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{asset.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      },
      {
        key: 'staffs',
        data: workspaceStaffs,
        title: '工作区中的员工',
        icon: <Users className="w-5 h-5" />,
        color: 'from-purple-500 to-pink-500',
        bgColor: 'from-purple-50 to-pink-50',
        render: (staff, index) => (
          <CollapsibleCard
            key={index}
            title={`员工：${staff.工号 || ''} ${staff.姓名 || ''}`.trim()}
            date={staff.入职日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">工号:</span> <span className="text-gray-900">{staff.工号}</span></div>
              <div><span className="font-medium text-gray-700">姓名:</span> <span className="text-gray-900">{staff.姓名}</span></div>
              <div><span className="font-medium text-gray-700">岗位编码:</span> <span className="text-gray-900">{staff.岗位编码}</span></div>
              <div><span className="font-medium text-gray-700">电话:</span> <span className="text-gray-900">{staff.电话}</span></div>
              <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{staff.状态}</span></div>
              {staff.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{staff.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      }
    ];
    
    // 定义原始视图中的 sections
    const originalSections = [
      {
        key: 'subjects',
        data: subjects,
        title: '生成的科目',
        icon: <FileText className="w-5 h-5" />,
        color: 'from-blue-500 to-cyan-500',
        bgColor: 'from-blue-50 to-cyan-50',
        render: (subject, index) => (
          <CollapsibleCard
            key={index}
            title={`科目：${subject.科目编码 || ''} ${subject.科目名称 || ''}`.trim()}
            date={subject.创建日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">科目编码:</span> <span className="text-gray-900">{subject.科目编码}</span></div>
              <div><span className="font-medium text-gray-700">科目名称:</span> <span className="text-gray-900">{subject.科目名称}</span></div>
              <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{subject.类别}</span></div>
              <div><span className="font-medium text-gray-700">方向:</span> <span className="text-gray-900">{subject.方向}</span></div>
              {subject.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{subject.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      },
      {
        key: 'assets',
        data: assets,
        title: '生成的资产',
        icon: <Coins className="w-5 h-5" />,
        color: 'from-emerald-500 to-teal-500',
        bgColor: 'from-emerald-50 to-teal-50',
        render: (asset, index) => (
          <CollapsibleCard
            key={index}
            title={`资产：${asset.资产编码 || ''} ${asset.资产名称 || ''}`.trim()}
            date={asset.购置日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">资产编码:</span> <span className="text-gray-900">{asset.资产编码}</span></div>
              <div><span className="font-medium text-gray-700">资产名称:</span> <span className="text-gray-900">{asset.资产名称}</span></div>
              <div><span className="font-medium text-gray-700">类别:</span> <span className="text-gray-900">{asset.类别}</span></div>
              <div><span className="font-medium text-gray-700">原值:</span> <span className="text-gray-900">{asset.原值}</span></div>
              <div><span className="font-medium text-gray-700">净值:</span> <span className="text-gray-900">{asset.净值}</span></div>
              <div><span className="font-medium text-gray-700">购置日期:</span> <span className="text-gray-900">{asset.购置日期}</span></div>
              <div><span className="font-medium text-gray-700">使用年限:</span> <span className="text-gray-900">{asset.使用年限}</span></div>
              <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{asset.状态}</span></div>
              {asset.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{asset.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      },
      {
        key: 'staffs',
        data: staffs,
        title: '生成的员工',
        icon: <Users className="w-5 h-5" />,
        color: 'from-purple-500 to-pink-500',
        bgColor: 'from-purple-50 to-pink-50',
        render: (staff, index) => (
          <CollapsibleCard
            key={index}
            title={`员工：${staff.工号 || ''} ${staff.姓名 || ''}`.trim()}
            date={staff.入职日期 || ''}
          >
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium text-gray-700">工号:</span> <span className="text-gray-900">{staff.工号}</span></div>
              <div><span className="font-medium text-gray-700">姓名:</span> <span className="text-gray-900">{staff.姓名}</span></div>
              <div><span className="font-medium text-gray-700">岗位编码:</span> <span className="text-gray-900">{staff.岗位编码}</span></div>
              <div><span className="font-medium text-gray-700">电话:</span> <span className="text-gray-900">{staff.电话}</span></div>
              <div><span className="font-medium text-gray-700">状态:</span> <span className="text-gray-900">{staff.状态}</span></div>
              {staff.备注 && <div className="col-span-2"><span className="font-medium text-gray-700">备注:</span> <span className="text-gray-900">{staff.备注}</span></div>}
            </div>
          </CollapsibleCard>
        )
      },
      {
        key: 'vouchers',
        data: vouchers,
        title: '生成的凭证',
        icon: <Receipt className="w-5 h-5" />,
        color: 'from-orange-500 to-red-500',
        bgColor: 'from-orange-50 to-red-50',
        render: (voucher, index) => {
          // 处理后端返回的数据结构
          const debitEntries = voucher.借方 || voucher.debit || voucher.debit_entries || [];
          const creditEntries = voucher.贷方 || voucher.credit || voucher.credit_entries || [];

          return (
            <VoucherTable
              key={index}
              voucher={{
                ...voucher,
                date: voucher.日期 || voucher.date,
                summary: voucher.摘要 || voucher.summary,
                entries: [
                  ...(Array.isArray(debitEntries) ? debitEntries : []).map(item => ({
                    ...item,
                    account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                    amount: parseFloat(item.金额 || item.amount || 0),
                    type: 'debit'
                  })),
                  ...(Array.isArray(creditEntries) ? creditEntries : []).map(item => ({
                    ...item,
                    account: `${item.科目编码 || item.subject_code || ''} ${item.科目名称 || item.subject_name || ''}`.trim(),
                    amount: parseFloat(item.金额 || item.amount || 0),
                    type: 'credit'
                  }))
                ]
              }}
              index={index}
              editable={false}
            />
          );
        }
      }
    ];
    
    // 根据视图模式选择 sections
    const sections = viewMode === 'interactive' ? workspaceSections : originalSections;
    const hasData = sections.some(section => section.data && section.data.length > 0);
    
    // 根据可见性和视图模式渲染内容
    const renderContent = () => {
      if (!isVisible) {
        return (
          <div className="flex-1 flex items-center justify-center min-h-[60vh]">
            <div className="text-center opacity-60 select-none pointer-events-none">
              <div className="mb-8">
                <LayoutGrid size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
              </div>
              <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">
                工作区已隐藏
              </h3>
              <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-lg mx-auto">
                点击右上角按钮显示工作区
              </p>
            </div>
          </div>
        );
      }
      
      if (viewMode === 'original') {
        return (
          <div className="p-8">
            <div className="max-w-6xl mx-auto">
              {sections.map((section, sectionIndex) => (
                section.data && section.data.length > 0 && (
                  <div key={section.key} className="mb-8">
                    <div className={`bg-gradient-to-r ${section.bgColor} rounded-2xl p-6 mb-6 shadow-soft`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`p-3 bg-gradient-to-r ${section.color} rounded-xl text-white shadow-lg`}>
                            {section.icon}
                          </div>
                          <div>
                            <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                              {section.title}
                            </h2>
                            <p className="text-sm text-gray-600 mt-1 flex items-center">
                              <Sparkles className="w-4 h-4 mr-1 text-yellow-500" />
                              共 {section.data.length} 个项目
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-6">
                      {section.data.map((item, index) => (
                        <div key={index}>
                          {section.render(item, index)}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              ))}

              {/* 空状态 */}
              {!hasData && (
                <div className="flex-1 flex items-center justify-center min-h-[60vh]">
                  <div className="text-center opacity-60 select-none pointer-events-none">
                    <div className="mb-8">
                      <Sparkles size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
                    </div>
                    <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">
                      工作区空空如也
                    </h3>
                    <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-lg mx-auto">
                      与智能助手对话，流程审批、凭证生成等。<br />
                      它们将在这里显示
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      }
      
      // Interactive view mode
      return (
        <div className="p-6">
          <InteractiveWorkspace
            renderItem={(item) => {
              if (item.type === 'voucher') {
                return <VoucherWorkspaceRenderer item={item} />;
              }
              // 默认渲染其他类型
              return (
                <div className="text-sm text-gray-500 p-4">
                  <p>类型: {item.type}</p>
                  <p>ID: {item.id}</p>
                  <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32">
                    {JSON.stringify(item.data, null, 2)}
                  </pre>
                </div>
              );
            }}
            maxHeight="calc(100vh - 200px)"
          />
        </div>
      );
    };
    
    return (
      <div className="flex-1 flex flex-col h-full overflow-hidden relative">
        {/* 视图模式切换器 */}
        <div className="flex items-center justify-between px-6 py-2 border-b border-gray-200 bg-white">
          <div className="flex items-center gap-2">
            <LayoutGrid className="text-blue-600" size={20} />
            <h2 className="font-semibold">工作区</h2>
            {items.length > 0 && (
              <span className="text-sm text-gray-500">
                共 {items.length} 个项目
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                onClick={() => setViewMode('original')}
                className={`flex items-center space-x-1.5 px-2.5 py-2 rounded text-xs font-medium transition-colors ${
                  viewMode === 'original'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <List size={14} />
                <span>原始视图</span>
              </button>
              <button
                onClick={() => setViewMode('interactive')}
                className={`flex items-center space-x-1.5 px-2.5 py-2 rounded text-xs font-medium transition-colors ${
                  viewMode === 'interactive'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <LayoutGrid size={14} />
                <span>交互视图</span>
              </button>
            </div>
            
            {/* 显示/隐藏工作区按钮 */}
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          {renderContent()}
        </div>
      </div>
    );
  };

  return (
    <WorkspaceProvider>
      <WorkspaceContent />
    </WorkspaceProvider>
  );
};

export default Workspace;
