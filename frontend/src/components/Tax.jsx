import React, { useState, useEffect, useRef } from 'react';
import { Plus, Search, Filter, Download, Upload, Edit, Trash2, Calculator, FileText, Calendar, AlertTriangle } from 'lucide-react';

const Tax = () => {
  const [taxRecords, setTaxRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPeriod, setFilterPeriod] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const fileInputRef = useRef();

  const [formData, setFormData] = useState({
    tax_type: 'vat', // vat, income_tax, business_tax, etc.
    tax_period: '',
    tax_base: '',
    tax_rate: '',
    tax_amount: '',
    paid_amount: '',
    status: 'pending', // pending, filed, paid, overdue
    due_date: '',
    filing_date: '',
    payment_date: '',
    description: '',
    attachments: []
  });

  // 税种选项
  const taxTypeOptions = [
    { value: 'vat', label: '增值税', rate: 13 },
    { value: 'income_tax', label: '企业所得税', rate: 25 },
    { value: 'business_tax', label: '营业税', rate: 5 },
    { value: 'personal_tax', label: '个人所得税', rate: 0 },
    { value: 'stamp_tax', label: '印花税', rate: 0.1 },
    { value: 'property_tax', label: '房产税', rate: 1.2 },
    { value: 'land_tax', label: '土地使用税', rate: 0 },
    { value: 'other', label: '其他税费', rate: 0 }
  ];

  // 状态选项
  const statusOptions = [
    { value: 'pending', label: '待申报', color: 'yellow' },
    { value: 'filed', label: '已申报', color: 'blue' },
    { value: 'paid', label: '已缴纳', color: 'green' },
    { value: 'overdue', label: '逾期', color: 'red' }
  ];

  // 获取税务记录
  const fetchTaxRecords = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/tax/records');
      if (response.ok) {
        const data = await response.json();
        setTaxRecords(data.records || []);
      }
    } catch (error) {
      console.error('获取税务记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTaxRecords();
  }, []);

  // 过滤税务记录
  const filteredRecords = taxRecords.filter(record => {
    const matchesSearch = record.tax_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || record.tax_type === filterType;
    const matchesStatus = filterStatus === 'all' || record.status === filterStatus;
    const matchesPeriod = !filterPeriod || record.tax_period?.includes(filterPeriod);
    return matchesSearch && matchesType && matchesStatus && matchesPeriod;
  });

  // 计算统计数据
  const stats = {
    totalTaxAmount: taxRecords.reduce((sum, r) => sum + (parseFloat(r.tax_amount || 0)), 0),
    paidAmount: taxRecords.filter(r => r.status === 'paid').reduce((sum, r) => sum + (parseFloat(r.paid_amount || 0)), 0),
    pendingRecords: taxRecords.filter(r => r.status === 'pending').length,
    overdueRecords: taxRecords.filter(r => r.status === 'overdue').length
  };

  // 计算税额
  const calculateTaxAmount = (taxBase, taxRate) => {
    const base = parseFloat(taxBase || 0);
    const rate = parseFloat(taxRate || 0);
    return (base * rate / 100).toFixed(2);
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      tax_type: 'vat',
      tax_period: '',
      tax_base: '',
      tax_rate: '',
      tax_amount: '',
      paid_amount: '',
      status: 'pending',
      due_date: '',
      filing_date: '',
      payment_date: '',
      description: '',
      attachments: []
    });
  };

  // 打开添加模态框
  const handleAdd = () => {
    resetForm();
    setEditingRecord(null);
    setShowAddModal(true);
  };

  // 打开编辑模态框
  const handleEdit = (record) => {
    setFormData(record);
    setEditingRecord(record);
    setShowAddModal(true);
  };

  // 保存税务记录
  const handleSave = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const dataToSave = {
        ...formData,
        tax_base: parseFloat(formData.tax_base) || 0,
        tax_rate: parseFloat(formData.tax_rate) || 0,
        tax_amount: parseFloat(formData.tax_amount) || 0,
        paid_amount: parseFloat(formData.paid_amount) || 0
      };

      const url = editingRecord 
        ? `http://localhost:8000/tax/records/${editingRecord.id}`
        : 'http://localhost:8000/tax/records';
      const method = editingRecord ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSave)
      });

      if (response.ok) {
        await fetchTaxRecords();
        setShowAddModal(false);
        resetForm();
      }
    } catch (error) {
      console.error('保存税务记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除税务记录
  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这条税务记录吗？')) return;
    
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/tax/records/${id}`, {
        method: 'DELETE'
      });
      if (response.ok) {
        await fetchTaxRecords();
      }
    } catch (error) {
      console.error('删除税务记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 生成税务申报表
  const handleGenerateReport = async (period) => {
    if (!period) {
      alert('请选择申报期间');
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/tax/generate-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ period })
      });
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `税务申报表_${period}.pdf`;
        a.click();
      }
    } catch (error) {
      console.error('生成税务申报表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 导出税务记录
  const handleExport = () => {
    const params = new URLSearchParams();
    if (filterPeriod) params.append('period', filterPeriod);
    if (filterType !== 'all') params.append('type', filterType);
    window.open(`http://localhost:8000/tax/records/export?${params.toString()}`, '_blank');
  };

  // 导入税务记录
  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/tax/records/import', {
        method: 'POST',
        body: formData
      });
      if (response.ok) {
        await fetchTaxRecords();
      }
    } catch (error) {
      console.error('导入税务记录失败:', error);
    } finally {
      setLoading(false);
      fileInputRef.current.value = '';
    }
  };

  // 获取税种信息
  const getTaxTypeInfo = (taxType) => {
    return taxTypeOptions.find(opt => opt.value === taxType) || taxTypeOptions[0];
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  // 实时计算税额
  useEffect(() => {
    if (showAddModal && formData.tax_base && formData.tax_rate) {
      const calculatedAmount = calculateTaxAmount(formData.tax_base, formData.tax_rate);
      setFormData(prev => ({ ...prev, tax_amount: calculatedAmount }));
    }
  }, [formData.tax_base, formData.tax_rate, showAddModal]);

  // 税种变化时自动设置税率
  useEffect(() => {
    if (showAddModal && formData.tax_type) {
      const taxTypeInfo = getTaxTypeInfo(formData.tax_type);
      setFormData(prev => ({ ...prev, tax_rate: taxTypeInfo.rate.toString() }));
    }
  }, [formData.tax_type, showAddModal]);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">税务管理</h1>
        <p className="text-gray-600">管理各类税费申报和缴纳</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calculator className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">应缴税额</p>
              <p className="text-2xl font-semibold text-gray-900">
                ¥{stats.totalTaxAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已缴税额</p>
              <p className="text-2xl font-semibold text-gray-900">
                ¥{stats.paidAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">待申报</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.pendingRecords}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">逾期未缴</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.overdueRecords}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            {/* 搜索框 */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索税种或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
              />
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2">
              <input
                type="month"
                value={filterPeriod}
                onChange={(e) => setFilterPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="申报期间"
              />

              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有税种</option>
                {taxTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有状态</option>
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImport}
              accept=".csv,.xlsx"
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Upload size={18} />
              导入
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Download size={18} />
              导出
            </button>
            <button
              onClick={() => handleGenerateReport(filterPeriod)}
              className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              <FileText size={18} />
              生成申报表
            </button>
            <button
              onClick={handleAdd}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              新增税务记录
            </button>
          </div>
        </div>
      </div>

      {/* 税务记录列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredRecords.length === 0 ? (
          <div className="text-center py-12">
            <Calculator size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">暂无税务记录</p>
            <button
              onClick={handleAdd}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              添加第一条税务记录
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">税种</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报期间</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计税基数</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">税率</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应缴税额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已缴金额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRecords.map((record) => {
                  const taxTypeInfo = getTaxTypeInfo(record.tax_type);
                  const statusInfo = getStatusInfo(record.status);
                  const isOverdue = record.status === 'overdue' || (record.due_date && new Date(record.due_date) < new Date() && record.status !== 'paid');
                  
                  return (
                    <tr key={record.id} className={`hover:bg-gray-50 ${isOverdue ? 'bg-red-50' : ''}`}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {taxTypeInfo.label}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {record.tax_period}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{parseFloat(record.tax_base || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {record.tax_rate}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ¥{parseFloat(record.tax_amount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                        ¥{parseFloat(record.paid_amount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className={isOverdue ? 'text-red-600 font-medium' : ''}>
                          {record.due_date}
                          {isOverdue && <AlertTriangle size={14} className="inline ml-1" />}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                          statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                          statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {statusInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEdit(record)}
                            className="text-blue-600 hover:text-blue-900"
                            title="编辑"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(record.id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 添加/编辑税务记录模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {editingRecord ? '编辑税务记录' : '新增税务记录'}
              </h3>
              
              <form onSubmit={handleSave} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">税种 *</label>
                    <select
                      value={formData.tax_type}
                      onChange={(e) => setFormData({...formData, tax_type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      {taxTypeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">申报期间 *</label>
                    <input
                      type="month"
                      value={formData.tax_period}
                      onChange={(e) => setFormData({...formData, tax_period: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">计税基数 *</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.tax_base}
                      onChange={(e) => setFormData({...formData, tax_base: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">税率 (%)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.tax_rate}
                      onChange={(e) => setFormData({...formData, tax_rate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">应缴税额</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.tax_amount}
                      onChange={(e) => setFormData({...formData, tax_amount: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">已缴金额</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.paid_amount}
                      onChange={(e) => setFormData({...formData, paid_amount: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
                    <input
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => setFormData({...formData, due_date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">申报日期</label>
                    <input
                      type="date"
                      value={formData.filing_date}
                      onChange={(e) => setFormData({...formData, filing_date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">缴款日期</label>
                    <input
                      type="date"
                      value={formData.payment_date}
                      onChange={(e) => setFormData({...formData, payment_date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="税务记录描述..."
                  />
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50"
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Tax;
