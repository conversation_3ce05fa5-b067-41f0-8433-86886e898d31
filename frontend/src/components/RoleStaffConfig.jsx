import React, { useEffect, useState, useRef } from 'react';
import { FileText, Users, UserCheck, UserX, Search, Plus, Upload, Download, Edit, Trash2, Eye, Building, X } from 'lucide-react';

const RoleStaffConfig = () => {
  // 部门
  const [roles, setRoles] = useState([]);
  const [editRole, setEditRole] = useState(null);
  const [roleForm, setRoleForm] = useState({ code: '', name: '', description: '' });
  const roleFileInputRef = useRef();
  const [roleSearchTerm, setRoleSearchTerm] = useState('');
  // 人员
  const [staffs, setStaffs] = useState([]);
  const [editStaff, setEditStaff] = useState(null);
  const [staffForm, setStaffForm] = useState({ job_no: '', name: '', role_code: '', phone: '', status: '', remark: '' });
  const staffFileInputRef = useRef();
  const [staffSearchTerm, setStaffSearchTerm] = useState('');

  // 加载岗位
  const fetchRoles = async () => {
    const res = await fetch('http://localhost:8000/roles');
    const data = await res.json();
    setRoles(data.roles || []);
  };
  // 加载人员
  const fetchStaffs = async () => {
    const res = await fetch('http://localhost:8000/staffs');
    const data = await res.json();
    setStaffs(data.staffs || []);
  };
  // 计算统计数据
  const calculateStats = () => {
    const activeStaffs = staffs.filter(staff => staff.status === '在职' || staff.status === '启用');
    const inactiveStaffs = staffs.filter(staff => staff.status !== '在职' && staff.status !== '启用');
    
    return {
      totalRoles: roles.length,
      totalStaffs: staffs.length,
      activeStaffs: activeStaffs.length,
      inactiveStaffs: inactiveStaffs.length
    };
  };

  const stats = calculateStats();

  useEffect(() => {
    fetchRoles();
    fetchStaffs();
  }, []);

  // 岗位导出
  const handleRoleExport = () => {
    window.open('http://localhost:8000/roles/export', '_blank');
  };
  // 岗位导入
  const handleRoleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/roles/import', { method: 'POST', body: formData });
    fetchRoles();
    roleFileInputRef.current.value = '';
  };
  // 岗位删除
  const handleRoleDelete = async (code) => {
    if (!window.confirm('确定要删除该岗位吗？')) return;
    await fetch(`http://localhost:8000/roles/${code}`, { method: 'DELETE' });
    fetchRoles();
  };
  // 岗位编辑
  const openEditRole = (role) => { setEditRole(role); setRoleForm(role); };
  const closeEditRole = () => { setEditRole(null); };
  const handleRoleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/roles/${roleForm.code}`, {
      method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(roleForm),
    });
    closeEditRole(); fetchRoles();
  };

  // 人员导出
  const handleStaffExport = () => {
    window.open('http://localhost:8000/staffs/export', '_blank');
  };
  // 人员导入
  const handleStaffImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/staffs/import', { method: 'POST', body: formData });
    fetchStaffs();
    staffFileInputRef.current.value = '';
  };
  // 人员删除
  const handleStaffDelete = async (job_no) => {
    if (!window.confirm('确定要删除该人员吗？')) return;
    await fetch(`http://localhost:8000/staffs/${job_no}`, { method: 'DELETE' });
    fetchStaffs();
  };
  // 人员编辑
  const openEditStaff = (staff) => { setEditStaff(staff); setStaffForm(staff); };
  const closeEditStaff = () => { setEditStaff(null); };
  const handleStaffEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/staffs/${staffForm.job_no}`, {
      method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(staffForm),
    });
    closeEditStaff(); fetchStaffs();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">岗位人员管理</h1>
        <p className="text-gray-600">管理岗位和人员信息</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">岗位总数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalRoles}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">人员总数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalStaffs}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">在职人员</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeStaffs}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserX className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">离职人员</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.inactiveStaffs}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 岗位管理 */}
      <div className="mb-10">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              <h2 className="text-xl font-semibold text-gray-800">岗位管理</h2>
              {/* 搜索框 */}
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索岗位编号或名称..."
                  value={roleSearchTerm}
                  onChange={(e) => setRoleSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <input type="file" accept=".csv" ref={roleFileInputRef} className="hidden" onChange={handleRoleImport} />
              <button
                onClick={() => roleFileInputRef.current?.click()}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Upload size={18} />
                导入
              </button>
              <button
                onClick={handleRoleExport}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Download size={18} />
                导出
              </button>
              <button
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                新增岗位
              </button>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">岗位编号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">岗位名称</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {roles.length === 0 ? (
                <tr><td colSpan={4} className="text-center py-8 text-gray-400">暂无数据</td></tr>
              ) : roles
                .filter(role =>
                  role.code?.toLowerCase().includes(roleSearchTerm.toLowerCase()) ||
                  role.name?.toLowerCase().includes(roleSearchTerm.toLowerCase())
                )
                .map((r, idx) => (
                  <tr key={r.code} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{r.code}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{r.name}</td>
                    <td className="px-6 py-4 text-sm text-gray-500">{r.description}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => openEditRole(r)}
                          className="text-blue-600 hover:text-blue-900"
                          title="编辑"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleRoleDelete(r.code)}
                          className="text-red-600 hover:text-red-900"
                          title="删除"
                        >
                          <Trash2 size={16} />
                        </button>
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="查看详情"
                        >
                          <Eye size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        {/* 岗位编辑弹窗 */}
        {editRole && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">编辑岗位</h3>
                  <button
                    onClick={closeEditRole}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X size={24} />
                  </button>
                </div>
                
                <form onSubmit={handleRoleEditSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">岗位编号</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={roleForm.code}
                      disabled
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">岗位名称</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={roleForm.name}
                      onChange={e => setRoleForm(f => ({ ...f, name: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={roleForm.description}
                      onChange={e => setRoleForm(f => ({ ...f, description: e.target.value }))}
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={closeEditRole}
                      className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                    >
                      保存
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* 人员管理 */}
      <div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              <h2 className="text-xl font-semibold text-gray-800">人员管理</h2>
              {/* 搜索框 */}
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索工号或姓名..."
                  value={staffSearchTerm}
                  onChange={(e) => setStaffSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <input type="file" accept=".csv" ref={staffFileInputRef} className="hidden" onChange={handleStaffImport} />
              <button
                onClick={() => staffFileInputRef.current?.click()}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Upload size={18} />
                导入
              </button>
              <button
                onClick={handleStaffExport}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Download size={18} />
                导出
              </button>
              <button
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                新增人员
              </button>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">岗位编号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {staffs.length === 0 ? (
                <tr><td colSpan={7} className="text-center py-8 text-gray-400">暂无数据</td></tr>
              ) : staffs
                .filter(staff =>
                  staff.job_no?.toLowerCase().includes(staffSearchTerm.toLowerCase()) ||
                  staff.name?.toLowerCase().includes(staffSearchTerm.toLowerCase())
                )
                .map((s, idx) => (
                  <tr key={s.job_no} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{s.job_no}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{s.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{s.role_code}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{s.phone}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        s.status === '在职' || s.status === '启用' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {s.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">{s.remark}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => openEditStaff(s)}
                          className="text-blue-600 hover:text-blue-900"
                          title="编辑"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleStaffDelete(s.job_no)}
                          className="text-red-600 hover:text-red-900"
                          title="删除"
                        >
                          <Trash2 size={16} />
                        </button>
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="查看详情"
                        >
                          <Eye size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        {/* 人员编辑弹窗 */}
        {editStaff && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">编辑人员</h3>
                  <button
                    onClick={closeEditStaff}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X size={24} />
                  </button>
                </div>
                
                <form onSubmit={handleStaffEditSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">工号</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.job_no}
                      disabled
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.name}
                      onChange={e => setStaffForm(f => ({ ...f, name: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">岗位编号</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.role_code}
                      onChange={e => setStaffForm(f => ({ ...f, role_code: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">联系方式</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.phone}
                      onChange={e => setStaffForm(f => ({ ...f, phone: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.status}
                      onChange={e => setStaffForm(f => ({ ...f, status: e.target.value }))}
                    >
                      <option value="在职">在职</option>
                      <option value="离职">离职</option>
                      <option value="启用">启用</option>
                      <option value="禁用">禁用</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={staffForm.remark}
                      onChange={e => setStaffForm(f => ({ ...f, remark: e.target.value }))}
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={closeEditStaff}
                      className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                    >
                      保存
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoleStaffConfig; 