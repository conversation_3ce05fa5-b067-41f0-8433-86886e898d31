import React, { useEffect, useState, useRef } from 'react';

const Asset = () => {
  const [assets, setAssets] = useState([]);
  const [editAsset, setEditAsset] = useState(null);
  const [form, setForm] = useState({ code: '', name: '', category: '', original_value: '', net_value: '', purchase_date: '', useful_life: '', status: '', remark: '' });
  const fileInputRef = useRef();

  // 加载资产数据
  const fetchAssets = async () => {
    const res = await fetch('http://localhost:8000/assets');
    const data = await res.json();
    setAssets(data.assets || []);
  };

  useEffect(() => {
    fetchAssets();
  }, []);

  // 导出资产
  const handleExport = () => {
    window.open('http://localhost:8000/assets/export', '_blank');
  };

  // 导入资产
  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/assets/import', {
      method: 'POST',
      body: formData,
    });
    fetchAssets();
    fileInputRef.current.value = '';
  };

  // 删除资产
  const handleDelete = async (code) => {
    if (!window.confirm('确定要删除该资产吗？')) return;
    await fetch(`http://localhost:8000/assets/${code}`, { method: 'DELETE' });
    fetchAssets();
  };

  // 打开编辑弹窗
  const openEdit = (asset) => {
    setEditAsset(asset);
    setForm(asset);
  };

  // 关闭编辑弹窗
  const closeEdit = () => {
    setEditAsset(null);
  };

  // 提交编辑
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/assets/${form.code}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...form,
        original_value: parseFloat(form.original_value),
        net_value: parseFloat(form.net_value),
        useful_life: parseInt(form.useful_life, 10),
      }),
    });
    closeEdit();
    fetchAssets();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">资产管理</h1>
        <p className="text-gray-600">查看和管理公司资产信息</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">资产总数</p>
              <p className="text-2xl font-semibold text-gray-900">{assets.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">原值总额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.reduce((sum, a) => sum + (a.original_value || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">净值总额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.reduce((sum, a) => sum + (a.net_value || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">折旧总额</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.reduce((sum, a) => sum + ((a.original_value || 0) - (a.net_value || 0)), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            <input
              type="file"
              accept=".csv"
              ref={fileInputRef}
              style={{ display: 'none' }}
              onChange={handleImport}
            />
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center"
              onClick={() => fileInputRef.current.click()}
            >
              导入
            </button>
          </div>
          <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center" onClick={handleExport}>
            导出
          </button>
        </div>
      </div>

      {/* 资产表格 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原值</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">净值</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">购置日期</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用年限</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {assets.length === 0 ? (
                <tr>
                  <td colSpan={10} className="text-center py-12">
                    <p className="text-gray-500">暂无数据</p>
                  </td>
                </tr>
              ) : assets.map((a, idx) => (
                <tr key={a.code} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{a.code}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{a.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{a.category}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {a.original_value?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {a.net_value?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{a.purchase_date || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{a.useful_life || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{a.status || '-'}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{a.remark || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        className="text-blue-600 hover:text-blue-900"
                        onClick={() => openEdit(a)}
                        title="编辑"
                      >
                        编辑
                      </button>
                      <button
                        className="text-red-600 hover:text-red-900"
                        onClick={() => handleDelete(a.code)}
                        title="删除"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* 编辑弹窗 */}
      {editAsset && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <form className="bg-white p-6 rounded shadow w-96" onSubmit={handleEditSubmit}>
            <h3 className="text-lg font-semibold mb-4">编辑资产</h3>
            <div className="mb-3">
              <label className="block mb-1">编号</label>
              <input className="w-full border rounded px-2 py-1" value={form.code} disabled />
            </div>
            <div className="mb-3">
              <label className="block mb-1">名称</label>
              <input className="w-full border rounded px-2 py-1" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">类别</label>
              <input className="w-full border rounded px-2 py-1" value={form.category} onChange={e => setForm(f => ({ ...f, category: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">原值</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.original_value} onChange={e => setForm(f => ({ ...f, original_value: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">净值</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.net_value} onChange={e => setForm(f => ({ ...f, net_value: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">购置日期</label>
              <input className="w-full border rounded px-2 py-1" type="date" value={form.purchase_date} onChange={e => setForm(f => ({ ...f, purchase_date: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">使用年限</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.useful_life} onChange={e => setForm(f => ({ ...f, useful_life: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">状态</label>
              <input className="w-full border rounded px-2 py-1" value={form.status} onChange={e => setForm(f => ({ ...f, status: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">备注</label>
              <input className="w-full border rounded px-2 py-1" value={form.remark} onChange={e => setForm(f => ({ ...f, remark: e.target.value }))} />
            </div>
            <div className="flex justify-end space-x-2 mt-4">
              <button type="button" className="px-4 py-2 bg-gray-300 rounded" onClick={closeEdit}>取消</button>
              <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">保存</button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default Asset;
