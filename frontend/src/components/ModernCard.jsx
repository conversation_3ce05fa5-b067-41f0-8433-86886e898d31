import React from 'react';

const ModernCard = ({ 
  children, 
  title, 
  subtitle, 
  icon: Icon, 
  variant = 'default',
  className = '',
  headerClassName = '',
  bodyClassName = '',
  hover = true,
  ...props 
}) => {
  const variants = {
    default: 'glass shadow-soft',
    solid: 'bg-white shadow-medium border border-gray-200',
    gradient: 'bg-gradient-to-br from-white to-gray-50 shadow-medium border border-gray-200',
    glass: 'glass shadow-glass',
  };

  const hoverClass = hover ? 'card-hover' : '';

  return (
    <div 
      className={`
        ${variants[variant]} 
        ${hoverClass}
        rounded-2xl overflow-hidden
        ${className}
      `}
      {...props}
    >
      {(title || subtitle || Icon) && (
        <div className={`p-6 border-b border-white/10 ${headerClassName}`}>
          <div className="flex items-center space-x-3">
            {Icon && (
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white shadow-lg">
                <Icon size={20} />
              </div>
            )}
            <div className="min-w-0 flex-1">
              {title && (
                <h3 className="text-lg font-semibold text-gray-800 truncate">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      
      <div className={`p-6 ${bodyClassName}`}>
        {children}
      </div>
    </div>
  );
};

export default ModernCard;