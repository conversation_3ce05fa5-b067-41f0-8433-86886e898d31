import React, { useEffect, useState, useRef } from 'react';
import { FileText, FolderOpen, CheckCircle, XCircle, Search, Plus, Upload, Download, Edit, Trash2, Eye, X } from 'lucide-react';

const AUX_OPTIONS = ['部门', '项目', '客户', '供应商'];

const getCode = node => node['科目编码'] || node.code;
const getName = node => node['科目名称'] || node.name;
const getCategory = node => node['类别'] || node.category;
const getDirection = node => node['方向'] || node.direction;
const getLevel = node => node['级次'] || node.level;
const getParentCode = node => node['父级编码'] || node.parent_code;
const getAux = node => node['辅助核算'] && node['辅助核算'].length ? node['辅助核算'].join(',') : (node.aux || '-');
const getIsLeaf = node => (node['末级'] ?? node.is_leaf) ? '是' : '否';
const getStatus = node => node['状态'] || node.status;
const getRemark = node => node['备注'] || node.remark;
const getQuantity = node => (node['数量核算'] ?? node.quantity) ? '是' : '否';

const renderRows = (nodes, level = 0, expanded, toggleExpand, openEdit, handleDelete) => {
  return nodes.flatMap(node => {
    const code = getCode(node);
    if (!code) {
      // 跳过无效节点
      return [];
    }
    const rowKey = `${code}-${level}`;
    const rows = [
      <tr key={rowKey} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          <span style={{ marginLeft: level * 20 }}>
            {node.children && node.children.length > 0 && (
              <button className="mr-1 text-blue-500" onClick={() => toggleExpand(code)}>
                {expanded[code] ? '▼' : '▶'}
              </button>
            )}
            {code}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{getName(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getCategory(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getDirection(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getLevel(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getParentCode(node) || '-'}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getAux(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getIsLeaf(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            getStatus(node) === '启用' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {getStatus(node)}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getRemark(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{getQuantity(node)}</td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center gap-2">
            <button
              onClick={() => openEdit(node)}
              className="text-blue-600 hover:text-blue-900"
              title="编辑"
            >
              <Edit size={16} />
            </button>
            <button
              onClick={() => handleDelete(code)}
              className="text-red-600 hover:text-red-900"
              title="删除"
            >
              <Trash2 size={16} />
            </button>
            <button
              className="text-blue-600 hover:text-blue-900"
              title="查看详情"
            >
              <Eye size={16} />
            </button>
          </div>
        </td>
      </tr>
    ];
    if (node.children && node.children.length > 0 && expanded[code]) {
      rows.push(...renderRows(node.children, level + 1, expanded, toggleExpand, openEdit, handleDelete));
    }
    return rows;
  });
};

const SubjectTable = () => {
  const [tree, setTree] = useState([]);
  const [expanded, setExpanded] = useState({});
  const fileInputRef = useRef();
  const [editSubject, setEditSubject] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [form, setForm] = useState({
    科目编码: '',
    科目名称: '',
    类别: '',
    方向: '',
    级次: 1,
    父级编码: '',
    辅助核算: [],
    末级: true,
    状态: '启用',
    备注: '',
    数量核算: false
  });
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [loadingTemplate, setLoadingTemplate] = useState(false);

  const fetchTree = async () => {
    const res = await fetch('http://localhost:8000/subjects/tree');
    const data = await res.json();
    setTree(data.tree || []);
  };

  const fetchTemplates = async () => {
    const res = await fetch('http://localhost:8000/subjects/templates');
    const data = await res.json();
    setTemplates(data);
  };

  // 计算统计数据
  const calculateStats = (nodes) => {
    let total = 0;
    let enabled = 0;
    let disabled = 0;
    let leaf = 0;

    const count = (nodeList) => {
      nodeList.forEach(node => {
        total++;
        if (getStatus(node) === '启用') enabled++;
        if (getStatus(node) === '禁用') disabled++;
        if (getIsLeaf(node) === '是') leaf++;
        
        if (node.children && node.children.length > 0) {
          count(node.children);
        }
      });
    };

    count(nodes);
    return { total, enabled, disabled, leaf };
  };

  const stats = calculateStats(tree);

  useEffect(() => {
    fetchTree();
    fetchTemplates();
  }, []);

  const handleTemplateChange = async (e) => {
    const key = e.target.value;
    if (!key) return;
    if (!window.confirm('切换模板将覆盖当前科目表，是否继续？')) return;
    setLoadingTemplate(true);
    await fetch('http://localhost:8000/subjects/use-template', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ key }),
    });
    setSelectedTemplate(key);
    setLoadingTemplate(false);
    fetchTree();
  };

  const toggleExpand = code => {
    setExpanded(exp => ({ ...exp, [code]: !exp[code] }));
  };

  const handleExport = () => {
    window.open('http://localhost:8000/subjects/export', '_blank');
  };

  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/subjects/import', {
      method: 'POST',
      body: formData,
    });
    fetchTree();
    fileInputRef.current.value = '';
  };

  const handleDelete = async (code) => {
    if (!window.confirm('确定要删除该科目吗？')) return;
    const res = await fetch(`http://localhost:8000/subjects/${code}`, { method: 'DELETE' });
    if (res.status !== 200) {
      const data = await res.json();
      alert(data.detail || '删除失败');
    }
    fetchTree();
  };

  const openEdit = (subject) => {
    setEditSubject(subject);
    setForm({ ...subject, 辅助核算: subject['辅助核算'] || [] });
  };

  const closeEdit = () => {
    setEditSubject(null);
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/subjects/${form['科目编码']}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(form),
    });
    closeEdit();
    fetchTree();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">科目表管理</h1>
        <p className="text-gray-600">管理和维护会计科目表结构</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总科目数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已启用</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.enabled}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已禁用</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.disabled}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FolderOpen className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">末级科目</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.leaf}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            {/* 搜索框 */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索科目编码或名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
              />
            </div>

            {/* 模板选择器 */}
            <select
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={selectedTemplate}
              onChange={handleTemplateChange}
              disabled={loadingTemplate}
            >
              <option value="">选择科目表模板</option>
              {templates.map(t => (
                <option key={t.key} value={t.key}>{t.name}</option>
              ))}
            </select>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <input
              type="file"
              accept=".csv"
              ref={fileInputRef}
              className="hidden"
              onChange={handleImport}
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Upload size={18} />
              导入
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Download size={18} />
              导出
            </button>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              新增科目
            </button>
          </div>
        </div>
      </div>

      {/* 科目表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科目编码</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科目名称</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">方向</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级次</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">父级编码</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">辅助核算</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">末级</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量核算</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {renderRows(tree, 0, expanded, toggleExpand, openEdit, handleDelete)}
          </tbody>
        </table>
      </div>

      {/* 编辑弹窗 */}
      {editSubject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">编辑科目</h3>
                <button
                  onClick={closeEdit}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              <form onSubmit={handleEditSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">科目编码</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['科目编码']}
                      disabled
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">科目名称</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['科目名称']}
                      onChange={e => setForm(f => ({ ...f, 科目名称: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">类别</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['类别']}
                      onChange={e => setForm(f => ({ ...f, 类别: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">方向</label>
                    <input
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['方向']}
                      onChange={e => setForm(f => ({ ...f, 方向: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['状态']}
                      onChange={e => setForm(f => ({ ...f, 状态: e.target.value }))}
                    >
                      <option value="启用">启用</option>
                      <option value="禁用">禁用</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">末级科目</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={form['末级'] ? '是' : '否'}
                      onChange={e => setForm(f => ({ ...f, 末级: e.target.value === '是' }))}
                    >
                      <option value="是">是</option>
                      <option value="否">否</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">辅助核算</label>
                  <select
                    multiple
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={form['辅助核算']}
                    onChange={e => {
                      const opts = Array.from(e.target.selectedOptions).map(o => o.value);
                      setForm(f => ({ ...f, 辅助核算: opts }));
                    }}
                  >
                    {AUX_OPTIONS.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                  <input
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={form['备注']}
                    onChange={e => setForm(f => ({ ...f, 备注: e.target.value }))}
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_quantity"
                    checked={form['数量核算']}
                    onChange={e => setForm(f => ({ ...f, 数量核算: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_quantity" className="ml-2 block text-sm text-gray-700">数量核算</label>
                </div>
                
                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={closeEdit}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
                  >
                    保存
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubjectTable; 