import React, { useState, useEffect } from 'react';
import { Save, Plus, Edit, Trash2, Building2, ChevronRight } from 'lucide-react';

const CompanyInfo = () => {
  const [company, setCompany] = useState({
    id: null,
    name: '',
    business_scope: '',
    industry: '',
    company_size: '',
    tax_id: '',
    accounting_standards: '',
    parent_company_id: null,
    company_type: 'parent',
    established_date: '',
    registered_capital: '',
    status: 'active'
  });
  const [subsidiaries, setSubsidiaries] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isAddingSubsidiary, setIsAddingSubsidiary] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch company data from backend
  useEffect(() => {
    fetchCompany();
  }, []);

  const fetchCompany = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/api/company/current');
      if (response.ok) {
        const data = await response.json();
        setCompany(data);
      } else if (response.status === 404) {
        // No company exists yet, set empty form
        setCompany({
          id: null,
          name: '',
          business_scope: '',
          industry: '',
          company_size: '',
          tax_id: '',
          accounting_standards: '',
          parent_company_id: null,
          company_type: 'parent',
          established_date: '',
          registered_capital: '',
          status: 'active'
        });
        setIsEditing(true);
        setSubsidiaries([]);
      } else {
        throw new Error('获取公司信息失败');
      }
    } catch (err) {
      setError('获取公司信息失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchSubsidiaries = async () => {
    if (!company.id) return;
    
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8000/api/companies/${company.id}/subsidiaries`);
      if (response.ok) {
        const data = await response.json();
        setSubsidiaries(data);
      }
    } catch (err) {
      console.error('获取子公司信息失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCompany(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddSubsidiary = () => {
    setIsAddingSubsidiary(true);
  };

  const handleCreateSubsidiary = async (subsidiaryData) => {
    if (!company.id) return;
    
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8000/api/companies/${company.id}/subsidiaries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subsidiaryData),
      });
      
      if (!response.ok) {
        throw new Error('创建子公司失败');
      }
      
      await fetchSubsidiaries();
      setIsAddingSubsidiary(false);
    } catch (err) {
      setError('创建子公司失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (company.id) {
        // Update existing company
        const response = await fetch(`http://localhost:8000/api/companies/${company.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(company),
        });
        
        if (!response.ok) {
          throw new Error('更新公司信息失败');
        }
        
        const updatedCompany = await response.json();
        setCompany(updatedCompany);
      } else {
        // Create new company
        const response = await fetch('http://localhost:8000/api/companies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(company),
        });
        
        if (!response.ok) {
          throw new Error('创建公司信息失败');
        }
        
        const newCompany = await response.json();
        setCompany(newCompany);
      }
      setIsEditing(false);
    } catch (err) {
      setError('保存公司信息失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch subsidiaries when company changes
  useEffect(() => {
    if (company.id && company.company_type === 'parent') {
      fetchSubsidiaries();
    }
  }, [company]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleDelete = async () => {
    if (window.confirm('确定要删除这家公司吗？删除后将无法恢复。')) {
      try {
        setLoading(true);
        const response = await fetch(`http://localhost:8000/api/companies/${company.id}`, {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('删除公司信息失败');
        }
        
        // Reset to empty form after deletion
        setCompany({
          id: null,
          name: '',
          business_scope: '',
          industry: '',
          company_size: '',
          tax_id: '',
          accounting_standards: '',
          parent_company_id: null,
          company_type: 'parent',
          established_date: '',
          registered_capital: '',
          status: 'active'
        });
        setIsEditing(true);
        setSubsidiaries([]);
      } catch (err) {
        setError('删除公司信息失败: ' + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">公司信息</h2>
          {!isEditing && company.id && (
            <div className="flex gap-2">
              <button
                onClick={handleEdit}
                className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit size={18} />
                编辑
              </button>
              <button
                onClick={handleDelete}
                className="flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Trash2 size={18} />
                删除
              </button>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {isEditing && (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公司名称 *
                </label>
                <input
                  type="text"
                  name="name"
                  value={company.name || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  行业
                </label>
                <input
                  type="text"
                  name="industry"
                  value={company.industry || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  税号
                </label>
                <input
                  type="text"
                  name="tax_id"
                  value={company.tax_id || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公司规模
                </label>
                <select
                  name="company_size"
                  value={company.company_size || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择</option>
                  <option value="小型企业">小型企业</option>
                  <option value="中小型企业">中小型企业</option>
                  <option value="中型企业">中型企业</option>
                  <option value="大型企业">大型企业</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  经营范围
                </label>
                <textarea
                  name="business_scope"
                  value={company.business_scope || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  会计准则
                </label>
                <input
                  type="text"
                  name="accounting_standards"
                  value={company.accounting_standards || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公司类型
                </label>
                <select
                  name="company_type"
                  value={company.company_type || 'parent'}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="parent">母公司</option>
                  <option value="subsidiary">子公司</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  成立日期
                </label>
                <input
                  type="date"
                  name="established_date"
                  value={company.established_date || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  注册资本
                </label>
                <input
                  type="number"
                  name="registered_capital"
                  value={company.registered_capital || ''}
                  onChange={handleInputChange}
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态
                </label>
                <select
                  name="status"
                  value={company.status || 'active'}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50"
              >
                <Save size={18} />
                {loading ? '保存中...' : '保存'}
              </button>
            </div>
          </form>
        )}

        {!isEditing && !company.id && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">暂无公司信息</p>
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center gap-2 mx-auto bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus size={18} />
              添加公司信息
            </button>
          </div>
        )}

        {!isEditing && company.id && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">公司名称</h3>
                <p className="text-lg font-semibold text-gray-900">{company.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">行业</h3>
                <p className="text-lg text-gray-900">{company.industry || '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">税号</h3>
                <p className="text-lg text-gray-900">{company.tax_id || '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">公司规模</h3>
                <p className="text-lg text-gray-900">{company.company_size || '未设置'}</p>
              </div>
              <div className="md:col-span-2">
                <h3 className="text-sm font-medium text-gray-500">经营范围</h3>
                <p className="text-gray-900">{company.business_scope || '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">会计准则</h3>
                <p className="text-gray-900">{company.accounting_standards || '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">公司类型</h3>
                <p className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  company.company_type === 'parent'
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {company.company_type === 'parent' ? '母公司' : '子公司'}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">成立日期</h3>
                <p className="text-gray-900">{company.established_date || '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">注册资本</h3>
                <p className="text-gray-900">{company.registered_capital ? `${company.registered_capital} 元` : '未设置'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">状态</h3>
                <p className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  company.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {company.status === 'active' ? '活跃' : '非活跃'}
                </p>
              </div>
            </div>

            {/* Subsidiaries Section */}
            {company.company_type === 'parent' && (
              <div className="mt-8 border-t pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                    <Building2 className="mr-2" size={20} />
                    子公司信息
                  </h3>
                  <button
                    onClick={handleAddSubsidiary}
                    className="flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Plus size={16} />
                    添加子公司
                  </button>
                </div>

                {subsidiaries.length > 0 ? (
                  <div className="space-y-3">
                    {subsidiaries.map(subsidiary => (
                      <div key={subsidiary.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <ChevronRight className="text-gray-400 mr-2" size={16} />
                            <div>
                              <h4 className="font-semibold text-gray-800">{subsidiary.name}</h4>
                              <p className="text-sm text-gray-600">{subsidiary.industry || '未设置行业'} | {subsidiary.tax_id || '未设置税号'}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800`}>
                              子公司
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <Building2 className="mx-auto text-gray-400 mb-2" size={32} />
                    <p className="text-gray-500">暂无子公司信息</p>
                    <p className="text-sm text-gray-400 mt-1">点击"添加子公司"按钮创建子公司</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Subsidiary Form Modal */}
        {isAddingSubsidiary && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-800">添加子公司</h3>
                <button
                  onClick={() => setIsAddingSubsidiary(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <SubsidiaryForm
                onSubmit={handleCreateSubsidiary}
                onCancel={() => setIsAddingSubsidiary(false)}
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyInfo;

// Subsidiary Form Component
const SubsidiaryForm = ({ onSubmit, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    name: '',
    business_scope: '',
    industry: '',
    company_size: '',
    tax_id: '',
    accounting_standards: '',
    established_date: '',
    registered_capital: '',
    status: 'active'
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            子公司名称 *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            行业
          </label>
          <input
            type="text"
            name="industry"
            value={formData.industry || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            税号
          </label>
          <input
            type="text"
            name="tax_id"
            value={formData.tax_id || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            公司规模
          </label>
          <select
            name="company_size"
            value={formData.company_size || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">请选择</option>
            <option value="小型企业">小型企业</option>
            <option value="中小型企业">中小型企业</option>
            <option value="中型企业">中型企业</option>
            <option value="大型企业">大型企业</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            经营范围
          </label>
          <textarea
            name="business_scope"
            value={formData.business_scope || ''}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            会计准则
          </label>
          <input
            type="text"
            name="accounting_standards"
            value={formData.accounting_standards || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            成立日期
          </label>
          <input
            type="date"
            name="established_date"
            value={formData.established_date || ''}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            注册资本
          </label>
          <input
            type="number"
            name="registered_capital"
            value={formData.registered_capital || ''}
            onChange={handleInputChange}
            step="0.01"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            状态
          </label>
          <select
            name="status"
            value={formData.status || 'active'}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
          </select>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={loading || !formData.name}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50"
        >
          {loading ? '创建中...' : '创建子公司'}
        </button>
      </div>
    </form>
  );
};