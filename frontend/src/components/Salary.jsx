import React, { useState, useEffect, useRef } from 'react';
import { Plus, Search, Filter, Download, Upload, Edit, Trash2, Calculator, Users, DollarSign, Calendar } from 'lucide-react';

const Salary = () => {
  const [salaryRecords, setSalaryRecords] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterMonth, setFilterMonth] = useState(new Date().toISOString().slice(0, 7));
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const fileInputRef = useRef();

  const [formData, setFormData] = useState({
    employee_id: '',
    salary_month: new Date().toISOString().slice(0, 7),
    basic_salary: '',
    allowances: '',
    overtime_pay: '',
    bonus: '',
    deductions: '',
    social_insurance: '',
    housing_fund: '',
    personal_tax: '',
    net_salary: '',
    status: 'draft', // draft, approved, paid
    notes: ''
  });

  // 状态选项
  const statusOptions = [
    { value: 'draft', label: '草稿', color: 'gray' },
    { value: 'approved', label: '已审批', color: 'blue' },
    { value: 'paid', label: '已发放', color: 'green' }
  ];

  // 获取工资记录
  const fetchSalaryRecords = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/salary/records');
      if (response.ok) {
        const data = await response.json();
        setSalaryRecords(data.records || []);
      }
    } catch (error) {
      console.error('获取工资记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const response = await fetch('http://localhost:8000/staffs');
      if (response.ok) {
        const data = await response.json();
        setEmployees(data.staffs || []);
      }
    } catch (error) {
      console.error('获取员工列表失败:', error);
    }
  };

  useEffect(() => {
    fetchSalaryRecords();
    fetchEmployees();
  }, []);

  // 过滤工资记录
  const filteredRecords = salaryRecords.filter(record => {
    const matchesSearch = record.employee_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employee_id?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesMonth = !filterMonth || record.salary_month === filterMonth;
    const matchesStatus = filterStatus === 'all' || record.status === filterStatus;
    return matchesSearch && matchesMonth && matchesStatus;
  });

  // 计算统计数据
  const currentMonthRecords = salaryRecords.filter(r => r.salary_month === filterMonth);
  const stats = {
    totalEmployees: currentMonthRecords.length,
    totalGrossSalary: currentMonthRecords.reduce((sum, r) => sum + (parseFloat(r.basic_salary || 0) + parseFloat(r.allowances || 0) + parseFloat(r.overtime_pay || 0) + parseFloat(r.bonus || 0)), 0),
    totalNetSalary: currentMonthRecords.reduce((sum, r) => sum + (parseFloat(r.net_salary || 0)), 0),
    paidRecords: currentMonthRecords.filter(r => r.status === 'paid').length
  };

  // 计算净工资
  const calculateNetSalary = (data) => {
    const gross = parseFloat(data.basic_salary || 0) + 
                  parseFloat(data.allowances || 0) + 
                  parseFloat(data.overtime_pay || 0) + 
                  parseFloat(data.bonus || 0);
    const totalDeductions = parseFloat(data.deductions || 0) + 
                           parseFloat(data.social_insurance || 0) + 
                           parseFloat(data.housing_fund || 0) + 
                           parseFloat(data.personal_tax || 0);
    return gross - totalDeductions;
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      employee_id: '',
      salary_month: new Date().toISOString().slice(0, 7),
      basic_salary: '',
      allowances: '',
      overtime_pay: '',
      bonus: '',
      deductions: '',
      social_insurance: '',
      housing_fund: '',
      personal_tax: '',
      net_salary: '',
      status: 'draft',
      notes: ''
    });
  };

  // 打开添加模态框
  const handleAdd = () => {
    resetForm();
    setEditingRecord(null);
    setShowAddModal(true);
  };

  // 打开编辑模态框
  const handleEdit = (record) => {
    setFormData(record);
    setEditingRecord(record);
    setShowAddModal(true);
  };

  // 保存工资记录
  const handleSave = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const netSalary = calculateNetSalary(formData);
      const dataToSave = {
        ...formData,
        basic_salary: parseFloat(formData.basic_salary) || 0,
        allowances: parseFloat(formData.allowances) || 0,
        overtime_pay: parseFloat(formData.overtime_pay) || 0,
        bonus: parseFloat(formData.bonus) || 0,
        deductions: parseFloat(formData.deductions) || 0,
        social_insurance: parseFloat(formData.social_insurance) || 0,
        housing_fund: parseFloat(formData.housing_fund) || 0,
        personal_tax: parseFloat(formData.personal_tax) || 0,
        net_salary: netSalary
      };

      const url = editingRecord 
        ? `http://localhost:8000/salary/records/${editingRecord.id}`
        : 'http://localhost:8000/salary/records';
      const method = editingRecord ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSave)
      });

      if (response.ok) {
        await fetchSalaryRecords();
        setShowAddModal(false);
        resetForm();
      }
    } catch (error) {
      console.error('保存工资记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除工资记录
  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这条工资记录吗？')) return;
    
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/salary/records/${id}`, {
        method: 'DELETE'
      });
      if (response.ok) {
        await fetchSalaryRecords();
      }
    } catch (error) {
      console.error('删除工资记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 批量生成工资单
  const handleBatchGenerate = async () => {
    if (!window.confirm('确定要为所有员工生成本月工资单吗？')) return;
    
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/salary/batch-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ salary_month: filterMonth })
      });
      if (response.ok) {
        await fetchSalaryRecords();
      }
    } catch (error) {
      console.error('批量生成工资单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 导出工资记录
  const handleExport = () => {
    const params = new URLSearchParams();
    if (filterMonth) params.append('month', filterMonth);
    window.open(`http://localhost:8000/salary/records/export?${params.toString()}`, '_blank');
  };

  // 导入工资记录
  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/salary/records/import', {
        method: 'POST',
        body: formData
      });
      if (response.ok) {
        await fetchSalaryRecords();
      }
    } catch (error) {
      console.error('导入工资记录失败:', error);
    } finally {
      setLoading(false);
      fileInputRef.current.value = '';
    }
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  // 获取员工名称
  const getEmployeeName = (employeeId) => {
    const employee = employees.find(emp => emp.job_no === employeeId);
    return employee ? employee.name : '未知员工';
  };

  // 实时计算净工资
  useEffect(() => {
    if (showAddModal) {
      const netSalary = calculateNetSalary(formData);
      setFormData(prev => ({ ...prev, net_salary: netSalary.toFixed(2) }));
    }
  }, [formData.basic_salary, formData.allowances, formData.overtime_pay, formData.bonus, 
      formData.deductions, formData.social_insurance, formData.housing_fund, formData.personal_tax, showAddModal]);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">工资管理</h1>
        <p className="text-gray-600">管理员工工资发放和薪酬计算</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">员工总数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalEmployees}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calculator className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">应发工资</p>
              <p className="text-2xl font-semibold text-gray-900">
                ¥{stats.totalGrossSalary.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">实发工资</p>
              <p className="text-2xl font-semibold text-gray-900">
                ¥{stats.totalNetSalary.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-orange-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已发放</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.paidRecords}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            {/* 搜索框 */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索员工姓名或工号..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
              />
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2">
              <input
                type="month"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有状态</option>
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImport}
              accept=".csv,.xlsx"
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Upload size={18} />
              导入
            </button>
            <button
              onClick={handleExport}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Download size={18} />
              导出
            </button>
            <button
              onClick={handleBatchGenerate}
              className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              <Calculator size={18} />
              批量生成
            </button>
            <button
              onClick={handleAdd}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              新增工资单
            </button>
          </div>
        </div>
      </div>

      {/* 工资记录列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredRecords.length === 0 ? (
          <div className="text-center py-12">
            <Calculator size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">暂无工资记录</p>
            <button
              onClick={handleAdd}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              添加第一条工资记录
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">月份</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">基本工资</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">津贴</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">加班费</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖金</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">扣款</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实发工资</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRecords.map((record) => {
                  const statusInfo = getStatusInfo(record.status);
                  const totalDeductions = (parseFloat(record.deductions || 0) + 
                                         parseFloat(record.social_insurance || 0) + 
                                         parseFloat(record.housing_fund || 0) + 
                                         parseFloat(record.personal_tax || 0));
                  
                  return (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {getEmployeeName(record.employee_id)}
                          </div>
                          <div className="text-sm text-gray-500">{record.employee_id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {record.salary_month}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{parseFloat(record.basic_salary || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{parseFloat(record.allowances || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{parseFloat(record.overtime_pay || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{parseFloat(record.bonus || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                        -¥{totalDeductions.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        ¥{parseFloat(record.net_salary || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                          statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {statusInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEdit(record)}
                            className="text-blue-600 hover:text-blue-900"
                            title="编辑"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(record.id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 添加/编辑工资记录模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {editingRecord ? '编辑工资记录' : '新增工资记录'}
              </h3>
              
              <form onSubmit={handleSave} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">员工 *</label>
                    <select
                      value={formData.employee_id}
                      onChange={(e) => setFormData({...formData, employee_id: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    >
                      <option value="">请选择员工</option>
                      {employees.map(employee => (
                        <option key={employee.job_no} value={employee.job_no}>
                          {employee.name} ({employee.job_no})
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">工资月份 *</label>
                    <input
                      type="month"
                      value={formData.salary_month}
                      onChange={(e) => setFormData({...formData, salary_month: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {statusOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* 收入项目 */}
                <div>
                  <h4 className="text-md font-semibold text-gray-800 mb-3 border-b pb-2">收入项目</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">基本工资</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.basic_salary}
                        onChange={(e) => setFormData({...formData, basic_salary: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">津贴补助</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.allowances}
                        onChange={(e) => setFormData({...formData, allowances: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">加班费</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.overtime_pay}
                        onChange={(e) => setFormData({...formData, overtime_pay: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">奖金</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.bonus}
                        onChange={(e) => setFormData({...formData, bonus: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* 扣除项目 */}
                <div>
                  <h4 className="text-md font-semibold text-gray-800 mb-3 border-b pb-2">扣除项目</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">其他扣款</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.deductions}
                        onChange={(e) => setFormData({...formData, deductions: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">社会保险</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.social_insurance}
                        onChange={(e) => setFormData({...formData, social_insurance: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">住房公积金</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.housing_fund}
                        onChange={(e) => setFormData({...formData, housing_fund: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">个人所得税</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.personal_tax}
                        onChange={(e) => setFormData({...formData, personal_tax: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* 实发工资 */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-gray-800">实发工资：</span>
                    <span className="text-2xl font-bold text-green-600">
                      ¥{parseFloat(formData.net_salary || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">备注</label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="工资发放备注..."
                  />
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50"
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Salary;
