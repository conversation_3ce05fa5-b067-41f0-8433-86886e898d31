import React from 'react';

const LeftResizer = ({ onResize }) => {
  const handleMouseDown = (e) => {
    e.preventDefault();
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (onResize) {
      onResize(e.clientX);
    }
  };

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  return (
    <div
      className="w-3 hover:w-3 bg-transparent cursor-col-resize absolute left-0 top-0 bottom-0 z-10 group"
      onMouseDown={handleMouseDown}
    >
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-3 h-full bg-transparent group-hover:bg-blue-400 group-hover:w-px"></div>
      </div>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-3 h-8 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100 group-hover:w-3"></div>
      </div>
    </div>
  );
};

export default LeftResizer;