import React, { useState, useCallback, useRef } from 'react';
import { CheckCircle, FileText, MessageCircle } from 'lucide-react';
import { MESSAGE_TYPES } from '../common/messageTypes';
import { cleanJsonFromText } from '../common/utils';
import api from '../../../api';
import { backendBase } from '../../../api';
import { getActiveService, AI_SERVICE_TYPES } from '../../../types/aiConfig';

const Audit = ({
  messages,
  setMessages,
  input,
  setInput,
  isLoading,
  setIsLoading,
  isStreaming,
  setIsStreaming,
  sessionId,
  setSessionId,
  abortController,
  setAbortController,
  uploadedFiles,
  setUploadedFiles,
  aiConfig,
  addMessage,
  updateMessage,
  showToast,
  configureAgent,
  setContext,
  setAutoLooping,
  autoLoopRef
}) => {
  const streamingMessageRef = useRef(null);
  const reasoningMessageRef = useRef(null);

  // 单据审核模式的 action 处理主函数
  const handleAuditAgentAction = async (response, userInput) => {
    try {
      console.log('[DEBUG] handleAuditAgentAction called', response);
      console.log('[DEBUG] action:', response.action);
      console.log('[DEBUG] audit_conclusion:', response.audit_conclusion);
      console.log('[DEBUG] needs_more_info:', response.needs_more_info);
      console.log('[DEBUG] required_info:', response.required_info);
      console.log('[DEBUG] analysis:', response.analysis);
      const { action, answer, data, is_finished, needs_more_info, required_info, audit_conclusion, analysis } = response;
      
      // 根据action类型处理不同的响应，避免重复显示
      switch (action) {
        case 'audit_complete':
          // 审核完成 - 添加审核结果消息
          const completeMessage = cleanJsonFromText(audit_conclusion) || '单据审核通过，符合相关规章制度要求。';
          let completeData = { result: 'audit_complete' };
          
          // 如果有分析内容，也包含在结果数据中
          if (analysis && analysis.trim()) {
            completeData.analysis = cleanJsonFromText(analysis);
          }
          
          addMessage(completeMessage, MESSAGE_TYPES.AUDIT_RESULT, completeData);
          showToast('单据审核完成', 'success');
          break;
          
        case 'request_more_info':
          // 需要更多信息 - 只显示信息请求消息，不重复显示结论
          if (needs_more_info && required_info && Array.isArray(required_info) && required_info.length > 0) {
            addMessage('', MESSAGE_TYPES.AUDIT_INFO_REQUEST, { 
              required_info: required_info.map(info => cleanJsonFromText(info)),
              content: cleanJsonFromText(audit_conclusion) || '为了完成审核，需要您提供以下补充信息：'
            });
            showToast('需要补充信息以完成审核', 'info');
          } else {
            addMessage(cleanJsonFromText(audit_conclusion) || '需要更多信息以完成审核', MESSAGE_TYPES.AUDIT_INFO_REQUEST, {
              content: cleanJsonFromText(audit_conclusion) || '需要更多信息以完成审核'
            });
            showToast('需要补充信息以完成审核', 'info');
          }
          break;
          
        case 'audit_rejected':
          // 审核拒绝 - 添加审核结果消息
          const rejectionMessage = cleanJsonFromText(audit_conclusion) || '单据审核不通过，存在不符合规章制度的问题。';
          let resultData = { result: 'audit_rejected' };
          
          // 如果有需要补充的信息，也显示出来
          if (needs_more_info && required_info && Array.isArray(required_info) && required_info.length > 0) {
            resultData.required_info = required_info.map(info => cleanJsonFromText(info));
          }
          
          addMessage(rejectionMessage, MESSAGE_TYPES.AUDIT_RESULT, resultData);
          showToast('单据审核未通过', 'error');
          break;
          
        case 'none':
        default:
          // 默认情况，可能是一般性分析
          if (audit_conclusion && audit_conclusion !== '分析完成') {
            addMessage(cleanJsonFromText(audit_conclusion), MESSAGE_TYPES.AUDIT_RESULT, { result: 'none' });
          }
          showToast('审核分析完成');
          break;
      }
      
      if (!is_finished) {
        // 如果未完成且需要更多信息，可以在这里添加自动提示或其他处理
        if (needs_more_info) {
          console.log('Audit requires more information from user');
        }
      }
    } catch (error) {
      console.error('handleAuditAgentAction error:', error);
      addMessage(`处理响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
      showToast('处理响应失败', 'error');
    }
  };

  // 封装单据审核模式的 sendMessage
  const sendAuditRequestWithContext = async (content, extraContext = {}) => {
    if ((!content.trim() && !(extraContext.files && extraContext.files.length > 0)) || isLoading) return;
    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;
    // 添加用户消息
    if (content && content.trim()) {
      addMessage(content, MESSAGE_TYPES.USER);
    } else if (extraContext.files && extraContext.files.length > 0) {
      addMessage('用户上传了文件，请进行单据审核', MESSAGE_TYPES.USER);
    }
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);
    // 插入思考动画
    const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
    let thinkingMsgId = thinkingMsg.id;
    let analysisMsgId = null;
    let reasoningMsgId = null;
    let creatingCardsMsgId = null;
    let gotCards = false;
    let gotAnalysis = false;
    let gotReasoning = false;
    let lastAnalysis = '';
    let lastReasoning = '';
    let fullBuffer = '';
    let jsonCandidate = '';
    let firstContentReceived = false; // 标记是否已收到第一个内容
    const controller = new AbortController();
    setAbortController(controller);
    try {
      // 获取当前活动的AI服务
      const activeService = getActiveService(aiConfig);

      // 构建请求头
      let headers = {
        'Content-Type': 'application/json'
      };

      if (activeService) {
        if (activeService.type === AI_SERVICE_TYPES.OLLAMA) {
          headers['X-API-Key'] = '';
          headers['X-Base-URL'] = activeService.config.baseUrl;
          headers['X-Model'] = activeService.config.model;
          headers['X-Use-Ollama'] = 'true';
        } else {
          headers['X-API-Key'] = activeService.config.apiKey || '';
          headers['X-Base-URL'] = activeService.config.baseUrl;
          headers['X-Model'] = activeService.config.model;
          headers['X-Use-Ollama'] = 'false';
        }
      }

      const response = await fetch(`${backendBase}/agent/v2/audit/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true,
          files: extraContext.files || null,
          ...extraContext
        }),
        signal: controller.signal
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let streamingBuffer = ''; // 用于存储流式文本内容
      let hasStructuredData = false; // 是否检测到结构化数据
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        fullBuffer += chunk;
        
        // 尝试从chunk中提取reasoning_content
        let reasoningContent = '';
        let regularContent = '';
        
        // 如果没有解析到结构化内容，将整个chunk作为普通内容
        if (!reasoningContent && !regularContent) {
          regularContent = chunk;
        }
        
        // 立即收集流式内容
        streamingBuffer += chunk;
        
        // 检查是否包含结构化标记
        if (chunk.includes('<|') && !hasStructuredData) {
          console.log('[DEBUG] 检测到结构化标记');
          hasStructuredData = true;
          
          // 添加智能体思考中的对话框
          if (!thinkingMsgId || firstContentReceived) {
            const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
            thinkingMsgId = thinkingMsg.id;
          }
        }
        
        // 立即显示流式内容，不等待完整响应
        if (streamingBuffer.trim()) {
          // 如果这是第一次收到内容，立即移除初始思考动画，但保留检测到结构化标记后添加的思考对话框
          if (!firstContentReceived) {
            firstContentReceived = true;
            // 只有当没有检测到结构化标记时，才移除思考动画
            if (!hasStructuredData) {
              setMessages(prev => prev.filter(msg => msg.id !== thinkingMsgId));
            }
          }
          
          // 如果检测到结构化标记，智能处理显示内容
          let displayContent = streamingBuffer;
          if (hasStructuredData) {
            // 检测到结构化标记后，不再更新分析内容，保持最后一次的有效内容
            // 这样可以确保在显示智能体思考中对话框时，之前的分析对话框不会继续输出
            if (lastAnalysis && lastAnalysis.trim()) {
              // 保持最后一次的有效分析内容，不再更新
              displayContent = lastAnalysis;
            } else {
              // 如果没有之前的分析内容，使用默认提示
              displayContent = '正在分析单据内容...';
            }
          } else {
            // 没有检测到结构化标记，正常处理显示内容
            const firstTagMatch = streamingBuffer.match(/<\|[^|]+\|>/);
            if (firstTagMatch) {
              if (firstTagMatch.index > 0) {
                // 标记不在开头，显示标记之前的所有内容（这是审核分析内容）
                displayContent = streamingBuffer.substring(0, firstTagMatch.index).trim();
              } else {
                // 标记在开头，说明分析内容可能已经结束，保留最后一次有效的内容
                // 不更新displayContent，保持上一次的有效内容
                displayContent = lastAnalysis || '正在分析单据内容...';
              }
            }
          }
          
          // 只有当显示内容有变化时才更新UI，并且只有在没有检测到结构化标记时才更新
          if (displayContent !== lastAnalysis && displayContent.trim() && !hasStructuredData) {
            lastAnalysis = displayContent;
            gotAnalysis = true;
            
            if (!analysisMsgId) {
              // 创建分析消息
              const analysisMsg = addMessage(displayContent, MESSAGE_TYPES.AUDIT_ANALYSIS);
              analysisMsgId = analysisMsg.id;
            } else {
              // 实时更新分析内容
              updateMessage(analysisMsgId, { content: displayContent });
            }
          }
          
          // 处理推理内容
          if (reasoningContent && reasoningContent.trim()) {
            lastReasoning += reasoningContent;
            gotReasoning = true;
            
            if (!reasoningMsgId) {
              // 创建推理消息
              const reasoningMsg = addMessage(lastReasoning, MESSAGE_TYPES.REASONING);
              reasoningMsgId = reasoningMsg.id;
            } else {
              // 实时更新推理内容
              updateMessage(reasoningMsgId, { content: lastReasoning });
            }
          }
        }
      }
      
      // 流式结束后，尝试解析结构化数据
      console.log('[DEBUG] 流式结束，fullBuffer长度:', fullBuffer.length);
      console.log('[DEBUG] streamingBuffer长度:', streamingBuffer.length);
      
      // 从fullBuffer中提取结构化数据
      const structuredData = {};
      const tagPatterns = {
        'audit_conclusion': /<\|audit_conclusion\|>(.*?)<\|\/audit_conclusion\|>/s,
        'action': /<\|action\|>(.*?)<\|\/action\|>/s,
        'needs_more_info': /<\|needs_more_info\|>(.*?)<\|\/needs_more_info\|>/s,
        'required_info': /<\|required_info\|>(.*?)<\|\/required_info\|>/s,
        'is_finished': /<\|is_finished\|>(.*?)<\|\/is_finished\|>/s
      };
      
      for (const [key, pattern] of Object.entries(tagPatterns)) {
        const match = fullBuffer.match(pattern);
        if (match) {
          structuredData[key] = match[1].trim();
          console.log('[DEBUG] 提取到', key, ':', structuredData[key]);
        }
      }
      
      // 如果有结构化数据，构建响应对象
      if (Object.keys(structuredData).length > 0) {
        // 从fullBuffer中提取审核分析部分（标记之前的内容）
        let analysisContent = fullBuffer;
        
        // 找到第一个标记的位置
        const firstTagMatch = fullBuffer.match(/<\|[^|]+\|>/);
        if (firstTagMatch) {
          analysisContent = fullBuffer.substring(0, firstTagMatch.index).trim();
        }
        
        // 清理分析内容
        analysisContent = cleanJsonFromText(analysisContent);
        
        console.log('[DEBUG] 提取的分析内容:', analysisContent);
        
        // 确保分析内容不为空，如果为空则使用audit_conclusion作为分析内容
        if (!analysisContent.trim() && structuredData.audit_conclusion) {
          analysisContent = cleanJsonFromText(structuredData.audit_conclusion);
          console.log('[DEBUG] 使用audit_conclusion作为分析内容:', analysisContent);
        }
        
        const structuredResponse = {
          success: true,
          action: structuredData.action || 'none',
          audit_conclusion: structuredData.audit_conclusion || '',
          needs_more_info: structuredData.needs_more_info === 'true',
          required_info: structuredData.required_info ? 
            structuredData.required_info.split(',').map(item => item.trim()).filter(item => item) : [],
          is_finished: structuredData.is_finished !== 'false',
          analysis: analysisContent || ''
        };
        
        console.log('[DEBUG] 构建的结构化响应:', structuredResponse);
        // 处理结构化响应
        try {
          // 确保移除任何剩余的思考动画
          setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
          
          // 确保显示审核分析内容
          if (structuredResponse.analysis && structuredResponse.analysis.trim()) {
            if (analysisMsgId) {
              // 更新现有的分析消息
              console.log('[DEBUG] 更新审核分析内容:', structuredResponse.analysis);
              updateMessage(analysisMsgId, {
                content: structuredResponse.analysis,
                type: MESSAGE_TYPES.AUDIT_ANALYSIS
              });
            } else {
              // 创建新的分析消息（如果之前没有创建）
              console.log('[DEBUG] 创建新的审核分析消息:', structuredResponse.analysis);
              addMessage(structuredResponse.analysis, MESSAGE_TYPES.AUDIT_ANALYSIS);
            }
          } else if (structuredResponse.audit_conclusion && structuredResponse.audit_conclusion.trim()) {
            // 如果没有分析内容但有审核结论，使用审核结论作为分析内容
            const conclusionContent = cleanJsonFromText(structuredResponse.audit_conclusion);
            if (analysisMsgId) {
              console.log('[DEBUG] 使用审核结论更新分析内容:', conclusionContent);
              updateMessage(analysisMsgId, {
                content: conclusionContent,
                type: MESSAGE_TYPES.AUDIT_ANALYSIS
              });
            } else {
              console.log('[DEBUG] 使用审核结论创建分析消息:', conclusionContent);
              addMessage(conclusionContent, MESSAGE_TYPES.AUDIT_ANALYSIS);
            }
          } else {
            console.log('[DEBUG] 没有分析内容或审核结论可显示');
          }
          
          // 处理结构化响应中的额外信息
          await handleAuditAgentAction(structuredResponse, content);
        } catch (error) {
          console.error('handleAuditAgentAction failed:', error);
          addMessage(`处理AI响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
        }
      } else if (!streamingBuffer.trim()) {
        // 如果没有收到任何内容
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        addMessage('❌ 没有收到响应内容', MESSAGE_TYPES.ERROR);
      } else {
        // 只有流式内容，没有结构化响应
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        const cleanedContent = cleanJsonFromText(streamingBuffer);
        if (analysisMsgId) {
          // 更新现有消息的最终内容
          updateMessage(analysisMsgId, { 
            content: cleanedContent,
            type: MESSAGE_TYPES.AUDIT_ANALYSIS 
          });
        } else {
          // 如果没有分析消息ID，创建新消息
          addMessage(cleanedContent, MESSAGE_TYPES.AUDIT_ANALYSIS);
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`发送失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('发送消息失败', 'error');
      } else {
        // 如果是用户主动停止，清除思考消息
        setMessages(prev => prev.filter(msg =>
          msg.type !== MESSAGE_TYPES.THINKING &&
          msg.type !== MESSAGE_TYPES.ANALYSIS &&
          msg.type !== MESSAGE_TYPES.REASONING &&
          msg.type !== MESSAGE_TYPES.CREATING_CARDS
        ));
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      streamingMessageRef.current = null;
      setAutoLooping(false);
      autoLoopRef.current = false;
    }
  };

  // 处理文件选择
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    console.log('handleFileSelect files:', fileArray);
    const newFiles = fileArray
      .filter(file => file && file.name)
      .map(file => ({
        id: Date.now() + Math.random(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type
          ? (file.type.startsWith('image/') ? 'image' : (file.type === 'application/pdf' ? 'pdf' : 'file'))
          : 'file'
      }));
    console.log('newFiles:', newFiles);
    setUploadedFiles(prev => {
      const result = [...prev, ...newFiles];
      console.log('setUploadedFiles result:', result);
      return result;
    });
  };

  // 删除文件
  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 单据审核模式的 sendMessage
  const sendMessage = async (content) => {
    try {
      // 单据审核功能
      if (!uploadedFiles || uploadedFiles.length === 0) {
        showToast('请上传单据文件进行审核', 'error');
        return;
      }
      
      // 1. 立即设置加载和流式状态
      setIsLoading(true);
      setIsStreaming(true);

      // 2. 保存当前文件列表并立即清空显示
      const currentFiles = [...uploadedFiles];
      setUploadedFiles([]);

      // 3. 立即插入文件消息，并记录消息ID用于后续更新
      const fileMsgIds = [];
      currentFiles.forEach(fileInfo => {
        const localUrl = URL.createObjectURL(fileInfo.file);
        const msg = addMessage('', MESSAGE_TYPES.FILE, {
          fileName: fileInfo.name,
          fileType: fileInfo.type,
          processing: true,
          fileUrl: localUrl,
        });
        fileMsgIds.push(msg.id);
      });

      // 4. 上传文件
      let fileInfos = [];
      for (let i = 0; i < currentFiles.length; i++) {
        const fileInfo = currentFiles[i];
        const formData = new FormData();
        formData.append('file', fileInfo.file);
        try {
          const response = await fetch(`${backendBase}/agent/v2/upload`, {
            method: 'POST',
            headers: {
              'X-API-Key': aiConfig.api_key,
              'X-Base-URL': aiConfig.base_url,
              'X-Model': aiConfig.model
            },
            body: formData
          });
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          const result = await response.json();
          if (result.success && result.file_info) {
            fileInfos.push({
              file_path: result.file_info.path, // 用本地路径给后端
              file_name: fileInfo.name,
              file_type: fileInfo.type
            });
            // 上传成功，更新消息 processing: false 和 fileUrl
            updateMessage(fileMsgIds[i], { processing: false, fileUrl: `${backendBase}${result.file_info.url}` });
          } else {
            updateMessage(fileMsgIds[i], { processing: false, error: true });
          }
        } catch (error) {
          console.error('文件上传失败:', error);
          showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
          updateMessage(fileMsgIds[i], { processing: false, error: true });
        }
      }

      // 5. 发送单据审核请求
      setContext({});
      setAutoLooping(true);
      autoLoopRef.current = true;
      await sendAuditRequestWithContext(content, { files: fileInfos });
      setIsLoading(false);
      setIsStreaming(false);
    } catch (error) {
      console.error('DEBUG: Error in sendMessage:', error);
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      // 清除思考和分析消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.REASONING &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      // 添加用户反馈消息
      addMessage('生成已停止', MESSAGE_TYPES.SYSTEM);
      showToast('生成已停止');
      // 重置状态
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
    }
  };

  return {
    sendMessage,
    stopGeneration,
    handleFileSelect,
    removeFile,
    renderFunctionIndicator: () => (
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-2 text-blue-700">
          <CheckCircle size={16} />
          <span className="text-sm font-medium">单据审核模式</span>
          <span className="text-xs bg-blue-100 px-2 py-1 rounded-full">已启用</span>
        </div>
        <p className="text-xs text-blue-600 mt-1">请上传单据文件进行智能审核</p>
      </div>
    ),
    placeholder: "上传单据进行审核..."
  };
};

export default Audit;