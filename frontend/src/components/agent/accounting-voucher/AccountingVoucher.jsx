import React, { useState, useCallback, useRef } from 'react';
import { FileText } from 'lucide-react';
import { MESSAGE_TYPES } from '../common/messageTypes';
import api from '../../../api';
import { backendBase } from '../../../api';

const AccountingVoucher = ({
  messages,
  setMessages,
  input,
  setInput,
  isLoading,
  setIsLoading,
  isStreaming,
  setIsStreaming,
  sessionId,
  setSessionId,
  abortController,
  setAbortController,
  uploadedFiles,
  setUploadedFiles,
  aiConfig,
  addMessage,
  updateMessage,
  showToast,
  configureAgent,
  setContext,
  setAutoLooping,
  autoLoopRef,
  sendMessageWithContext,
  processCards
}) => {
  const streamingMessageRef = useRef(null);

  // 处理文件选择
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    console.log('handleFileSelect files:', fileArray);
    const newFiles = fileArray
      .filter(file => file && file.name)
      .map(file => ({
        id: Date.now() + Math.random(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type
          ? (file.type.startsWith('image/') ? 'image' : (file.type === 'application/pdf' ? 'pdf' : 'file'))
          : 'file'
      }));
    console.log('newFiles:', newFiles);
    setUploadedFiles(prev => {
      const result = [...prev, ...newFiles];
      console.log('setUploadedFiles result:', result);
      return result;
    });
  };

  // 删除文件
  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 记账凭证模式的 sendMessage
  const sendMessage = async (content) => {
    try {
      // 记账凭证功能 - 现有流程
      // 1. 立即设置加载和流式状态
      setIsLoading(true);
      setIsStreaming(true);

      // 2. 保存当前文件列表并立即清空显示
      const currentFiles = [...uploadedFiles];
      setUploadedFiles([]);

      // 3. 立即插入文件消息，并记录消息ID用于后续更新
      const fileMsgIds = [];
      currentFiles.forEach(fileInfo => {
        const localUrl = URL.createObjectURL(fileInfo.file);
        const msg = addMessage('', MESSAGE_TYPES.FILE, {
          fileName: fileInfo.name,
          fileType: fileInfo.type,
          processing: true,
          fileUrl: localUrl,
        });
        fileMsgIds.push(msg.id);
      });

      // 4. 上传文件
      let fileInfos = [];
      for (let i = 0; i < currentFiles.length; i++) {
        const fileInfo = currentFiles[i];
        const formData = new FormData();
        formData.append('file', fileInfo.file);
        try {
          const response = await fetch(`${backendBase}/agent/v2/upload`, {
            method: 'POST',
            headers: {
              'X-API-Key': aiConfig.api_key,
              'X-Base-URL': aiConfig.base_url,
              'X-Model': aiConfig.model
            },
            body: formData
          });
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          const result = await response.json();
          if (result.success && result.file_info) {
            fileInfos.push({
              file_path: result.file_info.path, // 用本地路径给后端
              file_name: fileInfo.name,
              file_type: fileInfo.type
            });
            // 3.1 上传成功，更新消息 processing: false 和 fileUrl
            updateMessage(fileMsgIds[i], { processing: false, fileUrl: `${backendBase}${result.file_info.url}` });
          } else {
            updateMessage(fileMsgIds[i], { processing: false, error: true });
          }
        } catch (error) {
          console.error('文件上传失败:', error);
          showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
          updateMessage(fileMsgIds[i], { processing: false, error: true });
        }
      }

      setContext({});
      setAutoLooping(true);
      autoLoopRef.current = true;
      await sendMessageWithContext(content, { files: fileInfos });
      setIsLoading(false);
      setIsStreaming(false);
    } catch (error) {
      console.error('DEBUG: Error in sendMessage:', error);
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      // 清除思考和分析消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      // 添加用户反馈消息
      addMessage('生成已停止', MESSAGE_TYPES.SYSTEM);
      showToast('生成已停止');
      // 重置状态
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
    }
  };

  return {
    sendMessage,
    stopGeneration,
    handleFileSelect,
    removeFile,
    renderFunctionIndicator: () => (
      <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
        <div className="flex items-center gap-2 text-purple-700">
          <FileText size={16} />
          <span className="text-sm font-medium">记账凭证模式</span>
          <span className="text-xs bg-purple-100 px-2 py-1 rounded-full">已启用</span>
        </div>
        <p className="text-xs text-purple-600 mt-1">请上传凭证进行记账凭证生成</p>
      </div>
    ),
    placeholder: "输入消息... (Shift+Enter 换行)"
  };
};

export default AccountingVoucher;