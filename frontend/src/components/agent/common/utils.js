// 工具函数：提取AI回复中的json代码块内容
export function extractJsonFromCodeBlock(content) {
  // 匹配```json:xxx```或```json\nxxx```或```json\r\nxxx```等
  const match = content.match(/```json[:\n\r]*([\s\S]*?)```/i);
  if (match) {
    return match[1].trim();
  }
  // 兼容形如json: {...} 但无代码块
  const match2 = content.match(/json[:\n\r]*([\s\S]*)/i);
  if (match2) {
    try {
      // 尝试解析json
      const jsonStr = match2[1].trim();
      JSON.parse(jsonStr);
      return jsonStr;
    } catch {
      // 不是标准json，原样返回
      return match2[1].trim();
    }
  }
  return content;
}

// 工具函数：清理文本中的JSON格式化内容，返回纯净的用户友好文本
export function cleanJsonFromText(content) {
  if (!content || typeof content !== 'string') return content || '';
  
  let cleaned = content;
  
  // 1. 移除JSON代码块格式（```json ... ```）
  cleaned = cleaned.replace(/```json[\s\S]*?```/gi, '');
  
  // 2. 移除独立的JSON对象格式（只移除完整的JSON对象）
  cleaned = cleaned.replace(/\{\s*"[^"]*"\s*:\s*[\s\S]*?\}\s*$/g, '');
  
  // 3. 移除特定的中英文混杂格式
  cleaned = cleaned.replace(/\*\*[^*]+\*\*[：:]\s*[a-zA-Z_]+[（(][^)）]*[)）]/g, '');
  
  // 4. 移除可能的 "json:" 前缀
  cleaned = cleaned.replace(/^json[:\s]*/i, '');
  
  // 5. 清理多余的空白字符，但保留换行和格式
  cleaned = cleaned.replace(/[ \t]+/g, ' ');
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
  cleaned = cleaned.trim();
  
  // 6. 如果清理后内容为空或太短，返回原始内容（但移除JSON部分）
  if (!cleaned || cleaned.length < 10) {
    // 尝试提取非JSON部分
    let fallback = content.replace(/```json[\s\S]*?```/gi, '');
    fallback = fallback.replace(/\{\s*"[\s\S]*?\}\s*$/g, '');
    fallback = fallback.trim();
    return fallback || content.trim();
  }
  
  return cleaned;
}

// 工具函数：清理文本中的特殊标记，如<|consultation_conclusion|>等
export function cleanSpecialTags(content) {
  if (!content || typeof content !== 'string') return content || '';
  
  // 移除所有特殊标记格式 <|tag|> 和 </|tag|>
  let cleaned = content.replace(/<\|[^|]+\|>/g, '').replace(/<\|\/[^|]+\|>/g, '');
  
  // 清理多余的空白字符，但保留换行和格式
  cleaned = cleaned.replace(/[ \t]+/g, ' ');
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
  cleaned = cleaned.trim();
  
  return cleaned;
}