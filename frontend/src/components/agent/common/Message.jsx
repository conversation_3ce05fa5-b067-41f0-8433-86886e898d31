import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { RotateCcw, Loader, AlertCircle, MessageCircle, FileText, Image } from 'lucide-react';
import SubjectCard from '../../SubjectCard';
import AssetCard from '../../AssetCard';
import StaffCard from '../../StaffCard';
import VoucherCard from '../../VoucherCard';
import { MESSAGE_TYPES } from './messageTypes';
import { cleanJsonFromText, extractJsonFromCodeBlock } from './utils';

const Message = ({ message, onRetry, handleCardConfirm, sendMessageWithContext }) => {
  const [showPreview, setShowPreview] = useState(false);

  // 支持 ESC 关闭预览弹窗
  useEffect(() => {
    if (!showPreview) return;
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') setShowPreview(false);
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showPreview]);

  // 文件预览弹窗
  const renderPreviewModal = () => {
    if (!message.fileUrl) return null;
    if (message.fileType === 'image') {
      return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4" onClick={() => setShowPreview(false)}>
          <div className="relative max-w-[95vw] max-h-[95vh] flex items-center justify-center">
            <img
              src={message.fileUrl}
              alt={message.fileName}
              className="max-w-full max-h-full object-contain rounded-xl shadow-2xl"
              onClick={e => e.stopPropagation()}
            />
            <button
              onClick={() => setShowPreview(false)}
              className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200"
            >
              ×
            </button>
          </div>
        </div>
      );
    }
    if (message.fileType === 'pdf') {
      return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4" onClick={() => setShowPreview(false)}>
          <div className="bg-white rounded-2xl shadow-2xl max-w-[95vw] max-h-[95vh] p-6 flex flex-col" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">{message.fileName}</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="w-8 h-8 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
              >
                ×
              </button>
            </div>
            <iframe
              src={message.fileUrl}
              title={message.fileName}
              className="flex-1 w-full border rounded-xl"
              style={{ minHeight: '70vh' }}
            />
          </div>
        </div>
      );
    }
    return null;
  };

  const renderContent = () => {
    switch (message.type) {
      case MESSAGE_TYPES.USER:
        return (
          <div className="bg-gray-100 border border-gray-300 text-black p-4 rounded-2xl max-w-[80%] ml-auto shadow-lg">
            <div className="leading-relaxed font-medium">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.ASSISTANT:
        // 新增：自动提取json代码块并清理内容
        const displayContent = cleanJsonFromText(extractJsonFromCodeBlock(message.content));
        return (
          <div className="bg-white border border-gray-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-start justify-between">
              <div className="flex-1 text-gray-800 leading-relaxed prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                    h1: ({children}) => <h1 className="text-lg font-bold mb-2 text-gray-900">{children}</h1>,
                    h2: ({children}) => <h2 className="text-base font-semibold mb-2 text-gray-900">{children}</h2>,
                    h3: ({children}) => <h3 className="text-sm font-medium mb-1 text-gray-800">{children}</h3>,
                    ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                    ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                    li: ({children}) => <li className="text-gray-800">{children}</li>,
                    strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                    em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                    code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono text-gray-900">{children}</code>,
                    blockquote: ({children}) => <blockquote className="border-l-2 border-gray-300 pl-3 italic text-gray-700">{children}</blockquote>,
                    pre: ({children}) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto text-sm">{children}</pre>
                  }}
                >
                  {displayContent}
                </ReactMarkdown>
              </div>
              {message.error && (
                <button
                  onClick={() => onRetry(message.id)}
                  className="ml-3 text-red-500 hover:text-red-700 hover:bg-red-50 p-1.5 rounded-lg transition-all duration-200"
                  title="重试"
                >
                  <RotateCcw size={16} />
                </button>
              )}
            </div>
          </div>
        );
      case MESSAGE_TYPES.FILE:
        return (
          <div className="flex justify-end">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-2xl max-w-xs shadow-lg flex items-center gap-3">
              {message.fileType === 'image' && message.fileUrl && (
                <img
                  src={message.fileUrl}
                  alt={message.fileName}
                  className="w-12 h-12 object-cover rounded-lg cursor-pointer border border-white/20"
                  onClick={() => setShowPreview(true)}
                />
              )}
              {message.fileType === 'pdf' && (
                <div className="flex items-center gap-2">
                  <FileText size={16} className="text-white" />
                  <span
                    className="font-medium text-white cursor-pointer hover:underline text-sm truncate"
                    onClick={() => message.fileUrl && setShowPreview(true)}
                  >
                    {message.fileName}
                  </span>
                </div>
              )}
              {!message.fileUrl && (
                <div className="flex items-center gap-2">
                  {message.fileType === 'image' ? (
                    <Image size={16} className="text-white" />
                  ) : (
                    <FileText size={16} className="text-white" />
                  )}
                  <span className="text-white text-sm truncate">{message.fileName}</span>
                </div>
              )}
              {message.processing && <Loader className="animate-spin text-white" size={16} />}
              {message.error && <AlertCircle className="text-red-300" size={16} />}
              {showPreview && renderPreviewModal()}
            </div>
          </div>
        );
      case MESSAGE_TYPES.CARD:
        return (
          <div className="space-y-4 max-w-full">
            {message.cards && message.cards.map((card, index) => {
              if (card.skip) return null;

              // 合并props，优先级：props > data > card
              const cardProps = {
                ...(card.data || {}),
                ...(card.props || {}),
                editable: card._pending,
                onConfirm: card._pending ? (data) => handleCardConfirm(card, message.id, index) : undefined,
                type: card.type,
              };

              // 直接渲染卡片组件，不添加额外的包装容器
              if (card.type === 'subject') return <SubjectCard key={index} {...cardProps} />;
              if (card.type === 'asset') return <AssetCard key={index} {...cardProps} />;
              if (card.type === 'staff') return <StaffCard key={index} {...cardProps} />;
              if (card.type === 'voucher') return <VoucherCard key={index} {...cardProps} />;
              return null;
            })}
          </div>
        );
      case MESSAGE_TYPES.ERROR:
        return (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 p-4 rounded-2xl max-w-[80%] shadow-soft overflow-hidden">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-red-500 rounded-xl text-white shadow-lg">
                <AlertCircle size={16} />
              </div>
              <div>
                <div className="font-semibold text-red-800 mb-1">发生错误</div>
                <div
                  className="text-red-700 leading-relaxed overflow-hidden"
                  style={{
                    wordBreak: 'break-all',
                    overflowWrap: 'break-word',
                    hyphens: 'auto',
                    maxWidth: '100%',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {message.content}
                </div>
              </div>
            </div>
          </div>
        );
      case MESSAGE_TYPES.THINKING:
        return (
          <div className="flex items-center gap-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="relative">
              <div className="w-6 h-6 border-2 border-blue-200 rounded-full animate-spin">
                <div className="absolute inset-0 border-2 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
              </div>
            </div>
            <span className="text-gray-700 font-medium">智能体思考中<span className="loading-dots"></span></span>
          </div>
        );
      case MESSAGE_TYPES.ANALYSIS:
        return (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="text-gray-800 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.CREATING_CARDS:
        return (
          <div className="flex items-center gap-2 bg-purple-50 p-3 rounded-lg max-w-[80%]">
            <Loader className="animate-spin text-purple-400" size={20} />
            <span className="text-purple-700">正在创建卡片...</span>
          </div>
        );
      case MESSAGE_TYPES.SYSTEM:
        return (
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">系统消息</span>
            </div>
            <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.INFO:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-2xl max-w-[80%] shadow-soft">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-xs font-medium text-blue-500 uppercase tracking-wide">提示信息</span>
            </div>
            <div className="text-blue-800 whitespace-pre-wrap leading-relaxed">{message.content}</div>
          </div>
        );
      case MESSAGE_TYPES.AUDIT_ANALYSIS:
        return (
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-2 h-2 bg-emerald-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
              <span className="text-xs font-medium text-emerald-600 uppercase tracking-wide">审核分析</span>
            </div>
            <div className="text-emerald-800 leading-relaxed text-sm prose prose-sm max-w-none">
              <ReactMarkdown
                components={{
                  p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                  h1: ({children}) => <h1 className="text-lg font-bold mb-2 text-emerald-900">{children}</h1>,
                  h2: ({children}) => <h2 className="text-base font-semibold mb-2 text-emerald-900">{children}</h2>,
                  h3: ({children}) => <h3 className="text-sm font-medium mb-1 text-emerald-800">{children}</h3>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                  li: ({children}) => <li className="text-emerald-800">{children}</li>,
                  strong: ({children}) => <strong className="font-semibold text-emerald-900">{children}</strong>,
                  em: ({children}) => <em className="italic text-emerald-700">{children}</em>,
                  code: ({children}) => <code className="bg-emerald-100 px-1 py-0.5 rounded text-xs font-mono text-emerald-900">{children}</code>,
                  blockquote: ({children}) => <blockquote className="border-l-2 border-emerald-300 pl-3 italic text-emerald-700">{children}</blockquote>
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        );
      case MESSAGE_TYPES.AUDIT_RESULT:
        return (
          <div className={`p-4 rounded-2xl max-w-[80%] shadow-soft border-2 ${
            message.result === 'audit_complete' 
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300' 
              : message.result === 'audit_rejected'
              ? 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'
              : 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-300'
          }`}>
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full ${
                message.result === 'audit_complete' 
                  ? 'bg-green-500' 
                  : message.result === 'audit_rejected'
                  ? 'bg-red-500'
                  : 'bg-amber-500'
              }`}></div>
              <span className={`text-xs font-bold uppercase tracking-wide ${
                message.result === 'audit_complete' 
                  ? 'text-green-600' 
                  : message.result === 'audit_rejected'
                  ? 'text-red-600'
                  : 'text-amber-600'
              }`}>
                {message.result === 'audit_complete' ? '✅ 审核通过' : 
                 message.result === 'audit_rejected' ? '❌ 审核不通过' : 
                 '⚠️ 需要补充信息'}
              </span>
            </div>
            <div className={`font-medium leading-relaxed prose prose-sm max-w-none ${
              message.result === 'audit_complete' 
                ? 'text-green-800' 
                : message.result === 'audit_rejected'
                ? 'text-red-800'
                : 'text-amber-800'
            }`}>
              <ReactMarkdown
                components={{
                  p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                  h1: ({children}) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                  h2: ({children}) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                  h3: ({children}) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                  li: ({children}) => <li>{children}</li>,
                  strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                  em: ({children}) => <em className="italic">{children}</em>,
                  code: ({children}) => <code className="bg-white bg-opacity-50 px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                  blockquote: ({children}) => <blockquote className="border-l-2 border-current pl-3 italic opacity-80">{children}</blockquote>
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        );
      case MESSAGE_TYPES.AUDIT_INFO_REQUEST:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-300 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-xs font-bold text-blue-600 uppercase tracking-wide">📋 需要补充信息</span>
            </div>
            {message.required_info && message.required_info.length > 0 ? (
              <div className="space-y-3">
                <div className="text-blue-800 font-medium prose prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                      strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                      em: ({children}) => <em className="italic">{children}</em>
                    }}
                  >
                    {message.content || '请提供以下信息以完成审核：'}
                  </ReactMarkdown>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-200">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-blue-200">
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">序号</th>
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">需要补充的信息</th>
                      </tr>
                    </thead>
                    <tbody>
                      {message.required_info.map((info, index) => (
                        <tr key={index} className="border-b border-blue-100 last:border-b-0">
                          <td className="py-2 px-1 text-blue-600 font-medium">{index + 1}</td>
                          <td className="py-2 px-1 text-blue-800 prose prose-sm max-w-none">
                            <ReactMarkdown
                              components={{
                                p: ({children}) => <span>{children}</span>,
                                strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                                em: ({children}) => <em className="italic">{children}</em>,
                                code: ({children}) => <code className="bg-blue-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                              }}
                            >
                              {info}
                            </ReactMarkdown>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。
                </div>
              </div>
            ) : (
              <div className="text-blue-800 prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                    strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                    em: ({children}) => <em className="italic">{children}</em>,
                    code: ({children}) => <code className="bg-blue-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                  }}
                >
                  {message.content}
                </ReactMarkdown>
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg mt-2">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。
                </div>
              </div>
            )}
          </div>
        );
      case MESSAGE_TYPES.REASONING:
        return (
          <div className="bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-xs font-bold text-purple-600 uppercase tracking-wide">🧠 推理过程</span>
            </div>
            <div className="text-purple-800 leading-relaxed text-sm prose prose-sm max-w-none">
              <ReactMarkdown
                components={{
                  p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                  h1: ({children}) => <h1 className="text-lg font-bold mb-2 text-purple-900">{children}</h1>,
                  h2: ({children}) => <h2 className="text-base font-semibold mb-2 text-purple-900">{children}</h2>,
                  h3: ({children}) => <h3 className="text-sm font-medium mb-1 text-purple-800">{children}</h3>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                  li: ({children}) => <li className="text-purple-800">{children}</li>,
                  strong: ({children}) => <strong className="font-semibold text-purple-900">{children}</strong>,
                  em: ({children}) => <em className="italic text-purple-700">{children}</em>,
                  code: ({children}) => <code className="bg-purple-100 px-1 py-0.5 rounded text-xs font-mono text-purple-900">{children}</code>,
                  blockquote: ({children}) => <blockquote className="border-l-2 border-purple-300 pl-3 italic text-purple-700">{children}</blockquote>
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        );
      case MESSAGE_TYPES.CONSULTATION_ANALYSIS:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
              <span className="text-xs font-medium text-blue-600 uppercase tracking-wide">财务分析</span>
            </div>
            <div className="text-blue-800 leading-relaxed text-sm prose prose-sm max-w-none">
              <ReactMarkdown
                components={{
                  p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                  h1: ({children}) => <h1 className="text-lg font-bold mb-2 text-blue-900">{children}</h1>,
                  h2: ({children}) => <h2 className="text-base font-semibold mb-2 text-blue-900">{children}</h2>,
                  h3: ({children}) => <h3 className="text-sm font-medium mb-1 text-blue-800">{children}</h3>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                  li: ({children}) => <li className="text-blue-800">{children}</li>,
                  strong: ({children}) => <strong className="font-semibold text-blue-900">{children}</strong>,
                  em: ({children}) => <em className="italic text-blue-700">{children}</em>,
                  code: ({children}) => <code className="bg-blue-100 px-1 py-0.5 rounded text-xs font-mono text-blue-900">{children}</code>,
                  blockquote: ({children}) => <blockquote className="border-l-2 border-blue-300 pl-3 italic text-blue-700">{children}</blockquote>
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        );
      case MESSAGE_TYPES.CONSULTATION_RESULT:
        return (
          <div className={`p-4 rounded-2xl max-w-[80%] shadow-soft border-2 ${
            message.result === 'provide_advice'
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'
              : message.result === 'request_more_info'
              ? 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-300'
              : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-300'
          }`}>
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full ${
                message.result === 'provide_advice'
                  ? 'bg-green-500'
                  : message.result === 'request_more_info'
                  ? 'bg-amber-500'
                  : 'bg-blue-500'
              }`}></div>
              <span className={`text-xs font-bold uppercase tracking-wide ${
                message.result === 'provide_advice'
                  ? 'text-green-600'
                  : message.result === 'request_more_info'
                  ? 'text-amber-600'
                  : 'text-blue-600'
              }`}>
                {message.result === 'provide_advice' ? '💡 专业建议' :
                 message.result === 'request_more_info' ? '📋 需要补充信息' :
                 '📊 咨询结果'}
              </span>
            </div>
            <div className={`font-medium leading-relaxed prose prose-sm max-w-none ${
              message.result === 'provide_advice'
                ? 'text-green-800'
                : message.result === 'request_more_info'
                ? 'text-amber-800'
                : 'text-blue-800'
            }`}>
              <ReactMarkdown
                components={{
                  p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                  h1: ({children}) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                  h2: ({children}) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                  h3: ({children}) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                  ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                  li: ({children}) => <li>{children}</li>,
                  strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                  em: ({children}) => <em className="italic">{children}</em>,
                  code: ({children}) => <code className="bg-white bg-opacity-50 px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                  blockquote: ({children}) => <blockquote className="border-l-2 border-current pl-3 italic opacity-80">{children}</blockquote>
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        );
      case MESSAGE_TYPES.CONSULTATION_INFO_REQUEST:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-300 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-xs font-bold text-blue-600 uppercase tracking-wide">📋 需要补充信息</span>
            </div>
            {message.required_info && message.required_info.length > 0 ? (
              <div className="space-y-3">
                <div className="text-blue-800 font-medium prose prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                      strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                      em: ({children}) => <em className="italic">{children}</em>
                    }}
                  >
                    {message.content || '请提供以下信息以完成财务咨询：'}
                  </ReactMarkdown>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-200">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-blue-200">
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">序号</th>
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">需要补充的信息</th>
                      </tr>
                    </thead>
                    <tbody>
                      {message.required_info.map((info, index) => (
                        <tr key={index} className="border-b border-blue-100 last:border-b-0">
                          <td className="py-2 px-1 text-blue-600 font-medium">{index + 1}</td>
                          <td className="py-2 px-1 text-blue-800 prose prose-sm max-w-none">
                            <ReactMarkdown
                              components={{
                                p: ({children}) => <span>{children}</span>,
                                strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                                em: ({children}) => <em className="italic">{children}</em>,
                                code: ({children}) => <code className="bg-blue-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                              }}
                            >
                              {info}
                            </ReactMarkdown>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交咨询。
                </div>
              </div>
            ) : (
              <div className="text-blue-800 prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                    strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                    em: ({children}) => <em className="italic">{children}</em>,
                    code: ({children}) => <code className="bg-blue-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                  }}
                >
                  {message.content}
                </ReactMarkdown>
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg mt-2">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交咨询。
                </div>
              </div>
            )}
          </div>
        );
      case MESSAGE_TYPES.CONSULTATION_RECOMMENDATIONS:
        return (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-300 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-xs font-bold text-green-600 uppercase tracking-wide">💡 专业建议</span>
            </div>
            {message.recommendations && Array.isArray(message.recommendations) && message.recommendations.length > 0 ? (
              <div className="space-y-3">
                <div className="text-green-800 font-medium prose prose-sm max-w-none">
                  <ReactMarkdown
                    components={{
                      p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                      strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                      em: ({children}) => <em className="italic">{children}</em>
                    }}
                  >
                    {message.content || '根据您的财务情况，我们提供以下专业建议：'}
                  </ReactMarkdown>
                </div>
                <div className="bg-white rounded-lg p-3 border border-green-200">
                  <ol className="list-decimal list-inside space-y-2">
                    {message.recommendations.map((recommendation, index) => (
                      <li key={index} className="text-green-800 prose prose-sm max-w-none">
                        <ReactMarkdown
                          components={{
                            p: ({children}) => <span>{children}</span>,
                            strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                            em: ({children}) => <em className="italic">{children}</em>,
                            code: ({children}) => <code className="bg-green-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                          }}
                        >
                          {recommendation}
                        </ReactMarkdown>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
            ) : (
              <div className="text-green-800 prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    p: ({children}) => <p className="mb-2 last:mb-0">{children}</p>,
                    strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                    em: ({children}) => <em className="italic">{children}</em>,
                    code: ({children}) => <code className="bg-green-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                  }}
                >
                  {message.content}
                </ReactMarkdown>
              </div>
            )}
          </div>
        );
      default:
        return <div className="p-3">{message.content}</div>;
    }
  };

  // 判断是否为用户消息（包括文本消息和文件消息）
  const isUserMessage = message.type === MESSAGE_TYPES.USER || message.type === MESSAGE_TYPES.FILE;

  return (
    <div className="mb-4">
      <div className={`flex items-start gap-2 ${isUserMessage ? 'justify-end' : 'justify-start'}`}>
        <div className={`flex-1 ${isUserMessage ? 'flex justify-end' : ''}`}>
          {renderContent()}
        </div>
      </div>
      <div className={`text-xs text-gray-500 mt-1 px-3 ${isUserMessage ? 'text-right' : 'text-left'}`}>
        {new Date(message.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default Message;