import React, { useEffect } from 'react';
import { AlertCircle, MessageCircle, CheckCircle } from 'lucide-react';

const Toast = ({ message, type = 'success', onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  const bgColor = type === 'error' ? 'bg-red-500' : type === 'info' ? 'bg-blue-500' : 'bg-green-500';
  const icon = type === 'error' ? <AlertCircle size={20} /> : type === 'info' ? <MessageCircle size={20} /> : <CheckCircle size={20} />;

  return (
    <div className={`fixed top-6 right-6 z-50 ${bgColor} text-white px-4 py-2 rounded shadow flex items-center`}>
      {icon}
      <span className="ml-2">{message}</span>
    </div>
  );
};

export default Toast;