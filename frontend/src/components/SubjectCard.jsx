import React, { useState } from 'react';

const SubjectCard = (props) => {
  const { onConfirm = () => {}, editable = true, type, color, ...rest } = props;
  console.log('SubjectCard props', props);
  const [form, setForm] = useState(rest || {
    科目编码: '',
    科目名称: '',
    类别: '',
    方向: '',
    备注: ''
  });
  const [isEditing, setIsEditing] = useState(editable);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleConfirm = () => {
    if (onConfirm) onConfirm(form);
    setIsEditing(false);
  };

  return (
    <div className={`rounded-lg shadow p-4 border mb-2 ${color || ''}`}>
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold">科目卡片</h3>
        {isEditing ? null : (
          <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
        )}
      </div>
      <div className="space-y-2">
        <div>
          <label className="block text-sm text-gray-600">科目编码</label>
          <input name="科目编码" value={form.科目编码} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">科目名称</label>
          <input name="科目名称" value={form.科目名称} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">类别</label>
          <input name="类别" value={form.类别} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">方向</label>
          <input name="方向" value={form.方向} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">备注</label>
          <input name="备注" value={form.备注} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
      </div>
      {isEditing && (
        <div className="mt-4 flex justify-end">
          <button className="bg-blue-500 text-white px-4 py-1 rounded" onClick={handleConfirm}>确认</button>
        </div>
      )}
    </div>
  );
};

export default SubjectCard; 