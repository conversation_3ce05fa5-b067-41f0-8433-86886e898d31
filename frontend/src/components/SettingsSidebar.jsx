import React, { useState } from 'react';
import { Settings as SettingsIcon, Database, Server, ChevronRight } from 'lucide-react';

const SettingsSidebar = ({ activeSetting, onSettingSelect }) => {
  const settings = [
    {
      id: 'settings',
      name: 'AI服务器设置',
      icon: <SettingsIcon size={18} />,
      description: '配置AI服务连接参数'
    },
    {
      id: 'rag',
      name: 'RAG管理',
      icon: <Database size={18} />,
      description: '管理检索增强生成资源'
    },
    {
      id: 'mcp',
      name: 'MCP服务',
      icon: <Server size={18} />,
      description: '管理模型控制协议服务'
    }
  ];

  return (
    <div className="p-4 space-y-2">
      {settings.map((setting) => (
        <button
          key={setting.id}
          onClick={() => onSettingSelect(setting.id)}
          className={`w-full flex items-center justify-between p-3 rounded-xl transition-all duration-200 text-left ${
            activeSetting === setting.id
              ? 'bg-blue-50 border border-blue-200 text-blue-700'
              : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              activeSetting === setting.id 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {setting.icon}
            </div>
            <div>
              <div className="font-medium text-sm">{setting.name}</div>
              <div className="text-xs text-gray-500 mt-0.5">{setting.description}</div>
            </div>
          </div>
          <ChevronRight 
            size={16} 
            className={`transition-transform duration-200 ${
              activeSetting === setting.id ? 'text-blue-500' : 'text-gray-400'
            }`} 
          />
        </button>
      ))}
    </div>
  );
};

export default SettingsSidebar;