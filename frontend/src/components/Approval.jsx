import React, { useState, useEffect } from 'react';
import { CheckCircle, Clock, XCircle, FileText, User, Calendar, Search, Filter, Eye, Check, X } from 'lucide-react';

const Approval = () => {
  const [approvals, setApprovals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');

  // 审批状态选项
  const statusOptions = [
    { value: 'pending', label: '待审批', color: 'yellow' },
    { value: 'approved', label: '已通过', color: 'green' },
    { value: 'rejected', label: '已拒绝', color: 'red' },
    { value: 'cancelled', label: '已取消', color: 'gray' }
  ];

  // 审批类型选项
  const typeOptions = [
    { value: 'voucher', label: '凭证审批' },
    { value: 'invoice', label: '发票审批' },
    { value: 'payment', label: '付款审批' },
    { value: 'reimbursement', label: '报销审批' },
    { value: 'leave', label: '请假审批' }
  ];

  // 获取审批记录
  const fetchApprovals = async () => {
    setLoading(true);
    try {
      // 模拟数据，实际应该从API获取
      const mockData = [
        {
          id: 1,
          approval_no: 'AP2024001',
          title: '办公用品采购付款申请',
          type: 'payment',
          applicant: '张三',
          department: '行政部',
          amount: 2580.00,
          submit_date: '2024-01-15',
          status: 'approved',
          approver: '李经理',
          approval_date: '2024-01-16',
          description: '采购办公文具、打印机耗材等'
        },
        {
          id: 2,
          approval_no: 'AP2024002',
          title: '差旅费报销申请',
          type: 'reimbursement',
          applicant: '王五',
          department: '销售部',
          amount: 3500.00,
          submit_date: '2024-01-18',
          status: 'pending',
          approver: null,
          approval_date: null,
          description: '上海客户拜访差旅费用'
        },
        {
          id: 3,
          approval_no: 'AP2024003',
          title: '月度凭证审核',
          type: 'voucher',
          applicant: '赵六',
          department: '财务部',
          amount: 0,
          submit_date: '2024-01-20',
          status: 'rejected',
          approver: '财务总监',
          approval_date: '2024-01-21',
          description: '2024年1月份财务凭证审核'
        },
        {
          id: 4,
          approval_no: 'AP2024004',
          title: '供应商发票审批',
          type: 'invoice',
          applicant: '钱七',
          department: '采购部',
          amount: 15000.00,
          submit_date: '2024-01-22',
          status: 'pending',
          approver: null,
          approval_date: null,
          description: 'ABC供应商1月份发票'
        },
        {
          id: 5,
          approval_no: 'AP2024005',
          title: '年假申请',
          type: 'leave',
          applicant: '孙八',
          department: '技术部',
          amount: 0,
          submit_date: '2024-01-23',
          status: 'approved',
          approver: '技术经理',
          approval_date: '2024-01-24',
          description: '春节年假申请（2月8日-2月17日）'
        }
      ];
      setApprovals(mockData);
    } catch (error) {
      console.error('获取审批记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApprovals();
  }, []);

  // 过滤审批记录
  const filteredApprovals = approvals.filter(approval => {
    const matchesSearch = approval.approval_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         approval.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         approval.applicant?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || approval.status === filterStatus;
    const matchesType = filterType === 'all' || approval.type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  // 计算统计数据
  const stats = {
    totalApprovals: approvals.length,
    pendingApprovals: approvals.filter(a => a.status === 'pending').length,
    approvedApprovals: approvals.filter(a => a.status === 'approved').length,
    rejectedApprovals: approvals.filter(a => a.status === 'rejected').length
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  // 获取类型信息
  const getTypeInfo = (type) => {
    return typeOptions.find(opt => opt.value === type) || typeOptions[0];
  };

  // 处理审批操作
  const handleApproval = async (id, action) => {
    if (!window.confirm(`确定要${action === 'approve' ? '通过' : '拒绝'}此审批吗？`)) return;
    
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setApprovals(prev => prev.map(approval =>
        approval.id === id
          ? {
              ...approval,
              status: action === 'approve' ? 'approved' : 'rejected',
              approver: '当前用户',
              approval_date: new Date().toISOString().split('T')[0]
            }
          : approval
      ));
    } catch (error) {
      console.error('审批操作失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">审批管理</h1>
        <p className="text-gray-600">管理和处理各类业务审批流程</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总审批数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalApprovals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">待审批</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.pendingApprovals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已通过</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.approvedApprovals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已拒绝</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.rejectedApprovals}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            {/* 搜索框 */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索审批号、标题或申请人..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
              />
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有状态</option>
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有类型</option>
                {typeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* 审批记录列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredApprovals.length === 0 ? (
          <div className="text-center py-12">
            <FileText size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">暂无审批记录</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审批号</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请人</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审批人</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredApprovals.map((approval) => {
                  const statusInfo = getStatusInfo(approval.status);
                  const typeInfo = getTypeInfo(approval.type);
                  
                  return (
                    <tr key={approval.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {approval.approval_no}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          <div className="font-medium">{approval.title}</div>
                          <div className="text-xs text-gray-500 mt-1">{approval.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800`}>
                          {typeInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          <div className="font-medium">{approval.applicant}</div>
                          <div className="text-xs text-gray-400">{approval.department}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {approval.amount > 0 ? `¥${approval.amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {approval.submit_date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                          statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                          statusInfo.color === 'red' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {statusInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>
                          <div className="font-medium">{approval.approver || '-'}</div>
                          {approval.approval_date && (
                            <div className="text-xs text-gray-400">{approval.approval_date}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          {approval.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApproval(approval.id, 'approve')}
                                className="text-green-600 hover:text-green-900"
                                title="通过"
                              >
                                <Check size={16} />
                              </button>
                              <button
                                onClick={() => handleApproval(approval.id, 'reject')}
                                className="text-red-600 hover:text-red-900"
                                title="拒绝"
                              >
                                <X size={16} />
                              </button>
                            </>
                          )}
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <Eye size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Approval;