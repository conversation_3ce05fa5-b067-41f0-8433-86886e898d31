import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import WorkspaceItem from './WorkspaceItem';

const VirtualScrollWorkspace = ({ 
  items, 
  onRemove, 
  onPin, 
  onUnpin, 
  renderer,
  itemHeight = 300, // 预估的每个项目高度
  overscan = 5, // 预渲染的项目数量
  className = '',
  maxHeight = '600px'
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const containerRef = useRef(null);
  const scrollElementRef = useRef(null);

  // 监听容器大小变化
  useEffect(() => {
    const updateContainerHeight = () => {
      if (containerRef.current) {
        const height = containerRef.current.clientHeight;
        setContainerHeight(height);
      }
    };

    updateContainerHeight();
    
    // 使用 ResizeObserver 监听大小变化
    const resizeObserver = new ResizeObserver(updateContainerHeight);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);

  // 处理滚动事件
  const handleScroll = useCallback((event) => {
    setScrollTop(event.target.scrollTop);
  }, []);

  // 计算可见项目
  const getVisibleItems = useCallback(() => {
    if (!containerHeight) return { visibleItems: [], startIndex: 0, endIndex: 0, totalHeight: 0 };

    const totalHeight = items.length * itemHeight;
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );

    const visibleItems = items.slice(startIndex, endIndex + 1);

    return {
      visibleItems,
      startIndex,
      endIndex,
      totalHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, scrollTop, containerHeight, itemHeight, overscan]);

  const { visibleItems, startIndex, endIndex, totalHeight, offsetY } = getVisibleItems();

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ maxHeight }}
    >
      {/* 滚动容器 */}
      <div
        ref={scrollElementRef}
        className="overflow-auto h-full"
        onScroll={handleScroll}
        style={{ maxHeight }}
      >
        {/* 占位容器，用于创建滚动条 */}
        <div style={{ height: totalHeight, position: 'relative' }}>
          {/* 可见项目 */}
          <div
            style={{
              position: 'absolute',
              top: offsetY,
              left: 0,
              right: 0,
              willChange: 'transform'
            }}
          >
            {visibleItems.map((item, index) => (
              <WorkspaceItem
                key={`${item.type}_${item.id}_${item.timestamp}`}
                item={item}
                onRemove={onRemove}
                onPin={onPin}
                onUnpin={onUnpin}
                renderer={renderer}
                className="mb-3"
                maxHeight={`${itemHeight}px`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 加载状态指示器 */}
      {items.length > 50 && (
        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          显示 {startIndex + 1}-{endIndex + 1} / {items.length}
        </div>
      )}
    </div>
  );
};

// 使用 memo 优化性能
export default memo(VirtualScrollWorkspace, (prevProps, nextProps) => {
  // 自定义比较函数，避免不必要的重新渲染
  return (
    prevProps.items.length === nextProps.items.length &&
    prevProps.itemHeight === nextProps.itemHeight &&
    prevProps.maxHeight === nextProps.maxHeight &&
    prevProps.className === nextProps.className &&
    prevProps.renderer === nextProps.renderer
  );
});