import React, { useState, useCallback, useEffect } from 'react';
import { useWorkspaceManager } from './WorkspaceProvider';
import { debounce } from '../../utils/workspaceHelpers';

/**
 * ClickableRecord HOC - 为记录添加点击和双击交互功能
 * @param {React.ComponentType} WrappedComponent - 被包装的组件
 * @param {Object} options - 配置选项
 * @returns {React.ComponentType} 包装后的组件
 */
export function withClickableRecord(WrappedComponent, options = {}) {
  const {
    type = 'default', // 记录类型
    debounceDelay = 300, // 防抖延迟
    singleClickHandler, // 自定义单击处理函数
    doubleClickHandler, // 自定义双击处理函数
    enableWorkspace = true, // 是否启用工作区功能
    highlightClass = 'ring-2 ring-blue-500 ring-opacity-50', // 高亮样式类
    activeClass = 'bg-blue-50', // 激活状态样式类
    ...componentProps // 传递给被包装组件的额外属性
  } = options;

  // 返回包装后的组件
  const ClickableRecordWrapper = React.forwardRef((props, ref) => {
    const {
      item, // 记录数据
      onClick, // 原始点击事件
      onDoubleClick, // 原始双击事件
      className = '', // 原始样式类
      ...restProps // 其他属性
    } = props;

    const { 
      handleSingleClick, 
      handleDoubleClick, 
      isItemInWorkspace 
    } = useWorkspaceManager();

    const [clickCount, setClickCount] = useState(0);
    const [isInWorkspace, setIsInWorkspace] = useState(false);
    const [isHighlighted, setIsHighlighted] = useState(false);

    // 检查记录是否在工作区中
    useEffect(() => {
      if (enableWorkspace && item && item.id) {
        const inWorkspace = isItemInWorkspace(item.id);
        setIsInWorkspace(inWorkspace);
      }
    }, [item, isItemInWorkspace, enableWorkspace]);

    // 防抖的单击处理函数
    const debouncedSingleClick = useCallback(
      debounce((event, record) => {
        if (enableWorkspace) {
          handleSingleClick(record, type);
        }
        if (singleClickHandler) {
          singleClickHandler(event, record);
        }
        if (onClick) {
          onClick(event, record);
        }
        setIsHighlighted(false);
      }, debounceDelay),
      [handleSingleClick, singleClickHandler, onClick, enableWorkspace, type, debounceDelay]
    );

    // 双击处理函数
    const handleDoubleClickEvent = useCallback((event, record) => {
      event.preventDefault();
      setClickCount(0);
      
      if (enableWorkspace) {
        handleDoubleClick(record, type);
      }
      if (doubleClickHandler) {
        doubleClickHandler(event, record);
      }
      if (onDoubleClick) {
        onDoubleClick(event, record);
      }
    }, [handleDoubleClick, doubleClickHandler, onDoubleClick, enableWorkspace, type]);

    // 点击处理函数
    const handleClick = useCallback((event, record) => {
      event.preventDefault();
      
      const newClickCount = clickCount + 1;
      setClickCount(newClickCount);
      setIsHighlighted(true);

      if (newClickCount === 1) {
        // 第一次点击，等待可能的第二次点击
        setTimeout(() => {
          if (clickCount === 1) {
            // 确认是单击
            debouncedSingleClick(event, record);
          }
          setClickCount(0);
        }, debounceDelay);
      } else if (newClickCount === 2) {
        // 第二次点击，触发双击
        handleDoubleClickEvent(event, record);
      }
    }, [clickCount, debouncedSingleClick, handleDoubleClickEvent, debounceDelay]);

    // 组合样式类
    const combinedClassName = [
      className,
      'cursor-pointer transition-all duration-200',
      isInWorkspace ? activeClass : '',
      isHighlighted ? highlightClass : '',
      'hover:shadow-sm'
    ].filter(Boolean).join(' ');

    // 渲染被包装的组件
    return (
      <WrappedComponent
        ref={ref}
        item={item}
        className={combinedClassName}
        onClick={handleClick}
        onDoubleClick={handleDoubleClickEvent}
        isInWorkspace={isInWorkspace}
        isHighlighted={isHighlighted}
        {...componentProps}
        {...restProps}
      />
    );
  });

  // 设置显示名称
  ClickableRecordWrapper.displayName = `ClickableRecord(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return ClickableRecordWrapper;
}

/**
 * ClickableRecord 组件 - 作为容器使用
 * @param {Object} props - 组件属性
 */
export function ClickableRecord({
  children,
  item,
  type = 'default',
  onClick,
  onDoubleClick,
  onSingleClick,
  className = '',
  debounceDelay = 300,
  enableWorkspace = true,
  highlightClass = 'ring-2 ring-blue-500 ring-opacity-50',
  activeClass = 'bg-blue-50',
  singleClickHandler,
  doubleClickHandler,
  as = 'div', // Allow rendering as different HTML elements
  ...restProps
}) {
  const { 
    handleSingleClick, 
    handleDoubleClick, 
    isItemInWorkspace 
  } = useWorkspaceManager();

  const [clickCount, setClickCount] = useState(0);
  const [isInWorkspace, setIsInWorkspace] = useState(false);
  const [isHighlighted, setIsHighlighted] = useState(false);

  // 检查记录是否在工作区中
  useEffect(() => {
    if (enableWorkspace && item && item.id) {
      const inWorkspace = isItemInWorkspace(item.id);
      setIsInWorkspace(inWorkspace);
    }
  }, [item, isItemInWorkspace, enableWorkspace]);

  // 防抖的单击处理函数
  const debouncedSingleClick = useCallback(
    debounce((event) => {
      if (enableWorkspace) {
        handleSingleClick(item, type);
      }
      if (singleClickHandler) {
        singleClickHandler(event, item);
      }
      if (onClick) {
        onClick(event, item);
      }
      if (onSingleClick) {
        onSingleClick(event, item);
      }
      setIsHighlighted(false);
    }, debounceDelay),
    [handleSingleClick, singleClickHandler, onClick, onSingleClick, enableWorkspace, item, type, debounceDelay]
  );

  // 双击处理函数
  const handleDoubleClickEvent = useCallback((event) => {
    event.preventDefault();
    setClickCount(0);
    
    if (enableWorkspace) {
      handleDoubleClick(item, type);
    }
    if (doubleClickHandler) {
      doubleClickHandler(event, item);
    }
    if (onDoubleClick) {
      onDoubleClick(event, item);
    }
  }, [handleDoubleClick, doubleClickHandler, onDoubleClick, enableWorkspace, item, type]);

  // 点击处理函数
  const handleClick = useCallback((event) => {
    event.preventDefault();
    event.stopPropagation();
    
    const newClickCount = clickCount + 1;
    setClickCount(newClickCount);
    setIsHighlighted(true);

    if (newClickCount === 1) {
      // 第一次点击，等待可能的第二次点击
      setTimeout(() => {
        if (clickCount === 1) {
          // 确认是单击
          debouncedSingleClick(event);
        }
        setClickCount(0);
      }, debounceDelay);
    } else if (newClickCount === 2) {
      // 第二次点击，触发双击
      handleDoubleClickEvent(event);
    }
  }, [clickCount, debouncedSingleClick, handleDoubleClickEvent, debounceDelay]);

  // 组合样式类
  const combinedClassName = [
    className,
    'cursor-pointer transition-all duration-200',
    isInWorkspace ? activeClass : '',
    isHighlighted ? highlightClass : '',
    'hover:shadow-sm'
  ].filter(Boolean).join(' ');

  // Create a props object for the element
  const elementProps = {
    className: combinedClassName,
    onClick: handleClick,
    onDoubleClick: handleDoubleClickEvent,
    ...restProps
  };

  // Render as the specified element type (div, tr, etc.)
  const Element = as;
  
  return (
    <Element {...elementProps}>
      {children}
    </Element>
  );
}

// 导出默认的 ClickableRecord 组件
export default ClickableRecord;