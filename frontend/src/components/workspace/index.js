// 导出工作区相关组件和钩子

// 核心组件
export { default as WorkspaceProvider, useWorkspace, useWorkspaceManager } from './WorkspaceProvider';
export { default as InteractiveWorkspace } from './InteractiveWorkspace';
export { default as WorkspaceItem } from './WorkspaceItem';
export { default as ClickableRecord, withClickableRecord } from './ClickableRecord';

// 工具函数
export { workspaceStorage } from '../../utils/workspaceStorage';
export * from '../../utils/workspaceHelpers';

// 默认导出
export { default } from './WorkspaceProvider';