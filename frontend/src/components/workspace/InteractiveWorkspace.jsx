import React, { memo, useCallback, useState, useEffect } from 'react';
import { useWorkspace } from './WorkspaceProvider';
import WorkspaceItem from './WorkspaceItem';
import VirtualScrollWorkspace from './VirtualScrollWorkspace';
import { FileText, Sparkles, AlertCircle, RefreshCw } from 'lucide-react';

const InteractiveWorkspace = ({
  type, // 可选：过滤特定类型
  renderItem, // 自定义渲染函数
  emptyStateComponent: EmptyStateComponent,
  maxHeight = '600px',
  className = '',
  showHeader = true,
  showEmptyState = true,
  enableVirtualScroll = true, // 是否启用虚拟滚动
  virtualScrollThreshold = 20 // 启用虚拟滚动的阈值
}) => {
  const [performanceMode, setPerformanceMode] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const { 
    items, 
    removeItem, 
    pinItem, 
    unpinItem, 
    getItemsByType,
    getPinnedItems,
    getTemporaryItems,
    error 
  } = useWorkspace();

  // 过滤项目
  const filteredItems = useCallback(() => {
    if (type) {
      return getItemsByType(type);
    }
    return items;
  }, [items, type, getItemsByType]);

  // 获取要显示的项目
  const displayItems = filteredItems();

  // 性能监控：根据项目数量决定是否启用虚拟滚动
  useEffect(() => {
    if (enableVirtualScroll && displayItems.length > virtualScrollThreshold) {
      setPerformanceMode(true);
    } else {
      setPerformanceMode(false);
    }
  }, [displayItems.length, enableVirtualScroll, virtualScrollThreshold]);

  // 错误重试机制
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    // 这里可以添加重新加载数据的逻辑
    // 目前只是清除错误状态
    if (retryCount >= maxRetries) {
      console.warn('Max retries reached for workspace error recovery');
    }
  }, [retryCount, maxRetries]);

  // 默认渲染器
  const defaultRenderer = useCallback((item) => {
    if (renderItem) {
      return renderItem(item);
    }
    
    // 默认渲染逻辑
    return (
      <div className="space-y-2">
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="font-medium text-gray-700">类型:</span>
            <span className="ml-2 text-gray-900">{item.type}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">ID:</span>
            <span className="ml-2 text-gray-900">{item.id}</span>
          </div>
        </div>
        
        {/* 根据类型显示不同信息 */}
        {item.type === 'voucher' && item.data && (
          <div className="space-y-1 text-sm">
            <div>
              <span className="font-medium text-gray-700">凭证号:</span>
              <span className="ml-2 text-gray-900">{item.data.voucher_no || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">日期:</span>
              <span className="ml-2 text-gray-900">{item.data.date || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">摘要:</span>
              <span className="ml-2 text-gray-900">{item.data.summary || '-'}</span>
            </div>
            {item.data.total_amount && (
              <div>
                <span className="font-medium text-gray-700">金额:</span>
                <span className="ml-2 text-gray-900">
                  ¥{parseFloat(item.data.total_amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </span>
              </div>
            )}
          </div>
        )}
        
        {item.type === 'subject' && item.data && (
          <div className="space-y-1 text-sm">
            <div>
              <span className="font-medium text-gray-700">科目编码:</span>
              <span className="ml-2 text-gray-900">{item.data.科目编码 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">科目名称:</span>
              <span className="ml-2 text-gray-900">{item.data.科目名称 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">类别:</span>
              <span className="ml-2 text-gray-900">{item.data.类别 || '-'}</span>
            </div>
          </div>
        )}
        
        {item.type === 'asset' && item.data && (
          <div className="space-y-1 text-sm">
            <div>
              <span className="font-medium text-gray-700">资产编码:</span>
              <span className="ml-2 text-gray-900">{item.data.资产编码 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">资产名称:</span>
              <span className="ml-2 text-gray-900">{item.data.资产名称 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">原值:</span>
              <span className="ml-2 text-gray-900">{item.data.原值 || '-'}</span>
            </div>
          </div>
        )}
        
        {item.type === 'staff' && item.data && (
          <div className="space-y-1 text-sm">
            <div>
              <span className="font-medium text-gray-700">工号:</span>
              <span className="ml-2 text-gray-900">{item.data.工号 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">姓名:</span>
              <span className="ml-2 text-gray-900">{item.data.姓名 || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">岗位:</span>
              <span className="ml-2 text-gray-900">{item.data.岗位编码 || '-'}</span>
            </div>
          </div>
        )}
      </div>
    );
  }, [renderItem]);

  // 默认空状态组件
  const DefaultEmptyState = () => (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center opacity-60 select-none pointer-events-none">
      <div className="mb-8">
        <FileText size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
      </div>
      <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">工作区为空</h3>
      <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-lg mx-auto">
        点击或双击记录将其添加到工作区中。单击临时显示，双击固定显示。
      </p>
    </div>
  );

  const EmptyState = EmptyStateComponent || DefaultEmptyState;

  // 错误状态
  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">工作区错误</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
          
          {retryCount < maxRetries && (
            <button
              onClick={handleRetry}
              className="flex items-center space-x-1 px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors"
            >
              <RefreshCw size={14} />
              <span>重试 ({retryCount + 1}/{maxRetries})</span>
            </button>
          )}
        </div>
        
        {retryCount >= maxRetries && (
          <div className="mt-3 text-xs text-red-600">
            已达到最大重试次数，请刷新页面或联系技术支持
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {displayItems.length > 0 && (
              <span className="text-sm text-gray-500">
                共 {displayItems.length} 个项目
              </span>
            )}
          </div>
          
          {/* 统计信息 */}
          {displayItems.length > 0 && (
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                固定: {getPinnedItems().length}
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-gray-400 rounded-full mr-1"></span>
                临时: {getTemporaryItems().length}
              </span>
            </div>
          )}
        </div>
      )}
      
      {/* 工作区内容 */}
      <div className="flex-1 overflow-auto">
        {displayItems.length === 0 ? (
          showEmptyState && <EmptyState />
        ) : (
          <div className="space-y-3">
            {performanceMode ? (
              <VirtualScrollWorkspace
                items={displayItems}
                onRemove={removeItem}
                onPin={pinItem}
                onUnpin={unpinItem}
                renderer={defaultRenderer}
                maxHeight={maxHeight}
              />
            ) : (
              displayItems.map((item) => (
                <WorkspaceItem
                  key={`${item.type}_${item.id}_${item.timestamp}`}
                  item={item}
                  onRemove={removeItem}
                  onPin={pinItem}
                  onUnpin={unpinItem}
                  renderer={defaultRenderer}
                  maxHeight={maxHeight}
                />
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// 使用 memo 优化性能
export default memo(InteractiveWorkspace);