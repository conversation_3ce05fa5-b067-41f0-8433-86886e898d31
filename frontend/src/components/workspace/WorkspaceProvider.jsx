import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { workspaceStorage } from '../../utils/workspaceStorage';
import { 
  generateDisplayName, 
  isValidWorkspaceItem, 
  getItemKey, 
  sortItemsByTimestamp,
  limitItems,
  mergeItems,
  validateWorkspaceConfig
} from '../../utils/workspaceHelpers';

// 工作区操作类型
const WorkspaceActionTypes = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  PIN_ITEM: 'PIN_ITEM',
  UNPIN_ITEM: 'UNPIN_ITEM',
  CLEAR_WORKSPACE: 'CLEAR_WORKSPACE',
  LOAD_ITEMS: 'LOAD_ITEMS',
  REPLACE_TEMPORARY: 'REPLACE_TEMPORARY',
  UPDATE_ITEM: 'UPDATE_ITEM'
};

// 初始状态
const initialState = {
  items: [],
  loading: false,
  error: null,
  config: {
    maxItems: 50,
    storageKey: 'interactive-workspace',
    autoSave: true
  }
};

// 工作区 Reducer
function workspaceReducer(state, action) {
  switch (action.type) {
    case WorkspaceActionTypes.ADD_ITEM: {
      const newItem = {
        ...action.payload,
        timestamp: Date.now(),
        displayName: generateDisplayName(action.payload)
      };
      
      // 验证项目
      if (!isValidWorkspaceItem(newItem)) {
        return { ...state, error: 'Invalid workspace item' };
      }
      
      // 检查是否已存在
      const existingKey = getItemKey(newItem);
      const existingIndex = state.items.findIndex(item => getItemKey(item) === existingKey);
      
      let newItems;
      if (existingIndex >= 0) {
        // 更新现有项目
        newItems = state.items.map((item, index) => 
          index === existingIndex ? { ...item, ...newItem, timestamp: Date.now() } : item
        );
      } else {
        // 添加新项目
        newItems = [...state.items, newItem];
      }
      
      // 限制数量
      const limitedItems = limitItems(newItems, state.config.maxItems, true);
      
      return { 
        ...state, 
        items: sortItemsByTimestamp(limitedItems, 'desc'),
        error: null 
      };
    }
    
    case WorkspaceActionTypes.REMOVE_ITEM: {
      const newItems = state.items.filter(item => item.id !== action.payload.id);
      return {
        ...state,
        items: sortItemsByTimestamp(newItems, 'desc'),
        error: null
      };
    }
    
    case WorkspaceActionTypes.PIN_ITEM: {
      const newItems = state.items.map(item =>
        item.id === action.payload.id
          ? { ...item, isPinned: true, timestamp: Date.now() }
          : item
      );
      return {
        ...state,
        items: sortItemsByTimestamp(newItems, 'desc'),
        error: null
      };
    }
    
    case WorkspaceActionTypes.UNPIN_ITEM: {
      const newItems = state.items.map(item =>
        item.id === action.payload.id
          ? { ...item, isPinned: false }
          : item
      );
      return {
        ...state,
        items: sortItemsByTimestamp(newItems, 'desc'),
        error: null
      };
    }
    
    case WorkspaceActionTypes.CLEAR_WORKSPACE: {
      return { 
        ...state, 
        items: [],
        error: null 
      };
    }
    
    case WorkspaceActionTypes.LOAD_ITEMS: {
      return { 
        ...state, 
        items: sortItemsByTimestamp(action.payload, 'desc'),
        loading: false,
        error: null 
      };
    }
    
    case WorkspaceActionTypes.REPLACE_TEMPORARY: {
      // 移除所有非固定的临时项目，然后添加新项目
      const pinnedItems = state.items.filter(item => item.isPinned);
      const newItem = {
        ...action.payload,
        timestamp: Date.now(),
        displayName: generateDisplayName(action.payload)
      };
      
      if (!isValidWorkspaceItem(newItem)) {
        return { ...state, error: 'Invalid workspace item' };
      }
      
      // 检查是否已存在相同ID的项目
      const existingItemIndex = state.items.findIndex(item => item.id === newItem.id && item.type === newItem.type);
      let newItems;
      
      if (existingItemIndex >= 0) {
        // 如果已存在，更新该项目
        newItems = state.items.map((item, index) =>
          index === existingItemIndex ? { ...item, ...newItem, timestamp: Date.now() } : item
        );
      } else {
        // 如果不存在，移除所有非固定项目并添加新项目
        newItems = [...pinnedItems, newItem];
      }
      
      const limitedItems = limitItems(newItems, state.config.maxItems, true);
      
      return {
        ...state,
        items: sortItemsByTimestamp(limitedItems, 'desc'),
        error: null
      };
    }
    
    case WorkspaceActionTypes.UPDATE_ITEM: {
      const newItems = state.items.map(item =>
        item.id === action.payload.id
          ? { ...item, ...action.payload.updates, timestamp: Date.now() }
          : item
      );
      return {
        ...state,
        items: sortItemsByTimestamp(newItems, 'desc'),
        error: null
      };
    }
    
    default:
      return state;
  }
}

// 创建 Context
const WorkspaceContext = createContext();

// WorkspaceProvider 组件
export function WorkspaceProvider({ children, config = {} }) {
  const [state, dispatch] = useReducer(workspaceReducer, initialState);
  
  // 合并配置
  const mergedConfig = { ...initialState.config, ...config };
  
  // 验证配置
  const configValidation = validateWorkspaceConfig(mergedConfig);
  if (!configValidation.isValid) {
    console.warn('Workspace config validation errors:', configValidation.errors);
  }
  
  // 更新配置
  useEffect(() => {
    dispatch({ type: 'UPDATE_ITEM', payload: {
      updates: { config: mergedConfig }
    }});
  }, [JSON.stringify(mergedConfig)]);
  
  // 从本地存储加载数据
  useEffect(() => {
    const loadFromStorage = () => {
      try {
        const { items } = workspaceStorage.load();
        dispatch({ type: WorkspaceActionTypes.LOAD_ITEMS, payload: items });
      } catch (error) {
        console.error('Failed to load workspace from storage:', error);
        dispatch({ type: WorkspaceActionTypes.LOAD_ITEMS, payload: [] });
      }
    };
    
    loadFromStorage();
  }, [mergedConfig.storageKey]);
  
  // 自动保存到本地存储
  useEffect(() => {
    if (mergedConfig.autoSave) {
      const saveToStorage = () => {
        try {
          workspaceStorage.save(state.items);
        } catch (error) {
          console.error('Failed to save workspace to storage:', error);
        }
      };
      
      saveToStorage();
    }
  }, [state.items, mergedConfig.autoSave, mergedConfig.storageKey]);
  
  // 添加项目
  const addItem = useCallback((item) => {
    dispatch({ type: WorkspaceActionTypes.ADD_ITEM, payload: item });
  }, []);
  
  // 移除项目
  const removeItem = useCallback((id) => {
    dispatch({ type: WorkspaceActionTypes.REMOVE_ITEM, payload: { id } });
  }, []);
  
  // 固定项目
  const pinItem = useCallback((id) => {
    dispatch({ type: WorkspaceActionTypes.PIN_ITEM, payload: { id } });
  }, []);
  
  // 取消固定项目
  const unpinItem = useCallback((id) => {
    dispatch({ type: WorkspaceActionTypes.UNPIN_ITEM, payload: { id } });
  }, []);
  
  // 清空工作区
  const clearWorkspace = useCallback(() => {
    dispatch({ type: WorkspaceActionTypes.CLEAR_WORKSPACE });
  }, []);
  
  // 替换临时项目（单击操作）
  const replaceTemporary = useCallback((item) => {
    console.log('replaceTemporary called with:', item);
    dispatch({ type: WorkspaceActionTypes.REPLACE_TEMPORARY, payload: item });
    
    // 触发工作区显示事件
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('workspace-item-added', { detail: { item } }));
    }
  }, []);
  
  // 更新项目
  const updateItem = useCallback((id, updates) => {
    dispatch({ type: WorkspaceActionTypes.UPDATE_ITEM, payload: { id, updates } });
  }, []);
  
  // 检查项目是否在工作区中
  const isItemInWorkspace = useCallback((id) => {
    return state.items.some(item => item.id === id);
  }, [state.items]);
  
  // 获取指定类型的项目
  const getItemsByType = useCallback((type) => {
    return state.items.filter(item => item.type === type);
  }, [state.items]);
  
  // 获取固定项目
  const getPinnedItems = useCallback(() => {
    return state.items.filter(item => item.isPinned);
  }, [state.items]);
  
  // 获取临时项目
  const getTemporaryItems = useCallback(() => {
    return state.items.filter(item => !item.isPinned);
  }, [state.items]);
  
  // Context 值
  const contextValue = {
    // 状态
    items: state.items,
    loading: state.loading,
    error: state.error,
    config: mergedConfig,
    
    // 操作
    addItem,
    removeItem,
    pinItem,
    unpinItem,
    clearWorkspace,
    replaceTemporary,
    updateItem,
    
    // 查询
    isItemInWorkspace,
    getItemsByType,
    getPinnedItems,
    getTemporaryItems
  };
  
  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
}

// Hook 用于使用 Workspace Context
export function useWorkspace() {
  const context = useContext(WorkspaceContext);
  if (!context) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}

// Hook 用于使用工作区管理器
export function useWorkspaceManager() {
  const { 
    items, 
    addItem, 
    removeItem, 
    pinItem, 
    unpinItem, 
    replaceTemporary,
    isItemInWorkspace,
    getItemsByType 
  } = useWorkspace();
  
  // 处理单击事件
  const handleSingleClick = useCallback((item, type) => {
    console.log('handleSingleClick called with:', item, type);
    const workspaceItem = {
      id: item.id,
      type,
      data: item,
      isPinned: false
    };
    
    // 检查是否有固定项目
    const hasPinnedItems = items.some(i => i.isPinned);
    
    if (hasPinnedItems) {
      // 有固定项目，追加到顶部
      console.log('Adding item to workspace (has pinned items):', workspaceItem);
      addItem(workspaceItem);
    } else {
      // 没有固定项目，替换临时项目
      console.log('Replacing temporary item in workspace:', workspaceItem);
      replaceTemporary(workspaceItem);
    }
  }, [items, addItem, replaceTemporary]);
  
  // 处理双击事件
  const handleDoubleClick = useCallback((item, type) => {
    console.log('handleDoubleClick called with:', item, type);
    const workspaceItem = {
      id: item.id,
      type,
      data: item,
      isPinned: true
    };
    
    // 检查是否已存在
    if (isItemInWorkspace(item.id)) {
      // 已存在，切换固定状态
      const existingItem = items.find(i => i.id === item.id && i.type === type);
      if (existingItem) {
        if (existingItem.isPinned) {
          console.log('Unpinning item in workspace:', item.id);
          unpinItem(item.id);
        } else {
          console.log('Pinning item in workspace:', item.id);
          pinItem(item.id);
        }
      }
    } else {
      // 不存在，添加并固定
      console.log('Adding and pinning item to workspace:', workspaceItem);
      addItem(workspaceItem);
    }
  }, [items, addItem, pinItem, unpinItem, isItemInWorkspace]);
  
  // 获取工作区项目
  const getWorkspaceItems = useCallback(() => {
    return items;
  }, [items]);
  
  return {
    handleSingleClick,
    handleDoubleClick,
    isItemInWorkspace,
    getWorkspaceItems,
    getItemsByType
  };
}

// 默认导出
export default WorkspaceProvider;