import React, { memo } from 'react';
import { X, Pin, RotateCcw } from 'lucide-react';

const WorkspaceItem = ({ 
  item, 
  onRemove, 
  onPin, 
  onUnpin, 
  renderer,
  className = '',
  maxHeight = '400px'
}) => {
  const handleRemove = (e) => {
    e.stopPropagation();
    onRemove(item.id);
  };

  const handlePinToggle = (e) => {
    e.stopPropagation();
    if (item.isPinned) {
      onUnpin(item.id);
    } else {
      onPin(item.id);
    }
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 mb-3 transition-all duration-200 hover:shadow-md ${className}`}
      style={{ maxHeight }}
    >
      {/* 项目头部 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            item.isPinned 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-600'
          }`}>
            {item.isPinned ? (
              <Pin size={12} className="mr-1" />
            ) : (
              <RotateCcw size={12} className="mr-1" />
            )}
            {item.isPinned ? '已固定' : '临时'}
          </span>
          <h3 className="text-sm font-medium text-gray-900 truncate">
            {item.displayName}
          </h3>
        </div>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={handlePinToggle}
            className={`p-1.5 rounded-md transition-colors ${
              item.isPinned
                ? 'text-blue-600 hover:bg-blue-50'
                : 'text-gray-400 hover:bg-gray-100 hover:text-gray-600'
            }`}
            title={item.isPinned ? '取消固定' : '固定'}
          >
            <Pin size={14} className={item.isPinned ? 'fill-current' : ''} />
          </button>
          <button
            onClick={handleRemove}
            className="p-1.5 rounded-md text-gray-400 hover:bg-gray-100 hover:text-red-600 transition-colors"
            title="移除"
          >
            <X size={14} />
          </button>
        </div>
      </div>
      
      {/* 项目内容 */}
      <div className="p-3 overflow-auto" style={{ maxHeight: `calc(${maxHeight} - 60px)` }}>
        {renderer ? (
          renderer(item)
        ) : (
          <div className="text-sm text-gray-500">
            <p>类型: {item.type}</p>
            <p>ID: {item.id}</p>
            <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32">
              {JSON.stringify(item.data, null, 2)}
            </pre>
          </div>
        )}
      </div>
      
      {/* 项目底部 */}
      <div className="px-3 py-2 border-t border-gray-100 bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {new Date(item.timestamp).toLocaleString('zh-CN')}
          </span>
          <span className="capitalize">
            {item.type}
          </span>
        </div>
      </div>
    </div>
  );
};

// 使用 memo 优化性能
export default memo(WorkspaceItem);