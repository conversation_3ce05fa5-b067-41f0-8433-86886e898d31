import React, { useState } from 'react'
import { X, MessageCircle, Trash2, Calendar, Search, Plus } from 'lucide-react'

const HistoryDialog = ({ isOpen, onClose, sessions, onDeleteSession, onSelectSession, activeSessionId, onNewSession }) => {
  const [searchTerm, setSearchTerm] = useState('')

  if (!isOpen) return null

  const filteredSessions = sessions.filter(session => 
    session.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (session.messages && session.messages.some(msg => 
      msg.content && msg.content.toLowerCase().includes(searchTerm.toLowerCase())
    ))
  )

  const formatDate = (timestamp) => {
    if (!timestamp) return '未知时间'
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getLastMessageTime = (session) => {
    if (!session.messages || session.messages.length === 0) return null
    const lastMessage = session.messages[session.messages.length - 1]
    return lastMessage.timestamp
  }

  const getMessagePreview = (session) => {
    if (!session.messages || session.messages.length === 0) return '暂无消息'
    const lastUserMessage = session.messages.slice().reverse().find(msg => msg.type === 'USER')
    return lastUserMessage ? lastUserMessage.content.substring(0, 50) + '...' : '暂无消息'
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white">
              <MessageCircle size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">历史对话管理</h2>
              <p className="text-sm text-gray-600">管理和查看所有对话记录</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 搜索栏和新建按钮 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex space-x-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="搜索对话内容..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={() => {
                onNewSession()
                onClose()
              }}
              className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2"
            >
              <Plus size={16} />
              <span>新建</span>
            </button>
          </div>
        </div>

        {/* 对话列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3 max-h-96">
          {filteredSessions.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <MessageCircle size={48} className="mx-auto mb-4 text-gray-300" />
              <p>没有找到匹配的对话</p>
            </div>
          ) : (
            filteredSessions.map((session, index) => (
              <div
                key={session.id}
                className={`p-4 border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer ${
                  session.id === activeSessionId 
                    ? 'border-blue-300 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => {
                  onSelectSession(session.id)
                  onClose()
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <MessageCircle size={16} className={
                        session.id === activeSessionId ? 'text-blue-600' : 'text-gray-400'
                      } />
                      <h3 className="font-medium text-gray-800 truncate">
                        {session.name || `对话 ${index + 1}`}
                      </h3>
                      {session.id === activeSessionId && (
                        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full">
                          当前
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                      {getMessagePreview(session)}
                    </p>
                    <div className="flex items-center text-xs text-gray-500 space-x-4">
                      <div className="flex items-center space-x-1">
                        <Calendar size={12} />
                        <span>{formatDate(getLastMessageTime(session))}</span>
                      </div>
                      <span>{session.messages ? session.messages.length : 0} 条消息</span>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      if (window.confirm('确定要删除这个对话吗？此操作不可撤销。')) {
                        onDeleteSession(session.id)
                      }
                    }}
                    className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors ml-4"
                    title="删除对话"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 底部统计 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>共 {sessions.length} 个对话</span>
            <span>显示 {filteredSessions.length} 个结果</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HistoryDialog