import React from 'react';

const LoadingSpinner = ({ size = 'md', text = '', className = '' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      <div className="relative">
        {/* 外圈 */}
        <div className={`${sizeClasses[size]} border-2 border-gray-200 rounded-full animate-spin`}>
          <div className="absolute inset-0 border-2 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
        </div>
        
        {/* 内圈 */}
        <div className={`absolute inset-1 border border-gray-100 rounded-full`}>
          <div className="absolute inset-0 border border-transparent border-b-pink-400 border-l-cyan-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
        </div>
        
        {/* 中心点 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-1 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
        </div>
      </div>
      
      {text && (
        <div className={`${textSizes[size]} text-gray-600 font-medium animate-pulse`}>
          {text}
          <span className="loading-dots"></span>
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;