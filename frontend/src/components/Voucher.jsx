import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Plus, Minus, Calendar, Paperclip, Search, Filter, FileText, CheckCircle, Clock, XCircle, Eye, Edit, Trash2, Check, X } from 'lucide-react';
import Decimal from 'decimal.js';
import VoucherClickableRow from './voucher/VoucherClickableRow';
import { useWorkspaceManager } from './workspace';

// 专业的网格金额单元格组件
const AmountCell = ({ amount, className = "" }) => {
  // 创建12个固定宽度的格子
  const createGridCells = (value) => {
    const cells = [];

    if (!value && value !== 0) {
      // 空单元格 - 12个空格子
      for (let i = 0; i < 12; i++) {
        cells.push(
          <div
            key={i}
            className={`w-6 h-8 border-r border-gray-300 flex items-center justify-center text-xs ${i === 3 || i === 6 ? 'border-r-2 border-gray-500' :
              i === 9 ? 'border-r-2 border-gray-500' :
                i === 11 ? 'border-r-0' : ''
              }`}
          >
            &nbsp;
          </div>
        );
      }
      return cells;
    }

    // 格式化金额
    const formattedAmount = new Decimal(value).toFixed(2);
    const [integerPart, decimalPart] = formattedAmount.split('.');
    const paddedInteger = integerPart.padStart(10, '0');
    const allDigits = paddedInteger + decimalPart;

    for (let i = 0; i < 12; i++) {
      const digit = allDigits[i] || '0';
      const shouldHide = i < 10 && digit === '0' && allDigits.substring(0, i + 1).match(/^0+$/);

      cells.push(
        <div
          key={i}
          className={`w-6 h-8 border-r border-gray-300 flex items-center justify-center text-xs font-mono ${i === 3 || i === 6 ? 'border-r-2 border-gray-500' :
            i === 9 ? 'border-r-2 border-gray-500' :
              i === 11 ? 'border-r-0' : ''
            }`}
        >
          {shouldHide ? '' : digit}
        </div>
      );
    }

    return cells;
  };

  return (
    <td className={`${className} p-0`}>
      <div className="flex">
        {createGridCells(amount)}
      </div>
    </td>
  );
};

const VoucherEntry = ({ index, summary, account, amount, isDebit, balance }) => {
  return (
    <tr className="border-b border-gray-200">
      <td className="py-1 px-2 border-r border-gray-200 text-center w-12 text-sm">{index}</td>
      <td className="py-1 px-2 border-r border-gray-200 text-sm">{summary}</td>
      <td className="py-1 px-2 border-r border-gray-200 text-sm">
        <div className="flex items-center">
          <span className="flex-1">{account}</span>
          {balance && (
            <span className="text-gray-500 text-xs ml-2">
              余额: {new Decimal(balance).toFixed(2)}
            </span>
          )}
        </div>
      </td>
      <AmountCell
        amount={isDebit ? amount : null}
        className="border-r border-gray-200"
      />
      <AmountCell
        amount={!isDebit ? amount : null}
        className=""
      />
    </tr>
  );
};

const Voucher = () => {
  const [vouchers, setVouchers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPeriod, setFilterPeriod] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingVoucher, setEditingVoucher] = useState(null);
  const { handleSingleClick, handleDoubleClick } = useWorkspaceManager();

  // 凭证状态选项
  const statusOptions = [
    { value: 'draft', label: '草稿', color: 'gray' },
    { value: 'pending', label: '待审核', color: 'yellow' },
    { value: 'approved', label: '已审核', color: 'green' },
    { value: 'posted', label: '已过账', color: 'blue' },
    { value: 'cancelled', label: '已作废', color: 'red' }
  ];

  // 获取凭证记录
  const fetchVouchers = async () => {
    setLoading(true);
    try {
      // 模拟数据，实际应该从API获取
      const mockData = [
        {
          id: 1,
          voucher_no: '记2024001',
          date: '2024-01-15',
          summary: '办公用品采购',
          total_amount: 2580.00,
          status: 'posted',
          creator: '张三',
          reviewer: '李经理',
          entries: [
            { account: '管理费用-办公用品', amount: 2580.00, type: 'debit' },
            { account: '银行存款', amount: 2580.00, type: 'credit' }
          ]
        },
        {
          id: 2,
          voucher_no: '记2024002',
          date: '2024-01-18',
          summary: '销售商品收入',
          total_amount: 15000.00,
          status: 'approved',
          creator: '王五',
          reviewer: '财务总监',
          entries: [
            { account: '银行存款', amount: 15000.00, type: 'debit' },
            { account: '主营业务收入', amount: 13274.34, type: 'credit' },
            { account: '应交税费-应交增值税(销项税)', amount: 1725.66, type: 'credit' }
          ]
        },
        {
          id: 3,
          voucher_no: '记2024003',
          date: '2024-01-20',
          summary: '员工工资发放',
          total_amount: 35000.00,
          status: 'pending',
          creator: '赵六',
          reviewer: null,
          entries: [
            { account: '管理费用-工资', amount: 35000.00, type: 'debit' },
            { account: '银行存款', amount: 35000.00, type: 'credit' }
          ]
        },
        {
          id: 4,
          voucher_no: '记2024004',
          date: '2024-01-22',
          summary: '差旅费报销',
          total_amount: 3500.00,
          status: 'draft',
          creator: '钱七',
          reviewer: null,
          entries: [
            { account: '销售费用-差旅费', amount: 3500.00, type: 'debit' },
            { account: '其他应收款-员工借款', amount: 2000.00, type: 'credit' },
            { account: '银行存款', amount: 1500.00, type: 'credit' }
          ]
        },
        {
          id: 5,
          voucher_no: '记2024005',
          date: '2024-01-25',
          summary: '固定资产购置',
          total_amount: 50000.00,
          status: 'cancelled',
          creator: '孙八',
          reviewer: '财务总监',
          entries: [
            { account: '固定资产-电子设备', amount: 50000.00, type: 'debit' },
            { account: '银行存款', amount: 50000.00, type: 'credit' }
          ]
        }
      ];
      setVouchers(mockData);
    } catch (error) {
      console.error('获取凭证记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVouchers();
  }, []);

  // 过滤凭证记录
  const filteredVouchers = vouchers.filter(voucher => {
    const matchesSearch = voucher.voucher_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.summary?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voucher.creator?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || voucher.status === filterStatus;
    const matchesPeriod = !filterPeriod || voucher.date?.includes(filterPeriod);
    return matchesSearch && matchesStatus && matchesPeriod;
  });

  // 计算统计数据
  const stats = {
    totalVouchers: vouchers.length,
    draftVouchers: vouchers.filter(v => v.status === 'draft').length,
    pendingVouchers: vouchers.filter(v => v.status === 'pending').length,
    postedVouchers: vouchers.filter(v => v.status === 'posted').length
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  // 处理审核操作
  const handleApproval = async (id, action) => {
    if (!window.confirm(`确定要${action === 'approve' ? '审核通过' : '拒绝'}此凭证吗？`)) return;
    
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setVouchers(prev => prev.map(voucher =>
        voucher.id === id
          ? {
              ...voucher,
              status: action === 'approve' ? 'approved' : 'cancelled',
              reviewer: '当前用户'
            }
          : voucher
      ));
    } catch (error) {
      console.error('审核操作失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">凭证管理</h1>
        <p className="text-gray-600">管理和处理会计记账凭证</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总凭证数</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalVouchers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">待审核</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.pendingVouchers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已过账</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.postedVouchers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircle className="h-8 w-8 text-gray-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">草稿</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.draftVouchers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
            {/* 搜索框 */}
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索凭证号、摘要或制单人..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
              />
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2">
              <input
                type="month"
                value={filterPeriod}
                onChange={(e) => setFilterPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="凭证期间"
              />

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">所有状态</option>
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              新增凭证
            </button>
          </div>
        </div>
      </div>

      {/* 凭证记录列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredVouchers.length === 0 ? (
          <div className="text-center py-12">
            <FileText size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">暂无凭证记录</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <Plus size={18} />
              添加第一张凭证
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">凭证号</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">摘要</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">制单人</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核人</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredVouchers.map((voucher) => {
                  const statusInfo = getStatusInfo(voucher.status);
                  
                  return (
                    <VoucherClickableRow
                      key={voucher.id}
                      voucher={voucher}
                      className="hover:bg-gray-50"
                      onSingleClick={(event, voucher) => {
                        // 处理单击事件 - 将凭证添加到工作区
                        console.log('Single click voucher:', voucher);
                        handleSingleClick(voucher, 'voucher');
                        
                        // 确保工作区可见
                        if (typeof window !== 'undefined') {
                          window.dispatchEvent(new CustomEvent('workspace-item-added', {
                            detail: { item: voucher, type: 'voucher' }
                          }));
                        }
                      }}
                      onDoubleClick={(event, voucher) => {
                        // 处理双击事件 - 固定凭证到工作区
                        console.log('Double click voucher:', voucher);
                        handleDoubleClick(voucher, 'voucher');
                        
                        // 确保工作区可见
                        if (typeof window !== 'undefined') {
                          window.dispatchEvent(new CustomEvent('workspace-item-added', {
                            detail: { item: voucher, type: 'voucher' }
                          }));
                        }
                      }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {voucher.voucher_no}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {voucher.date}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          <div className="font-medium">{voucher.summary}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ¥{voucher.total_amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {voucher.creator}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                          statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                          statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                          statusInfo.color === 'red' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {statusInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {voucher.reviewer || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          {voucher.status === 'pending' && (
                            <>
                              <button
                                onClick={() => handleApproval(voucher.id, 'approve')}
                                className="text-green-600 hover:text-green-900"
                                title="审核通过"
                              >
                                <Check size={16} />
                              </button>
                              <button
                                onClick={() => handleApproval(voucher.id, 'reject')}
                                className="text-red-600 hover:text-red-900"
                                title="拒绝"
                              >
                                <X size={16} />
                              </button>
                            </>
                          )}
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={() => setEditingVoucher(voucher)}
                            className="text-gray-600 hover:text-gray-900"
                            title="编辑"
                          >
                            <Edit size={16} />
                          </button>
                        </div>
                      </td>
                    </VoucherClickableRow>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 凭证详情模态框 */}
      {editingVoucher && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">凭证详情 - {editingVoucher.voucher_no}</h3>
                <button
                  onClick={() => setEditingVoucher(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              {/* 凭证头部信息 */}
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">凭证号</p>
                    <p className="font-medium">{editingVoucher.voucher_no}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">日期</p>
                    <p className="font-medium">{editingVoucher.date}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">摘要</p>
                    <p className="font-medium">{editingVoucher.summary}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">状态</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      getStatusInfo(editingVoucher.status).color === 'green' ? 'bg-green-100 text-green-800' :
                      getStatusInfo(editingVoucher.status).color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      getStatusInfo(editingVoucher.status).color === 'blue' ? 'bg-blue-100 text-blue-800' :
                      getStatusInfo(editingVoucher.status).color === 'red' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {getStatusInfo(editingVoucher.status).label}
                    </span>
                  </div>
                </div>
              </div>

              {/* 凭证分录表格 */}
              <table className="w-full border-collapse mb-6">
                <thead>
                  <tr className="bg-gray-50">
                    <td className="py-2 px-2 border-b border-r border-gray-200 text-center w-12 text-sm font-bold">序号</td>
                    <td className="py-2 px-2 border-b border-r border-gray-200 text-center text-sm font-bold">摘要</td>
                    <td className="py-2 px-2 border-b border-r border-gray-200 text-center text-sm font-bold">科目</td>
                    <td className="py-1 px-0 border-b border-r border-gray-200 text-center text-sm font-bold bg-gray-50">
                      <div className="mb-1">借方金额</div>
                    </td>
                    <td className="py-1 px-0 border-b border-gray-200 text-center text-sm font-bold bg-gray-50">
                      <div className="mb-1">贷方金额</div>
                    </td>
                  </tr>
                </thead>
                <tbody>
                  {editingVoucher.entries.map((entry, idx) => (
                    <VoucherEntry
                      key={idx}
                      index={idx + 1}
                      summary={editingVoucher.summary}
                      account={entry.account || ''}
                      amount={entry.amount || 0}
                      isDebit={entry.type === 'debit'}
                    />
                  ))}
                  
                  {/* 合计行 */}
                  <tr className="bg-gray-50 font-medium border-t-2 border-gray-400">
                    <td className="py-1 px-2 border-r border-gray-200 text-center text-sm font-bold" colSpan="3">
                      合计
                    </td>
                    <AmountCell
                      amount={editingVoucher.entries.reduce((sum, e) => e.type === 'debit' ? sum + e.amount : sum, 0)}
                      className="border-r border-gray-200 bg-gray-50"
                    />
                    <AmountCell
                      amount={editingVoucher.entries.reduce((sum, e) => e.type === 'credit' ? sum + e.amount : sum, 0)}
                      className="bg-gray-50"
                    />
                  </tr>
                </tbody>
              </table>

              {/* 凭证底部信息 */}
              <div className="flex justify-between text-sm text-gray-600">
                <span>制单人：{editingVoucher.creator}</span>
                <span>审核人：{editingVoucher.reviewer || '待审核'}</span>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setEditingVoucher(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Voucher;
