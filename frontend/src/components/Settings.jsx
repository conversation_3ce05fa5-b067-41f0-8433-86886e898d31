import React, { useState } from 'react';
import { Save, TestTube, Settings as SettingsIcon, Sparkles, Database, Server } from 'lucide-react';
import RAGManagement from './settings/RAGManagement';
import MCPManagement from './settings/MCPManagement';

const SettingsPanel = ({ aiConfig, setAiConfig, backendBase }) => {
  const [config, setConfig] = useState(aiConfig || {
    api_key: '',
    base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: 'ernie-4.5-turbo-vl-preview',
    use_ollama: false,
    ollama_base_url: 'http://localhost:11434',
    ollama_model: 'llama3.1:8b'
  });
  
  const [testResult, setTestResult] = useState(null);
  const [isTesting, setIsTesting] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');

  const handleSave = () => {
    setAiConfig(config);
    localStorage.setItem('aiConfig', JSON.stringify(config));
  };

  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);
    try {
      const response = await fetch(`${backendBase}/ai/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '连接测试失败');
      }
      
      setTestResult({ success: true, message: '连接测试成功！' });
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `连接测试失败: ${error.message || '未知错误'}` 
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-200">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white shadow-lg">
              <SettingsIcon size={24} />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                AI设置中心
              </h2>
              <p className="text-sm text-gray-600 mt-1">配置您的AI服务和RAG管理</p>
            </div>
          </div>
        </div>

        {/* Tab 导航 */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <SettingsIcon size={16} />
              <span>AI服务器设置</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('rag')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'rag'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Database size={16} />
              <span>RAG管理</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('mcp')}
            className={`px-6 py-3 font-medium text-sm transition-all duration-200 border-b-2 ${
              activeTab === 'mcp'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Server size={16} />
              <span>MCP服务</span>
            </div>
          </button>
        </div>

        {/* AI服务器设置内容 */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* API Key */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                API Key
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="password"
                value={config.api_key}
                onChange={(e) => setConfig({...config, api_key: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入您的API Key"
              />
            </div>

            {/* 基础URL */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                基础URL
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.base_url}
                onChange={(e) => setConfig({...config, base_url: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入API基础URL"
              />
            </div>

            {/* 模型名称 */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-900">
                模型名称
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={config.model}
                onChange={(e) => setConfig({...config, model: e.target.value})}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入模型名称"
                disabled={config.use_ollama}
              />
            </div>

            {/* 本地模型服务切换 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-semibold text-gray-900">
                  使用本地大模型服务 (Ollama)
                </label>
                <div className="relative inline-block w-12 h-6">
                  <input
                    type="checkbox"
                    checked={config.use_ollama}
                    onChange={(e) => setConfig({...config, use_ollama: e.target.checked})}
                    className="opacity-0 w-0 h-0 peer"
                    id="ollama-toggle"
                  />
                  <label
                    htmlFor="ollama-toggle"
                    className="absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-all duration-300 peer-checked:bg-blue-600 peer-checked:before:translate-x-6 before:absolute before:h-4 before:w-4 before:left-1 before:bottom-1 before:bg-white before:rounded-full before:transition-all before:duration-300"
                  />
                </div>
              </div>
              <p className="text-sm text-gray-600">启用后使用本地运行的Ollama模型服务</p>
            </div>

            {/* Ollama配置 (仅在启用Ollama时显示) */}
            {config.use_ollama && (
              <div className="space-y-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Ollama配置</h3>
                
                {/* Ollama基础URL */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-blue-900">
                    Ollama服务地址
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.ollama_base_url}
                    onChange={(e) => setConfig({...config, ollama_base_url: e.target.value})}
                    className="w-full px-4 py-3 bg-white border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-blue-900 placeholder-blue-500 transition-all duration-200"
                    placeholder="http://localhost:11434"
                  />
                </div>

                {/* Ollama模型名称 */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-blue-900">
                    Ollama模型名称
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    value={config.ollama_model}
                    onChange={(e) => setConfig({...config, ollama_model: e.target.value})}
                    className="w-full px-4 py-3 bg-white border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-blue-900 placeholder-blue-500 transition-all duration-200"
                    placeholder="llama3.1:8b"
                  />
                </div>

                <div className="p-3 bg-blue-100 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>提示：</strong>请确保Ollama服务已启动并已下载相应的模型。可以使用命令 <code className="bg-blue-200 px-1 rounded">ollama pull {config.ollama_model}</code> 下载模型。
                  </p>
                </div>
              </div>
            )}

            {/* 测试结果 */}
            {testResult && (
              <div className={`p-4 rounded-xl border ${
                testResult.success
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <div className="flex items-center space-x-2">
                  <Sparkles size={16} className={testResult.success ? 'text-green-600' : 'text-red-600'} />
                  <div className="font-semibold">{testResult.success ? '连接成功' : '连接失败'}</div>
                </div>
                <div className="text-sm mt-1 opacity-90">{testResult.message}</div>
              </div>
            )}

            {/* 按钮组 */}
            <div className="flex space-x-3 pt-4">
              <button
                onClick={handleTest}
                disabled={isTesting}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {isTesting ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>测试中</span>
                  </div>
                ) : (
                  <>
                    <TestTube size={18} className="mr-2" />
                    测试连接
                  </>
                )}
              </button>
              
              <button
                onClick={handleSave}
                className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
              >
                <Save size={18} className="mr-2" />
                保存设置
              </button>
            </div>
          </div>
        )}

        {/* RAG管理内容 */}
        {activeTab === 'rag' && (
          <div>
            <RAGManagement
              backendBase={backendBase}
              aiConfig={config}
            />
          </div>
        )}

        {/* MCP服务管理内容 */}
        {activeTab === 'mcp' && (
          <div>
            <MCPManagement />
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsPanel;