import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Calendar } from 'lucide-react';

const CollapsibleCard = ({ title, date, children, defaultCollapsed = false }) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  return (
    <div className="glass rounded-2xl shadow-soft border border-white/20 overflow-hidden card-hover group">
      <div 
        className="flex items-center justify-between p-6 cursor-pointer bg-gradient-to-r from-white/5 to-white/10 hover:from-white/10 hover:to-white/15 transition-all duration-300"
        onClick={() => setCollapsed(!collapsed)}
      >
        <div className="flex items-center space-x-4 min-w-0 flex-1">
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-gray-800 truncate group-hover:text-gray-900 transition-colors">
              {title}
            </h3>
            {date && (
              <div className="flex items-center text-gray-500 text-sm mt-1">
                <Calendar size={14} className="mr-1.5 text-blue-500" />
                <span>{date}</span>
              </div>
            )}
          </div>
        </div>
        
        <button className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-white/20 rounded-xl transition-all duration-200 ml-4">
          <div className={`transition-transform duration-300 ${collapsed ? 'rotate-0' : 'rotate-180'}`}>
            <ChevronDown size={20} />
          </div>
        </button>
      </div>
      
      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
        collapsed ? 'max-h-0' : 'max-h-96'
      }`}>
        <div className="p-6 pt-0 bg-gradient-to-b from-white/5 to-transparent">
          <div className="bg-white/50 backdrop-blur-sm rounded-xl p-4 border border-white/30">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollapsibleCard; 