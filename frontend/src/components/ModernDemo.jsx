import React, { useState } from 'react';
import ModernButton from './ModernButton';
import ModernInput from './ModernInput';
import ModernCard from './ModernCard';
import LoadingSpinner from './LoadingSpinner';
import { 
  Sparkles, 
  Heart, 
  Star, 
  Zap, 
  Search, 
  Mail, 
  Lock,
  User,
  Settings,
  Download,
  Upload
} from 'lucide-react';

const ModernDemo = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            现代化UI组件展示
          </h1>
          <p className="text-gray-600 text-lg">
            体验最新的设计风格和交互效果
          </p>
        </div>

        {/* 按钮展示 */}
        <ModernCard title="按钮组件" icon={Sparkles}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ModernButton variant="primary" icon={Heart}>
              主要按钮
            </ModernButton>
            <ModernButton variant="secondary" icon={Star}>
              次要按钮
            </ModernButton>
            <ModernButton variant="success" icon={Zap}>
              成功按钮
            </ModernButton>
            <ModernButton variant="warning" icon={Download}>
              警告按钮
            </ModernButton>
            <ModernButton variant="ghost" icon={Upload}>
              幽灵按钮
            </ModernButton>
            <ModernButton variant="glass" icon={Settings}>
              玻璃按钮
            </ModernButton>
          </div>
          
          <div className="mt-6 space-y-4">
            <h4 className="font-semibold text-gray-700">不同尺寸</h4>
            <div className="flex flex-wrap gap-4 items-center">
              <ModernButton size="sm" variant="primary">小按钮</ModernButton>
              <ModernButton size="md" variant="primary">中按钮</ModernButton>
              <ModernButton size="lg" variant="primary">大按钮</ModernButton>
              <ModernButton size="xl" variant="primary">超大按钮</ModernButton>
            </div>
          </div>

          <div className="mt-6 space-y-4">
            <h4 className="font-semibold text-gray-700">状态展示</h4>
            <div className="flex flex-wrap gap-4">
              <ModernButton loading={loading} onClick={() => setLoading(!loading)}>
                {loading ? '加载中' : '点击加载'}
              </ModernButton>
              <ModernButton disabled>禁用按钮</ModernButton>
            </div>
          </div>
        </ModernCard>

        {/* 输入框展示 */}
        <ModernCard title="输入框组件" icon={Search}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ModernInput
                label="用户名"
                icon={User}
                placeholder="请输入用户名"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                required
              />
              <ModernInput
                label="邮箱地址"
                type="email"
                icon={Mail}
                placeholder="请输入邮箱"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                required
              />
            </div>
            
            <ModernInput
              label="密码"
              type="password"
              icon={Lock}
              placeholder="请输入密码"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
              required
            />
            
            <ModernInput
              label="搜索"
              icon={Search}
              placeholder="搜索任何内容..."
            />
            
            <ModernInput
              label="错误状态示例"
              error="这是一个错误提示信息"
              placeholder="这个输入框有错误"
            />
          </form>
        </ModernCard>

        {/* 加载动画展示 */}
        <ModernCard title="加载动画" icon={Zap}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <LoadingSpinner size="sm" text="小尺寸" />
            </div>
            <div>
              <LoadingSpinner size="md" text="中尺寸" />
            </div>
            <div>
              <LoadingSpinner size="lg" text="大尺寸" />
            </div>
            <div>
              <LoadingSpinner size="xl" text="超大尺寸" />
            </div>
          </div>
        </ModernCard>

        {/* 卡片变体展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ModernCard
            title="默认卡片"
            subtitle="玻璃态效果"
            icon={Heart}
            variant="default"
          >
            <p className="text-gray-600">
              这是一个使用玻璃态效果的默认卡片，具有半透明背景和模糊效果。
            </p>
          </ModernCard>

          <ModernCard
            title="实心卡片"
            subtitle="传统白色背景"
            icon={Star}
            variant="solid"
          >
            <p className="text-gray-600">
              这是一个传统的实心卡片，使用白色背景和边框。
            </p>
          </ModernCard>

          <ModernCard
            title="渐变卡片"
            subtitle="渐变背景效果"
            icon={Zap}
            variant="gradient"
          >
            <p className="text-gray-600">
              这是一个使用渐变背景的卡片，营造更丰富的视觉层次。
            </p>
          </ModernCard>
        </div>

        {/* 交互演示 */}
        <ModernCard title="交互效果演示" icon={Settings}>
          <div className="space-y-6">
            <div>
              <h4 className="font-semibold text-gray-700 mb-3">悬停效果</h4>
              <p className="text-gray-600 mb-4">将鼠标悬停在下面的元素上查看效果：</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div 
                    key={i}
                    className="glass p-4 rounded-xl text-center card-hover cursor-pointer"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-2"></div>
                    <p className="text-sm text-gray-600">悬停卡片 {i}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-700 mb-3">动画效果</h4>
              <p className="text-gray-600 mb-4">页面加载时的渐入动画和交互反馈。</p>
            </div>
          </div>
        </ModernCard>
      </div>
    </div>
  );
};

export default ModernDemo;