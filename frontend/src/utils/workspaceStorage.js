// 工作区本地存储工具
class WorkspaceStorage {
  constructor(storageKey = 'interactive-workspace') {
    this.storageKey = storageKey;
  }

  // 保存工作区数据到本地存储
  save(items) {
    try {
      const data = {
        items: items.map(item => ({
          ...item,
          // 确保数据可序列化
          data: this.serializeData(item.data)
        })),
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Failed to save workspace data:', error);
      return false;
    }
  }

  // 从本地存储加载工作区数据
  load() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return { items: [] };

      const data = JSON.parse(stored);
      
      // 验证数据格式
      if (!data.version || !Array.isArray(data.items)) {
        console.warn('Invalid workspace data format, returning empty workspace');
        return { items: [] };
      }

      // 反序列化数据
      const items = data.items.map(item => ({
        ...item,
        data: this.deserializeData(item.data)
      }));

      return { items };
    } catch (error) {
      console.error('Failed to load workspace data:', error);
      return { items: [] };
    }
  }

  // 清除工作区数据
  clear() {
    try {
      localStorage.removeItem(this.storageKey);
      return true;
    } catch (error) {
      console.error('Failed to clear workspace data:', error);
      return false;
    }
  }

  // 序列化数据（处理特殊对象）
  serializeData(data) {
    if (!data) return data;
    
    // 处理 Date 对象
    if (data instanceof Date) {
      return { __type: 'Date', value: data.toISOString() };
    }
    
    // 处理 Decimal 对象
    if (data.constructor && data.constructor.name === 'Decimal') {
      return { __type: 'Decimal', value: data.toString() };
    }
    
    // 处理数组和对象
    if (Array.isArray(data)) {
      return data.map(item => this.serializeData(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const result = {};
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.serializeData(value);
      }
      return result;
    }
    
    return data;
  }

  // 反序列化数据
  deserializeData(data) {
    if (!data) return data;
    
    // 处理 Date 对象
    if (data.__type === 'Date') {
      return new Date(data.value);
    }
    
    // 处理 Decimal 对象
    if (data.__type === 'Decimal') {
      // 这里需要根据实际使用的 Decimal 库来处理
      // 暂时返回字符串，在使用时再转换为 Decimal
      return data.value;
    }
    
    // 处理数组和对象
    if (Array.isArray(data)) {
      return data.map(item => this.deserializeData(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const result = {};
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.deserializeData(value);
      }
      return result;
    }
    
    return data;
  }
}

// 导出单例实例
export const workspaceStorage = new WorkspaceStorage();

// 导出类以便创建多个实例
export default WorkspaceStorage;