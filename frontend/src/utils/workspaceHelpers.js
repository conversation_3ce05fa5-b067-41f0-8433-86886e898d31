// 工作区辅助函数

/**
 * 生成工作区项目的显示名称
 * @param {Object} item - 工作区项目
 * @returns {string} 显示名称
 */
export function generateDisplayName(item) {
  if (!item || !item.data) return '未命名项目';

  switch (item.type) {
    case 'voucher':
      return `凭证: ${item.data.voucher_no || item.data.id || '未知'}`;
    case 'subject':
      return `科目: ${item.data.科目名称 || item.data.subject_name || '未知'}`;
    case 'asset':
      return `资产: ${item.data.资产名称 || item.data.asset_name || '未知'}`;
    case 'staff':
      return `员工: ${item.data.姓名 || item.data.name || '未知'}`;
    default:
      return `${item.type}: ${item.data.id || '未知'}`;
  }
}

/**
 * 检查工作区项目是否有效
 * @param {Object} item - 工作区项目
 * @returns {boolean} 是否有效
 */
export function isValidWorkspaceItem(item) {
  if (!item) return false;
  
  const required = ['id', 'type', 'data'];
  return required.every(field => item[field] !== undefined && item[field] !== null);
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 深度比较两个对象是否相等
 * @param {any} obj1 - 对象1
 * @param {any} obj2 - 对象2
 * @returns {boolean} 是否相等
 */
export function deepEqual(obj1, obj2) {
  if (obj1 === obj2) return true;
  
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || 
      obj1 === null || obj2 === null) {
    return obj1 === obj2;
  }
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }
  
  return true;
}

/**
 * 获取工作区项目的唯一键
 * @param {Object} item - 工作区项目
 * @returns {string} 唯一键
 */
export function getItemKey(item) {
  return `${item.type}_${item.id}`;
}

/**
 * 按时间戳排序工作区项目
 * @param {Array} items - 工作区项目数组
 * @param {string} order - 排序顺序 ('asc' 或 'desc')
 * @returns {Array} 排序后的数组
 */
export function sortItemsByTimestamp(items, order = 'desc') {
  return [...items].sort((a, b) => {
    const timeA = a.timestamp || 0;
    const timeB = b.timestamp || 0;
    return order === 'desc' ? timeB - timeA : timeA - timeB;
  });
}

/**
 * 过滤工作区项目
 * @param {Array} items - 工作区项目数组
 * @param {Object} filters - 过滤条件
 * @returns {Array} 过滤后的数组
 */
export function filterItems(items, filters = {}) {
  return items.filter(item => {
    if (filters.type && item.type !== filters.type) return false;
    if (filters.isPinned !== undefined && item.isPinned !== filters.isPinned) return false;
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const displayName = generateDisplayName(item).toLowerCase();
      return displayName.includes(searchTerm);
    }
    return true;
  });
}

/**
 * 限制工作区项目数量
 * @param {Array} items - 工作区项目数组
 * @param {number} maxItems - 最大数量
 * @param {boolean} keepPinned - 是否保留固定的项目
 * @returns {Array} 限制后的数组
 */
export function limitItems(items, maxItems, keepPinned = true) {
  if (items.length <= maxItems) return items;
  
  if (keepPinned) {
    const pinned = items.filter(item => item.isPinned);
    const unpinned = items.filter(item => !item.isPinned);
    const unpinnedToKeep = unpinned.slice(0, maxItems - pinned.length);
    return [...pinned, ...unpinnedToKeep];
  } else {
    return items.slice(0, maxItems);
  }
}

/**
 * 合并工作区项目
 * @param {Array} existingItems - 现有项目
 * @param {Array} newItems - 新项目
 * @returns {Array} 合并后的项目
 */
export function mergeItems(existingItems, newItems) {
  const existingKeys = new Set(existingItems.map(getItemKey));
  const uniqueNewItems = newItems.filter(item => !existingKeys.has(getItemKey(item)));
  return [...existingItems, ...uniqueNewItems];
}

/**
 * 验证工作区配置
 * @param {Object} config - 工作区配置
 * @returns {Object} 验证结果
 */
export function validateWorkspaceConfig(config) {
  const errors = [];
  
  if (!config || typeof config !== 'object') {
    errors.push('配置必须是一个对象');
    return { isValid: false, errors };
  }
  
  if (config.maxItems !== undefined && (typeof config.maxItems !== 'number' || config.maxItems < 1)) {
    errors.push('maxItems 必须是大于0的数字');
  }
  
  if (config.storageKey !== undefined && typeof config.storageKey !== 'string') {
    errors.push('storageKey 必须是字符串');
  }
  
  if (config.autoSave !== undefined && typeof config.autoSave !== 'boolean') {
    errors.push('autoSave 必须是布尔值');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}