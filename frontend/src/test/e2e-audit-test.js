/**
 * End-to-End Audit UI Test
 * 
 * This script can be run in the browser console to test the actual
 * audit UI improvements in a real environment.
 */

class E2EAuditTester {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString()
    const result = { timestamp, message, type }
    this.results.push(result)
    
    const color = type === 'pass' ? 'green' : type === 'fail' ? 'red' : 'blue'
    console.log(`%c[${timestamp}] ${message}`, `color: ${color}; font-weight: bold`)
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Test 1: Verify audit message types exist
  testMessageTypes() {
    this.log('Testing audit message types...', 'info')
    
    const tests = [
      { selector: '[class*="from-emerald-50"]', name: 'Audit Analysis' },
      { selector: '[class*="from-green-50"], [class*="from-red-50"], [class*="from-amber-50"]', name: 'Audit Result' },
      { selector: '[class*="from-blue-50"]', name: 'Audit Info Request' }
    ]

    let passed = 0
    tests.forEach(test => {
      const elements = document.querySelectorAll(test.selector)
      if (elements.length > 0) {
        this.log(`✓ ${test.name} message type found (${elements.length} instances)`, 'pass')
        passed++
      } else {
        this.log(`✗ ${test.name} message type not found`, 'fail')
      }
    })

    return passed === tests.length
  }

  // Test 2: Check for audit function button
  testAuditButton() {
    this.log('Testing audit function button...', 'info')
    
    const auditButton = document.querySelector('button:contains("单据审核"), [data-function="audit"]')
    if (auditButton || document.querySelector('*').textContent.includes('单据审核')) {
      this.log('✓ Audit function button found', 'pass')
      return true
    } else {
      this.log('✗ Audit function button not found', 'fail')
      return false
    }
  }

  // Test 3: Check for file upload functionality
  testFileUpload() {
    this.log('Testing file upload functionality...', 'info')
    
    const fileInputs = document.querySelectorAll('input[type="file"]')
    const uploadButtons = document.querySelectorAll('button:contains("上传"), [data-upload]')
    
    if (fileInputs.length > 0 || uploadButtons.length > 0) {
      this.log(`✓ File upload functionality found (${fileInputs.length} inputs, ${uploadButtons.length} buttons)`, 'pass')
      return true
    } else {
      this.log('✗ File upload functionality not found', 'fail')
      return false
    }
  }

  // Test 4: Check for streaming indicators
  testStreamingIndicators() {
    this.log('Testing streaming indicators...', 'info')
    
    const loadingElements = document.querySelectorAll(
      '[class*="animate-pulse"], [class*="animate-spin"], [class*="loading"]'
    )
    
    if (loadingElements.length > 0) {
      this.log(`✓ Streaming indicators found (${loadingElements.length} elements)`, 'pass')
      return true
    } else {
      this.log('ℹ No streaming indicators currently visible (this is normal)', 'info')
      return true
    }
  }

  // Test 5: Check for error handling elements
  testErrorHandling() {
    this.log('Testing error handling elements...', 'info')
    
    const errorElements = document.querySelectorAll(
      '[class*="text-red"], [class*="bg-red"], button:contains("重试")'
    )
    
    if (errorElements.length > 0) {
      this.log(`ℹ Error handling elements found (${errorElements.length} elements)`, 'info')
    } else {
      this.log('ℹ No error elements currently visible (this is normal)', 'info')
    }
    
    return true
  }

  // Test 6: Performance check
  testPerformance() {
    this.log('Testing performance...', 'info')
    
    if (performance.memory) {
      const memory = performance.memory
      const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2)
      const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2)
      
      this.log(`Memory usage: ${usedMB}MB / ${totalMB}MB`, 'info')
      
      if (memory.usedJSHeapSize / memory.jsHeapSizeLimit < 0.8) {
        this.log('✓ Memory usage within acceptable range', 'pass')
        return true
      } else {
        this.log('⚠ High memory usage detected', 'fail')
        return false
      }
    } else {
      this.log('ℹ Memory monitoring not available', 'info')
      return true
    }
  }

  // Test 7: UI responsiveness
  async testResponsiveness() {
    this.log('Testing UI responsiveness...', 'info')
    
    const buttons = document.querySelectorAll('button:not([disabled])')
    const inputs = document.querySelectorAll('input, textarea')
    
    let responsiveElements = 0
    
    // Test button responsiveness
    buttons.forEach(button => {
      const startTime = performance.now()
      button.addEventListener('mouseenter', () => {
        const responseTime = performance.now() - startTime
        if (responseTime < 100) {
          responsiveElements++
        }
      }, { once: true })
      
      // Simulate hover
      button.dispatchEvent(new MouseEvent('mouseenter'))
    })
    
    await this.sleep(100)
    
    if (responsiveElements > 0 || buttons.length === 0) {
      this.log(`✓ UI elements responsive (${responsiveElements}/${buttons.length} buttons)`, 'pass')
      return true
    } else {
      this.log('⚠ Some UI elements may be slow to respond', 'fail')
      return false
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('Starting E2E Audit UI Tests...', 'info')
    
    const tests = [
      { name: 'Message Types', test: () => this.testMessageTypes() },
      { name: 'Audit Button', test: () => this.testAuditButton() },
      { name: 'File Upload', test: () => this.testFileUpload() },
      { name: 'Streaming Indicators', test: () => this.testStreamingIndicators() },
      { name: 'Error Handling', test: () => this.testErrorHandling() },
      { name: 'Performance', test: () => this.testPerformance() },
      { name: 'Responsiveness', test: () => this.testResponsiveness() }
    ]

    let passed = 0
    let total = tests.length

    for (const { name, test } of tests) {
      try {
        const result = await test()
        if (result) {
          passed++
        }
      } catch (error) {
        this.log(`✗ ${name} test failed: ${error.message}`, 'fail')
      }
    }

    const duration = Date.now() - this.startTime
    this.log(`Tests completed in ${duration}ms`, 'info')
    this.log(`Results: ${passed}/${total} tests passed`, passed === total ? 'pass' : 'fail')

    return {
      passed,
      total,
      duration,
      results: this.results
    }
  }

  // Generate report
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      results: this.results,
      summary: {
        total: this.results.length,
        passed: this.results.filter(r => r.type === 'pass').length,
        failed: this.results.filter(r => r.type === 'fail').length,
        info: this.results.filter(r => r.type === 'info').length
      }
    }

    console.log('=== E2E AUDIT UI TEST REPORT ===')
    console.log(`URL: ${report.url}`)
    console.log(`Browser: ${report.userAgent}`)
    console.log(`Timestamp: ${report.timestamp}`)
    console.log(`Total Results: ${report.summary.total}`)
    console.log(`✓ Passed: ${report.summary.passed}`)
    console.log(`✗ Failed: ${report.summary.failed}`)
    console.log(`ℹ Info: ${report.summary.info}`)
    console.log('================================')

    return report
  }
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  window.E2EAuditTester = E2EAuditTester
  console.log('E2E Audit Tester loaded. Run: new E2EAuditTester().runAllTests()')
}

export default E2EAuditTester