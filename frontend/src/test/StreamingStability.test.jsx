import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import Agent from '../components/Agent'

// Mock the API
vi.mock('../api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn()
  },
  backendBase: 'http://localhost:8000'
}))

// Mock ReactMarkdown
vi.mock('react-markdown', () => ({
  default: ({ children }) => <div data-testid="markdown">{children}</div>
}))

describe('Streaming Response Stability Tests', () => {
  const mockProps = {
    setVouchers: vi.fn(),
    setSubjects: vi.fn(),
    setAssets: vi.fn(),
    setStaffs: vi.fn(),
    aiConfig: {
      api_key: 'test-key',
      base_url: 'http://localhost:8000',
      model: 'gpt-3.5-turbo',
      use_ollama: false
    },
    inSidebar: false,
    session: null,
    updateSession: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('Connection stability', () => {
    it('should handle connection interruptions gracefully', async () => {
      // Mock connection interruption
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 开始分析...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 分析进行中...\n\n') 
          })
          .mockRejectedValueOnce(new Error('Connection lost'))
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试连接中断' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should show initial content
      await waitFor(() => {
        expect(screen.getByText(/开始分析/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/分析进行中/)).toBeInTheDocument()
      })

      // Should handle connection error gracefully
      await waitFor(() => {
        expect(screen.getByText(/发生错误/)).toBeInTheDocument()
      })
    })

    it('should handle slow streaming responses', async () => {
      // Mock slow streaming
      const mockReader = {
        read: vi.fn()
          .mockImplementation(() => 
            new Promise(resolve => {
              setTimeout(() => {
                resolve({ 
                  done: false, 
                  value: new TextEncoder().encode('data: 慢速分析内容...\n\n') 
                })
              }, 2000) // 2 second delay
            })
          )
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试慢速响应' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should show loading state
      await waitFor(() => {
        expect(screen.getByText(/思考中/)).toBeInTheDocument()
      })

      // Should eventually show content (with longer timeout)
      await waitFor(() => {
        expect(screen.getByText(/慢速分析内容/)).toBeInTheDocument()
      }, { timeout: 5000 })
    })

    it('should handle empty streaming chunks', async () => {
      // Mock streaming with empty chunks
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正常内容\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('') // Empty chunk
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: \n\n') // Empty data
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 更多内容\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试空块' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should handle empty chunks gracefully and show valid content
      await waitFor(() => {
        expect(screen.getByText(/正常内容/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/更多内容/)).toBeInTheDocument()
      })
    })
  })

  describe('Data parsing stability', () => {
    it('should handle malformed SSE data', async () => {
      // Mock malformed SSE data
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正常数据\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('malformed: 错误格式\n') // Missing \n\n
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 恢复正常\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试格式错误' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should show valid data
      await waitFor(() => {
        expect(screen.getByText(/正常数据/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/恢复正常/)).toBeInTheDocument()
      })

      // Should not crash or show error for malformed data
      expect(screen.queryByText(/发生错误/)).not.toBeInTheDocument()
    })

    it('should handle mixed content types in stream', async () => {
      // Mock stream with mixed content
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 普通文本内容\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: **Markdown** 内容\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <AUDIT_RESULT>{"action":"audit_complete","audit_conclusion":"结构化结果"}</AUDIT_RESULT>\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试混合内容' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should show analysis content
      await waitFor(() => {
        expect(screen.getByText(/普通文本内容/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/Markdown.*内容/)).toBeInTheDocument()
      })

      // Should parse and show structured result
      await waitFor(() => {
        expect(screen.getByText('✅ 审核通过')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/结构化结果/)).toBeInTheDocument()
      })
    })

    it('should handle Unicode and special characters in stream', async () => {
      // Mock stream with Unicode content
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 中文内容 🎉\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: Émojis: 💰📊📈\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: Special chars: @#$%^&*()\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试特殊字符' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should handle Unicode correctly
      await waitFor(() => {
        expect(screen.getByText(/中文内容 🎉/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/Émojis: 💰📊📈/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/Special chars: @#\$%\^&\*\(\)/)).toBeInTheDocument()
      })
    })
  })

  describe('Memory and performance stability', () => {
    it('should handle large streaming responses without memory leaks', async () => {
      // Mock large streaming response
      const mockReader = {
        read: vi.fn()
      }

      // Generate large content
      const largeContent = 'A'.repeat(10000) // 10KB of content
      for (let i = 0; i < 100; i++) {
        mockReader.read.mockResolvedValueOnce({ 
          done: false, 
          value: new TextEncoder().encode(`data: ${largeContent} chunk ${i}\n\n`) 
        })
      }
      mockReader.read.mockResolvedValueOnce({ done: true, value: undefined })

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试大量数据' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should handle large content
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Should eventually complete without crashing
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled()
      }, { timeout: 10000 })
    })

    it('should clean up resources when component unmounts during streaming', async () => {
      // Mock ongoing streaming
      const mockReader = {
        read: vi.fn().mockImplementation(() => 
          new Promise(resolve => {
            setTimeout(() => {
              resolve({ 
                done: false, 
                value: new TextEncoder().encode('data: 持续流式内容...\n\n') 
              })
            }, 100)
          })
        )
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      const { unmount } = render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试组件卸载' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Wait for streaming to start
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Unmount component during streaming
      unmount()

      // Should not cause any errors or memory leaks
      // This test mainly ensures no console errors occur
    })
  })

  describe('Concurrent streaming stability', () => {
    it('should handle multiple concurrent audit requests properly', async () => {
      let requestCount = 0
      global.fetch.mockImplementation(() => {
        requestCount++
        const currentRequest = requestCount
        
        return Promise.resolve({
          ok: true,
          body: {
            getReader: () => ({
              read: vi.fn()
                .mockResolvedValueOnce({ 
                  done: false, 
                  value: new TextEncoder().encode(`data: 请求 ${currentRequest} 处理中...\n\n`) 
                })
                .mockResolvedValueOnce({ done: true, value: undefined })
            })
          }
        })
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })

      // Send first request
      fireEvent.change(input, { target: { value: '第一个请求' } })
      fireEvent.click(sendButton)

      // Quickly send second request
      fireEvent.change(input, { target: { value: '第二个请求' } })
      fireEvent.click(sendButton)

      // Should handle concurrent requests appropriately
      await waitFor(() => {
        expect(requestCount).toBeGreaterThanOrEqual(1)
      })

      // Should show some response
      await waitFor(() => {
        expect(screen.getByText(/请求.*处理中/)).toBeInTheDocument()
      })
    })
  })
})