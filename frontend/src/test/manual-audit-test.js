/**
 * Manual Testing Script for Audit UI Improvements
 * 
 * This script provides a comprehensive manual testing checklist
 * for verifying the audit UI improvements work correctly.
 * 
 * Run this in the browser console while using the application
 * to perform automated checks alongside manual testing.
 */

class AuditUITester {
  constructor() {
    this.testResults = []
    this.startTime = Date.now()
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = { timestamp, message, type }
    this.testResults.push(logEntry)
    
    const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'
    console.log(`%c[${timestamp}] ${message}`, `color: ${color}`)
  }

  // Test 1: Verify audit message types are rendered correctly
  testAuditMessageTypes() {
    this.log('Testing audit message types...', 'info')
    
    const auditAnalysis = document.querySelector('[class*="from-emerald-50"]')
    const auditResult = document.querySelector('[class*="from-green-50"], [class*="from-red-50"], [class*="from-amber-50"]')
    const auditInfoRequest = document.querySelector('[class*="from-blue-50"]')
    
    if (auditAnalysis) {
      this.log('✓ Audit analysis message type found', 'success')
    } else {
      this.log('✗ Audit analysis message type not found', 'error')
    }
    
    if (auditResult) {
      this.log('✓ Audit result message type found', 'success')
    } else {
      this.log('✗ Audit result message type not found', 'error')
    }
    
    if (auditInfoRequest) {
      this.log('✓ Audit info request message type found', 'success')
    } else {
      this.log('✗ Audit info request message type not found', 'error')
    }
  }

  // Test 2: Check streaming functionality
  testStreamingFunctionality() {
    this.log('Testing streaming functionality...', 'info')
    
    // Check if messages appear incrementally
    const messages = document.querySelectorAll('[class*="message"]')
    if (messages.length > 0) {
      this.log(`✓ Found ${messages.length} messages`, 'success')
    } else {
      this.log('✗ No messages found', 'error')
    }
    
    // Check for loading indicators
    const loadingIndicators = document.querySelectorAll('[class*="animate-pulse"], [class*="animate-spin"]')
    if (loadingIndicators.length > 0) {
      this.log(`✓ Found ${loadingIndicators.length} loading indicators`, 'success')
    } else {
      this.log('ℹ No loading indicators currently visible', 'info')
    }
  }

  // Test 3: Verify response time
  testResponseTime() {
    this.log('Testing response time...', 'info')
    
    const sendButton = document.querySelector('button[type="submit"], button:contains("发送")')
    if (sendButton) {
      const startTime = Date.now()
      
      // Monitor for first response
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.addedNodes.length > 0) {
            const responseTime = Date.now() - startTime
            if (responseTime < 1000) {
              this.log(`✓ Response time: ${responseTime}ms (< 1s)`, 'success')
            } else {
              this.log(`⚠ Response time: ${responseTime}ms (> 1s)`, 'error')
            }
            observer.disconnect()
          }
        })
      })
      
      observer.observe(document.body, { childList: true, subtree: true })
      
      // Auto-disconnect after 5 seconds
      setTimeout(() => observer.disconnect(), 5000)
    }
  }

  // Test 4: Check error handling
  testErrorHandling() {
    this.log('Testing error handling...', 'info')
    
    const errorMessages = document.querySelectorAll('[class*="from-red-50"], [class*="text-red"]')
    if (errorMessages.length > 0) {
      this.log(`ℹ Found ${errorMessages.length} error messages`, 'info')
      
      // Check for retry buttons
      const retryButtons = document.querySelectorAll('button:contains("重试"), [title*="重试"]')
      if (retryButtons.length > 0) {
        this.log('✓ Retry buttons available', 'success')
      } else {
        this.log('ℹ No retry buttons found', 'info')
      }
    } else {
      this.log('ℹ No error messages currently visible', 'info')
    }
  }

  // Test 5: Verify structured info requests
  testStructuredInfoRequests() {
    this.log('Testing structured info requests...', 'info')
    
    const infoTables = document.querySelectorAll('table')
    const infoLists = document.querySelectorAll('ol, ul')
    
    if (infoTables.length > 0) {
      this.log(`✓ Found ${infoTables.length} structured tables`, 'success')
    }
    
    if (infoLists.length > 0) {
      this.log(`✓ Found ${infoLists.length} structured lists`, 'success')
    }
    
    if (infoTables.length === 0 && infoLists.length === 0) {
      this.log('ℹ No structured info requests currently visible', 'info')
    }
  }

  // Test 6: Check UI responsiveness
  testUIResponsiveness() {
    this.log('Testing UI responsiveness...', 'info')
    
    const startTime = performance.now()
    
    // Simulate user interactions
    const buttons = document.querySelectorAll('button')
    const inputs = document.querySelectorAll('input, textarea')
    
    let interactionCount = 0
    
    buttons.forEach(button => {
      if (!button.disabled) {
        button.addEventListener('click', () => {
          interactionCount++
          const responseTime = performance.now() - startTime
          if (responseTime < 100) {
            this.log(`✓ Button response time: ${responseTime.toFixed(2)}ms`, 'success')
          } else {
            this.log(`⚠ Slow button response: ${responseTime.toFixed(2)}ms`, 'error')
          }
        }, { once: true })
      }
    })
    
    inputs.forEach(input => {
      input.addEventListener('input', () => {
        interactionCount++
        const responseTime = performance.now() - startTime
        if (responseTime < 50) {
          this.log(`✓ Input response time: ${responseTime.toFixed(2)}ms`, 'success')
        } else {
          this.log(`⚠ Slow input response: ${responseTime.toFixed(2)}ms`, 'error')
        }
      }, { once: true })
    })
    
    this.log(`ℹ Monitoring ${buttons.length} buttons and ${inputs.length} inputs`, 'info')
  }

  // Test 7: Memory usage monitoring
  testMemoryUsage() {
    this.log('Testing memory usage...', 'info')
    
    if (performance.memory) {
      const memory = performance.memory
      const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2)
      const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2)
      const limitMB = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)
      
      this.log(`Memory usage: ${usedMB}MB / ${totalMB}MB (limit: ${limitMB}MB)`, 'info')
      
      if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {
        this.log('⚠ High memory usage detected', 'error')
      } else {
        this.log('✓ Memory usage within normal range', 'success')
      }
    } else {
      this.log('ℹ Memory monitoring not available in this browser', 'info')
    }
  }

  // Run all tests
  runAllTests() {
    this.log('Starting comprehensive audit UI tests...', 'info')
    
    this.testAuditMessageTypes()
    this.testStreamingFunctionality()
    this.testResponseTime()
    this.testErrorHandling()
    this.testStructuredInfoRequests()
    this.testUIResponsiveness()
    this.testMemoryUsage()
    
    const totalTime = Date.now() - this.startTime
    this.log(`All tests completed in ${totalTime}ms`, 'info')
    
    return this.generateReport()
  }

  // Generate test report
  generateReport() {
    const successCount = this.testResults.filter(r => r.type === 'success').length
    const errorCount = this.testResults.filter(r => r.type === 'error').length
    const totalCount = this.testResults.length
    
    const report = {
      summary: {
        total: totalCount,
        success: successCount,
        errors: errorCount,
        warnings: totalCount - successCount - errorCount
      },
      results: this.testResults,
      timestamp: new Date().toISOString()
    }
    
    console.log('=== AUDIT UI TEST REPORT ===')
    console.log(`Total tests: ${totalCount}`)
    console.log(`✓ Passed: ${successCount}`)
    console.log(`✗ Failed: ${errorCount}`)
    console.log(`⚠ Warnings: ${report.summary.warnings}`)
    console.log('============================')
    
    return report
  }
}

// Manual testing checklist
const MANUAL_TEST_CHECKLIST = [
  {
    id: 'MT001',
    title: '文件上传测试',
    steps: [
      '1. 点击"单据审核"功能',
      '2. 上传一个PDF或图片文件',
      '3. 验证文件消息正确显示',
      '4. 验证文件预览功能正常'
    ],
    expected: '文件上传成功，显示文件名和预览'
  },
  {
    id: 'MT002',
    title: '流式审核分析测试',
    steps: [
      '1. 上传文件后发送审核请求',
      '2. 观察是否立即显示"审核分析"消息',
      '3. 验证分析内容是否实时更新',
      '4. 检查绿色渐变背景和动画效果'
    ],
    expected: '立即显示分析消息，内容实时流式更新'
  },
  {
    id: 'MT003',
    title: '审核通过结果测试',
    steps: [
      '1. 提交一个完整的单据进行审核',
      '2. 等待审核完成',
      '3. 验证显示绿色"✅ 审核通过"消息',
      '4. 检查通过原因是否清晰显示'
    ],
    expected: '显示绿色审核通过消息，包含详细原因'
  },
  {
    id: 'MT004',
    title: '审核不通过结果测试',
    steps: [
      '1. 提交一个有问题的单据',
      '2. 等待审核完成',
      '3. 验证显示红色"❌ 审核不通过"消息',
      '4. 检查拒绝原因是否详细说明'
    ],
    expected: '显示红色审核不通过消息，包含具体问题'
  },
  {
    id: 'MT005',
    title: '信息补充请求测试',
    steps: [
      '1. 提交一个信息不完整的单据',
      '2. 等待审核完成',
      '3. 验证显示蓝色"📋 需要补充信息"消息',
      '4. 检查信息列表是否结构化显示',
      '5. 验证序号和具体要求是否清晰'
    ],
    expected: '显示蓝色信息请求消息，包含结构化列表'
  },
  {
    id: 'MT006',
    title: '错误处理测试',
    steps: [
      '1. 断开网络连接',
      '2. 尝试发送审核请求',
      '3. 验证错误消息显示',
      '4. 检查重试按钮是否可用',
      '5. 恢复网络后测试重试功能'
    ],
    expected: '显示具体错误信息和重试选项'
  },
  {
    id: 'MT007',
    title: '响应时间测试',
    steps: [
      '1. 发送审核请求',
      '2. 记录从点击到首次响应的时间',
      '3. 验证是否在1秒内显示反馈',
      '4. 检查流式内容是否立即开始显示'
    ],
    expected: '1秒内显示初始反馈，流式内容立即开始'
  },
  {
    id: 'MT008',
    title: '并发请求测试',
    steps: [
      '1. 快速连续发送多个审核请求',
      '2. 观察系统如何处理并发请求',
      '3. 验证UI是否保持响应',
      '4. 检查是否有内存泄漏或性能问题'
    ],
    expected: '正确处理并发请求，UI保持响应'
  }
]

// Export for use
window.AuditUITester = AuditUITester
window.MANUAL_TEST_CHECKLIST = MANUAL_TEST_CHECKLIST

// Auto-run basic tests if in development mode
if (window.location.hostname === 'localhost') {
  console.log('Audit UI Testing Tools Loaded')
  console.log('Run: new AuditUITester().runAllTests() to start automated tests')
  console.log('Manual test checklist available in MANUAL_TEST_CHECKLIST')
}