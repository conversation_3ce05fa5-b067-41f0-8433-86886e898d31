import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import Agent from '../components/Agent'

// Mock the API
vi.mock('../api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn()
  },
  backendBase: 'http://localhost:8000'
}))

// Mock ReactMarkdown
vi.mock('react-markdown', () => ({
  default: ({ children }) => <div data-testid="markdown">{children}</div>
}))

// Mock other components
vi.mock('../components/SubjectCard', () => ({
  default: ({ children }) => <div data-testid="subject-card">{children}</div>
}))
vi.mock('../components/AssetCard', () => ({
  default: ({ children }) => <div data-testid="asset-card">{children}</div>
}))
vi.mock('../components/StaffCard', () => ({
  default: ({ children }) => <div data-testid="staff-card">{children}</div>
}))
vi.mock('../components/VoucherCard', () => ({
  default: ({ children }) => <div data-testid="voucher-card">{children}</div>
}))

describe('Agent Component - Streaming Functionality', () => {
  const mockProps = {
    setVouchers: vi.fn(),
    setSubjects: vi.fn(),
    setAssets: vi.fn(),
    setStaffs: vi.fn(),
    aiConfig: {
      api_key: 'test-key',
      base_url: 'http://localhost:8000',
      model: 'gpt-3.5-turbo',
      use_ollama: false
    },
    inSidebar: false,
    session: null,
    updateSession: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset fetch mock
    global.fetch = vi.fn()
  })

  describe('Streaming audit analysis', () => {
    it('should start streaming immediately when audit request is sent', async () => {
      // Mock streaming response
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ done: false, value: new TextEncoder().encode('data: 正在分析单据\n\n') })
          .mockResolvedValueOnce({ done: false, value: new TextEncoder().encode('data: 检查金额信息\n\n') })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      // Find and click audit button (assuming it exists)
      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      // Upload a file first
      const fileInput = screen.getByRole('button', { name: /上传文件/i })
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      
      // Mock file upload
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      })
      
      fireEvent.change(fileInput)

      // Send audit request
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Wait for streaming to start
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Verify streaming content appears
      await waitFor(() => {
        expect(screen.getByText(/正在分析单据/)).toBeInTheDocument()
      })
    })

    it('should handle streaming errors gracefully', async () => {
      // Mock streaming error
      global.fetch.mockRejectedValue(new Error('Network error'))

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      // Try to send a message
      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试审核' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/发生错误/)).toBeInTheDocument()
      })
    })

    it('should parse structured markers in streaming response', async () => {
      // Mock streaming response with structured markers
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正在分析单据内容...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <AUDIT_RESULT>{"action":"audit_complete","audit_conclusion":"审核通过"}</AUDIT_RESULT>\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      // Send audit request
      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '审核这个单据' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Wait for analysis message
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Wait for result message
      await waitFor(() => {
        expect(screen.getByText('✅ 审核通过')).toBeInTheDocument()
      })
    })
  })

  describe('Response time optimization', () => {
    it('should show thinking state within 1 second', async () => {
      // Mock delayed response
      global.fetch.mockImplementation(() => 
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              ok: true,
              body: {
                getReader: () => ({
                  read: vi.fn().mockResolvedValue({ done: true, value: undefined })
                })
              }
            })
          }, 500) // 500ms delay
        })
      )

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试消息' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      const startTime = Date.now()
      fireEvent.click(sendButton)

      // Should show thinking state quickly
      await waitFor(() => {
        expect(screen.getByText(/思考中/)).toBeInTheDocument()
        const elapsedTime = Date.now() - startTime
        expect(elapsedTime).toBeLessThan(1000) // Within 1 second
      })
    })

    it('should start displaying content immediately when streaming begins', async () => {
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 第一段内容\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 第二段内容\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '开始审核' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Should show first chunk immediately
      await waitFor(() => {
        expect(screen.getByText(/第一段内容/)).toBeInTheDocument()
      })

      // Should show second chunk as it arrives
      await waitFor(() => {
        expect(screen.getByText(/第二段内容/)).toBeInTheDocument()
      })
    })
  })

  describe('Audit state management', () => {
    it('should manage loading states correctly during audit process', async () => {
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 分析中...\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '审核请求' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      // Initially not loading
      expect(sendButton).not.toBeDisabled()
      
      fireEvent.click(sendButton)

      // Should be loading during request
      await waitFor(() => {
        expect(sendButton).toBeDisabled()
      })

      // Should finish loading when stream ends
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled()
      })
    })

    it('should clear loading state when audit is cancelled', async () => {
      // Mock a long-running stream
      const mockReader = {
        read: vi.fn().mockImplementation(() => 
          new Promise(resolve => {
            setTimeout(() => {
              resolve({ done: false, value: new TextEncoder().encode('data: 长时间分析...\n\n') })
            }, 1000)
          })
        )
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '长时间审核' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      // Wait for loading state
      await waitFor(() => {
        expect(sendButton).toBeDisabled()
      })

      // Find and click stop button
      const stopButton = screen.getByRole('button', { name: /停止/i })
      fireEvent.click(stopButton)

      // Should clear loading state
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled()
      })
    })
  })

  describe('Error handling and user feedback', () => {
    it('should display specific error messages for different error types', async () => {
      // Mock network error
      global.fetch.mockRejectedValue(new Error('Failed to fetch'))

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试错误' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      await waitFor(() => {
        expect(screen.getByText(/发生错误/)).toBeInTheDocument()
        expect(screen.getByText(/Failed to fetch/)).toBeInTheDocument()
      })
    })

    it('should provide retry option for failed requests', async () => {
      global.fetch.mockRejectedValue(new Error('Network error'))

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      fireEvent.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      fireEvent.change(input, { target: { value: '测试重试' } })
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      fireEvent.click(sendButton)

      await waitFor(() => {
        expect(screen.getByText(/发生错误/)).toBeInTheDocument()
      })

      // Look for retry button
      const retryButton = screen.getByRole('button', { name: /重试/i })
      expect(retryButton).toBeInTheDocument()

      // Test retry functionality
      global.fetch.mockResolvedValueOnce({
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn().mockResolvedValue({ done: true, value: undefined })
          })
        }
      })

      fireEvent.click(retryButton)

      await waitFor(() => {
        expect(screen.queryByText(/发生错误/)).not.toBeInTheDocument()
      })
    })
  })
})