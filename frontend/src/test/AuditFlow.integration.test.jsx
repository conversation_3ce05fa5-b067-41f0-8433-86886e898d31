import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import Agent from '../components/Agent'

// Mock the API
vi.mock('../api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn()
  },
  backendBase: 'http://localhost:8000'
}))

// Mock ReactMarkdown
vi.mock('react-markdown', () => ({
  default: ({ children }) => <div data-testid="markdown">{children}</div>
}))

// Mock other components
vi.mock('../components/SubjectCard', () => ({
  default: ({ children }) => <div data-testid="subject-card">{children}</div>
}))
vi.mock('../components/AssetCard', () => ({
  default: ({ children }) => <div data-testid="asset-card">{children}</div>
}))
vi.mock('../components/StaffCard', () => ({
  default: ({ children }) => <div data-testid="staff-card">{children}</div>
}))
vi.mock('../components/VoucherCard', () => ({
  default: ({ children }) => <div data-testid="voucher-card">{children}</div>
}))

describe('Complete Audit Flow Integration Tests', () => {
  const mockProps = {
    setVouchers: vi.fn(),
    setSubjects: vi.fn(),
    setAssets: vi.fn(),
    setStaffs: vi.fn(),
    aiConfig: {
      api_key: 'test-key',
      base_url: 'http://localhost:8000',
      model: 'gpt-3.5-turbo',
      use_ollama: false
    },
    inSidebar: false,
    session: null,
    updateSession: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('Successful audit flow', () => {
    it('should complete full audit flow from file upload to approval', async () => {
      const user = userEvent.setup()

      // Mock successful streaming response
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 开始分析上传的单据...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 检查单据格式和内容完整性...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 验证金额和日期信息...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <AUDIT_RESULT>{"action":"audit_complete","audit_conclusion":"单据审核通过，所有信息完整准确，符合财务规范。"}</AUDIT_RESULT>\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      // Step 1: Select audit function
      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      // Step 2: Upload file
      const fileInput = screen.getByLabelText(/上传文件/i) || screen.getByRole('button', { name: /上传/i })
      const file = new File(['test invoice content'], 'invoice.pdf', { type: 'application/pdf' })
      
      await user.upload(fileInput, file)

      // Verify file upload message appears
      await waitFor(() => {
        expect(screen.getByText('invoice.pdf')).toBeInTheDocument()
      })

      // Step 3: Send audit request
      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '请审核这个发票')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // Step 4: Verify streaming analysis appears
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/开始分析上传的单据/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/检查单据格式和内容完整性/)).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/验证金额和日期信息/)).toBeInTheDocument()
      })

      // Step 5: Verify final audit result
      await waitFor(() => {
        expect(screen.getByText('✅ 审核通过')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/单据审核通过，所有信息完整准确，符合财务规范/)).toBeInTheDocument()
      })

      // Verify the complete flow worked
      expect(mockReader.read).toHaveBeenCalledTimes(5)
    })

    it('should handle audit rejection flow correctly', async () => {
      const user = userEvent.setup()

      // Mock rejection streaming response
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正在分析单据内容...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 发现问题：金额信息不清晰\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <AUDIT_RESULT>{"action":"audit_rejected","audit_conclusion":"单据审核不通过：发票金额模糊不清，无法准确识别。"}</AUDIT_RESULT>\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      // Upload and audit
      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '审核这个模糊的发票')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // Verify analysis
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/发现问题：金额信息不清晰/)).toBeInTheDocument()
      })

      // Verify rejection result
      await waitFor(() => {
        expect(screen.getByText('❌ 审核不通过')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText(/发票金额模糊不清，无法准确识别/)).toBeInTheDocument()
      })
    })

    it('should handle info request flow with structured requirements', async () => {
      const user = userEvent.setup()

      // Mock info request streaming response
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正在分析单据...\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 需要补充更多信息才能完成审核\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <AUDIT_INFO_REQUEST>{"action":"request_more_info","audit_conclusion":"需要补充以下信息：","required_info":["请提供发票号码","请确认交易日期","请上传付款凭证"]}</AUDIT_INFO_REQUEST>\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      // Upload and audit
      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '审核这个不完整的单据')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // Verify analysis
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Verify info request
      await waitFor(() => {
        expect(screen.getByText('📋 需要补充信息')).toBeInTheDocument()
      })

      // Verify structured requirements table
      await waitFor(() => {
        expect(screen.getByText('序号')).toBeInTheDocument()
        expect(screen.getByText('需要补充的信息')).toBeInTheDocument()
        expect(screen.getByText('请提供发票号码')).toBeInTheDocument()
        expect(screen.getByText('请确认交易日期')).toBeInTheDocument()
        expect(screen.getByText('请上传付款凭证')).toBeInTheDocument()
      })

      // Verify helpful tip
      await waitFor(() => {
        expect(screen.getByText(/请在上方输入框中提供所需信息/)).toBeInTheDocument()
      })
    })
  })

  describe('Error scenarios', () => {
    it('should handle network errors during audit', async () => {
      const user = userEvent.setup()

      global.fetch.mockRejectedValue(new Error('Network connection failed'))

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '测试网络错误')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // Verify error handling
      await waitFor(() => {
        expect(screen.getByText(/发生错误/)).toBeInTheDocument()
        expect(screen.getByText(/Network connection failed/)).toBeInTheDocument()
      })

      // Verify retry option is available
      const retryButton = screen.getByRole('button', { name: /重试/i })
      expect(retryButton).toBeInTheDocument()
    })

    it('should handle malformed streaming data gracefully', async () => {
      const user = userEvent.setup()

      // Mock malformed streaming response
      const mockReader = {
        read: vi.fn()
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: 正常分析内容\n\n') 
          })
          .mockResolvedValueOnce({ 
            done: false, 
            value: new TextEncoder().encode('data: <INVALID_MARKER>malformed json{invalid\n\n') 
          })
          .mockResolvedValueOnce({ done: true, value: undefined })
      }

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '测试格式错误')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // Should still show normal content
      await waitFor(() => {
        expect(screen.getByText(/正常分析内容/)).toBeInTheDocument()
      })

      // Should handle malformed data gracefully (not crash)
      await waitFor(() => {
        expect(screen.queryByText(/发生错误/)).not.toBeInTheDocument()
      })
    })
  })

  describe('Performance and responsiveness', () => {
    it('should maintain responsive UI during long streaming sessions', async () => {
      const user = userEvent.setup()

      // Mock long streaming response
      const mockReader = {
        read: vi.fn()
      }

      // Generate many small chunks
      for (let i = 0; i < 50; i++) {
        mockReader.read.mockResolvedValueOnce({ 
          done: false, 
          value: new TextEncoder().encode(`data: 分析步骤 ${i + 1}...\n\n`) 
        })
      }
      mockReader.read.mockResolvedValueOnce({ done: true, value: undefined })

      global.fetch.mockResolvedValue({
        ok: true,
        body: {
          getReader: () => mockReader
        }
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      await user.type(input, '长时间分析测试')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await user.click(sendButton)

      // UI should remain responsive
      await waitFor(() => {
        expect(screen.getByText('审核分析')).toBeInTheDocument()
      })

      // Should be able to interact with other elements
      const stopButton = screen.getByRole('button', { name: /停止/i })
      expect(stopButton).toBeInTheDocument()
      
      // Should be able to click stop button
      await user.click(stopButton)

      // Should stop streaming
      await waitFor(() => {
        expect(sendButton).not.toBeDisabled()
      })
    })

    it('should handle rapid successive audit requests', async () => {
      const user = userEvent.setup()

      let callCount = 0
      global.fetch.mockImplementation(() => {
        callCount++
        return Promise.resolve({
          ok: true,
          body: {
            getReader: () => ({
              read: vi.fn()
                .mockResolvedValueOnce({ 
                  done: false, 
                  value: new TextEncoder().encode(`data: 请求 ${callCount} 分析中...\n\n`) 
                })
                .mockResolvedValueOnce({ done: true, value: undefined })
            })
          }
        })
      })

      render(<Agent {...mockProps} />)

      const auditButton = screen.getByText('单据审核')
      await user.click(auditButton)

      const input = screen.getByPlaceholderText(/请输入消息/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })

      // Send multiple requests rapidly
      await user.type(input, '第一个请求')
      await user.click(sendButton)

      await user.clear(input)
      await user.type(input, '第二个请求')
      await user.click(sendButton)

      // Should handle both requests appropriately
      await waitFor(() => {
        expect(screen.getByText(/请求 1 分析中/)).toBeInTheDocument()
      })

      // Second request should either queue or replace first
      await waitFor(() => {
        expect(callCount).toBeGreaterThanOrEqual(1)
      })
    })
  })
})