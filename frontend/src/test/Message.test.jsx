import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
// We need to extract the Message component from Agent.jsx
// For now, let's create a simple test component
const Message = ({ message, onRetry, handleCardConfirm, sendMessageWithContext }) => {
  // This is a simplified version for testing
  const MESSAGE_TYPES = {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system',
    FILE: 'file',
    CARD: 'card',
    ERROR: 'error',
    THINKING: 'thinking',
    ANALYSIS: 'analysis',
    CREATING_CARDS: 'creating_cards',
    INFO: 'info',
    AUDIT_ANALYSIS: 'audit_analysis',
    AUDIT_RESULT: 'audit_result',
    AUDIT_INFO_REQUEST: 'audit_info_request'
  }

  const renderContent = () => {
    switch (message.type) {
      case MESSAGE_TYPES.AUDIT_ANALYSIS:
        return (
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-2 h-2 bg-emerald-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
              <span className="text-xs font-medium text-emerald-600 uppercase tracking-wide">审核分析</span>
            </div>
            <div className="text-emerald-800 leading-relaxed text-sm prose prose-sm max-w-none">
              <div data-testid="markdown">{message.content}</div>
            </div>
          </div>
        )
      case MESSAGE_TYPES.AUDIT_RESULT:
        return (
          <div className={`p-4 rounded-2xl max-w-[80%] shadow-soft border-2 ${
            message.result === 'audit_complete' 
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300' 
              : message.result === 'audit_rejected'
              ? 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'
              : 'bg-gradient-to-r from-amber-50 to-yellow-50 border-amber-300'
          }`}>
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full ${
                message.result === 'audit_complete' 
                  ? 'bg-green-500' 
                  : message.result === 'audit_rejected'
                  ? 'bg-red-500'
                  : 'bg-amber-500'
              }`}></div>
              <span className={`text-xs font-bold uppercase tracking-wide ${
                message.result === 'audit_complete' 
                  ? 'text-green-600' 
                  : message.result === 'audit_rejected'
                  ? 'text-red-600'
                  : 'text-amber-600'
              }`}>
                {message.result === 'audit_complete' ? '✅ 审核通过' : 
                 message.result === 'audit_rejected' ? '❌ 审核不通过' : 
                 '⚠️ 需要补充信息'}
              </span>
            </div>
            <div className="font-medium leading-relaxed">
              {message.content}
            </div>
          </div>
        )
      case MESSAGE_TYPES.AUDIT_INFO_REQUEST:
        return (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-300 p-4 rounded-2xl max-w-[85%] shadow-soft">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-xs font-bold text-blue-600 uppercase tracking-wide">📋 需要补充信息</span>
            </div>
            {message.required_info && message.required_info.length > 0 ? (
              <div className="space-y-3">
                <div className="text-blue-800 font-medium">
                  {message.content || '请提供以下信息以完成审核：'}
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-200">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-blue-200">
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">序号</th>
                        <th className="text-left py-2 px-1 text-blue-700 font-medium">需要补充的信息</th>
                      </tr>
                    </thead>
                    <tbody>
                      {message.required_info.map((info, index) => (
                        <tr key={index} className="border-b border-blue-100 last:border-b-0">
                          <td className="py-2 px-1 text-blue-600 font-medium">{index + 1}</td>
                          <td className="py-2 px-1 text-blue-800">{info}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。
                </div>
              </div>
            ) : (
              <div className="text-blue-800">
                {message.content}
                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded-lg mt-2">
                  💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。
                </div>
              </div>
            )}
          </div>
        )
      default:
        return <div className="p-3">{message.content}</div>
    }
  }

  return (
    <div className="mb-4">
      <div className="flex items-start gap-2 justify-start">
        <div className="flex-1">
          {renderContent()}
        </div>
      </div>
      <div className="text-xs text-gray-500 mt-1 px-3 text-left">
        {new Date(message.timestamp).toLocaleTimeString()}
      </div>
    </div>
  )
}

// Mock ReactMarkdown
vi.mock('react-markdown', () => ({
  default: ({ children }) => <div data-testid="markdown">{children}</div>
}))

const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  FILE: 'file',
  CARD: 'card',
  ERROR: 'error',
  THINKING: 'thinking',
  ANALYSIS: 'analysis',
  CREATING_CARDS: 'creating_cards',
  INFO: 'info',
  AUDIT_ANALYSIS: 'audit_analysis',
  AUDIT_RESULT: 'audit_result',
  AUDIT_INFO_REQUEST: 'audit_info_request'
}

describe('Message Component - Audit UI Improvements', () => {
  const mockProps = {
    onRetry: vi.fn(),
    handleCardConfirm: vi.fn(),
    sendMessageWithContext: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('AUDIT_ANALYSIS message type', () => {
    it('should render audit analysis message with correct styling', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_ANALYSIS,
        content: '正在分析单据内容...',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      // Check for audit analysis specific elements
      expect(screen.getByText('审核分析')).toBeInTheDocument()
      expect(screen.getByText('正在分析单据内容...')).toBeInTheDocument()
      
      // Check for emerald color styling (via class names)
      const container = screen.getByText('审核分析').closest('.from-emerald-50')
      expect(container).toHaveClass('from-emerald-50', 'to-teal-50', 'border-emerald-200')
    })

    it('should render markdown content in audit analysis', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_ANALYSIS,
        content: '**重要发现**: 单据金额异常\n\n- 检查项目1\n- 检查项目2',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByTestId('markdown')).toBeInTheDocument()
      expect(screen.getByTestId('markdown')).toHaveTextContent('**重要发现**: 单据金额异常')
    })

    it('should show animated dots for analysis in progress', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_ANALYSIS,
        content: '分析中...',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      // Check for animated dots
      const dots = screen.getByText('审核分析').parentElement.querySelectorAll('.animate-pulse')
      expect(dots).toHaveLength(3)
    })
  })

  describe('AUDIT_RESULT message type', () => {
    it('should render audit complete result with green styling', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_RESULT,
        content: '单据审核通过，所有信息完整准确。',
        result: 'audit_complete',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('✅ 审核通过')).toBeInTheDocument()
      expect(screen.getByText('单据审核通过，所有信息完整准确。')).toBeInTheDocument()
      
      // Check for green styling
      const container = screen.getByText('✅ 审核通过').closest('.from-green-50')
      expect(container).toHaveClass('from-green-50', 'to-emerald-50', 'border-green-300')
    })

    it('should render audit rejected result with red styling', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_RESULT,
        content: '单据审核不通过，存在以下问题：金额不符。',
        result: 'audit_rejected',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('❌ 审核不通过')).toBeInTheDocument()
      expect(screen.getByText('单据审核不通过，存在以下问题：金额不符。')).toBeInTheDocument()
      
      // Check for red styling
      const container = screen.getByText('❌ 审核不通过').closest('.from-red-50')
      expect(container).toHaveClass('from-red-50', 'to-pink-50', 'border-red-300')
    })

    it('should render other audit result with amber styling', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_RESULT,
        content: '需要进一步确认信息。',
        result: 'other',
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('⚠️ 需要补充信息')).toBeInTheDocument()
      expect(screen.getByText('需要进一步确认信息。')).toBeInTheDocument()
      
      // Check for amber styling
      const container = screen.getByText('⚠️ 需要补充信息').closest('.from-amber-50')
      expect(container).toHaveClass('from-amber-50', 'to-yellow-50', 'border-amber-300')
    })
  })

  describe('AUDIT_INFO_REQUEST message type', () => {
    it('should render info request with structured list', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_INFO_REQUEST,
        content: '请提供以下信息以完成审核：',
        required_info: [
          '请提供发票号码',
          '请确认交易日期',
          '请上传相关凭证'
        ],
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('📋 需要补充信息')).toBeInTheDocument()
      expect(screen.getByText('请提供以下信息以完成审核：')).toBeInTheDocument()
      
      // Check for structured table
      expect(screen.getByText('序号')).toBeInTheDocument()
      expect(screen.getByText('需要补充的信息')).toBeInTheDocument()
      
      // Check for each required info item
      expect(screen.getByText('请提供发票号码')).toBeInTheDocument()
      expect(screen.getByText('请确认交易日期')).toBeInTheDocument()
      expect(screen.getByText('请上传相关凭证')).toBeInTheDocument()
      
      // Check for numbered items
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
    })

    it('should render info request without structured list when required_info is empty', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_INFO_REQUEST,
        content: '请提供更多详细信息。',
        required_info: [],
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('📋 需要补充信息')).toBeInTheDocument()
      expect(screen.getByText('请提供更多详细信息。')).toBeInTheDocument()
      
      // Should not show table headers
      expect(screen.queryByText('序号')).not.toBeInTheDocument()
      expect(screen.queryByText('需要补充的信息')).not.toBeInTheDocument()
    })

    it('should show helpful tip for user action', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_INFO_REQUEST,
        content: '需要补充信息',
        required_info: ['测试信息'],
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      expect(screen.getByText('💡 请在上方输入框中提供所需信息，或上传相关文件后重新提交审核。')).toBeInTheDocument()
    })

    it('should render with blue styling', () => {
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_INFO_REQUEST,
        content: '需要补充信息',
        required_info: [],
        timestamp: new Date()
      }

      render(<Message message={message} {...mockProps} />)

      const container = screen.getByText('📋 需要补充信息').closest('.from-blue-50')
      expect(container).toHaveClass('from-blue-50', 'to-indigo-50', 'border-blue-300')
    })
  })

  describe('Message timestamp', () => {
    it('should display formatted timestamp', () => {
      const testDate = new Date('2024-01-01T12:30:45')
      const message = {
        id: '1',
        type: MESSAGE_TYPES.AUDIT_ANALYSIS,
        content: '测试内容',
        timestamp: testDate
      }

      render(<Message message={message} {...mockProps} />)

      // Check if timestamp is displayed (format may vary by locale)
      const timeElement = screen.getByText(testDate.toLocaleTimeString())
      expect(timeElement).toBeInTheDocument()
      expect(timeElement).toHaveClass('text-xs', 'text-gray-500')
    })
  })
})