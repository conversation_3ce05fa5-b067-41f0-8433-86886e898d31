// src/api/index.js

// 后端基础地址配置，支持多环境
export const backendBase = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// 通用请求函数
const request = async (url, options = {}) => {
  const fullUrl = url.startsWith('http') ? url : `${backendBase}${url}`;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  if (config.body && typeof config.body === 'object') {
    config.body = JSON.stringify(config.body);
  }

  try {
    const response = await fetch(fullUrl, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }
    
    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

const api = {
  // 通用 HTTP 方法
  get: (url, options = {}) => request(url, { ...options, method: 'GET' }),
  post: (url, data, options = {}) => request(url, { ...options, method: 'POST', body: data }),
  put: (url, data, options = {}) => request(url, { ...options, method: 'PUT', body: data }),
  delete: (url, options = {}) => request(url, { ...options, method: 'DELETE' }),
  patch: (url, data, options = {}) => request(url, { ...options, method: 'PATCH', body: data }),

  // 原有的特定接口（保持向后兼容）
  createSubject: (data) => api.post('/api/subject/create', data),
  updateAsset: (data) => api.post('/api/asset/update', data),
  deleteStaff: (data) => api.post('/api/staff/delete', data),
  querySupplier: (data) => api.post('/api/supplier/query', data),
  createVoucher: (data) => api.post('/api/voucher/create', data),
  getSubjects: () => api.get('/api/subjects'),
  
  // MCP 相关接口
  getMCPConfig: () => api.get('/api/mcp/config'),
  updateMCPConfig: (config) => api.post('/api/mcp/config', config),
  startMCPServer: (serverName) => api.post(`/api/mcp/servers/${serverName}/start`),
  stopMCPServer: (serverName) => api.post(`/api/mcp/servers/${serverName}/stop`),
  getMCPServersStatus: () => api.get('/api/mcp/servers/status'),
  getMCPTools: () => api.get('/api/mcp/tools'),
  callMCPTool: (toolName, parameters) => api.post('/api/mcp/tools/call', { tool_name: toolName, parameters }),
  reloadMCPConfig: () => api.post('/api/mcp/reload'),
};

export default api; 