{"name": "accounting-app", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui"}, "dependencies": {"@headlessui/react": "^2.2.7", "axios": "^1.7.0", "decimal.js": "^10.4.3", "framer-motion": "^11.0.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.1", "react-textarea-autosize": "^8.5.9"}, "devDependencies": {"@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.4.12"}}