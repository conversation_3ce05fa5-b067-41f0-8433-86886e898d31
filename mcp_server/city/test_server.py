#!/usr/bin/env python3
"""
城市分级查询 MCP Server 测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from server import CITY_TO_TIER, CITY_TIERS

# 直接实现测试函数，避免调用装饰器包装的函数
def get_city_tier_test(city_name: str) -> str:
    """测试版本的城市分级查询"""
    city_name = city_name.strip()
    if not city_name:
        return "请提供城市名称"
    
    tier = CITY_TO_TIER.get(city_name)
    if tier:
        return f"{city_name} 属于 {tier}"
    else:
        possible_matches = []
        for city in CITY_TO_TIER.keys():
            if city_name in city or city in city_name:
                possible_matches.append(city)
        
        if possible_matches:
            suggestions = "、".join(possible_matches[:5])
            return f"未找到城市 '{city_name}'，您是否想查询：{suggestions}？"
        else:
            return f"未找到城市 '{city_name}'，请检查城市名称是否正确"

def list_cities_by_tier_test(tier: str) -> str:
    """测试版本的分级城市列表"""
    tier = tier.strip()
    if tier not in CITY_TIERS:
        return f"无效的城市分级：{tier}。可选分级：一线城市、新一线城市、二线城市、三线城市、四线城市、五线城市"
    
    cities = CITY_TIERS[tier]
    cities_text = "、".join(cities)
    return f"{tier}（{len(cities)}个）：{cities_text}"

def get_all_tiers_test() -> str:
    """测试版本的所有分级统计"""
    result = []
    for tier, cities in CITY_TIERS.items():
        result.append(f"{tier}：{len(cities)}个城市")
    
    total_cities = sum(len(cities) for cities in CITY_TIERS.values())
    result.append(f"\n总计：{total_cities}个城市")
    
    return "\n".join(result)

def search_cities_test(keyword: str) -> str:
    """测试版本的城市搜索"""
    keyword = keyword.strip()
    if not keyword:
        return "请提供搜索关键词"
    
    matches = []
    for city, tier in CITY_TO_TIER.items():
        if keyword in city:
            matches.append(f"{city}({tier})")
    
    if matches:
        if len(matches) > 20:
            return f"找到 {len(matches)} 个匹配城市，前20个：" + "、".join(matches[:20]) + "..."
        else:
            return f"找到 {len(matches)} 个匹配城市：" + "、".join(matches)
    else:
        return f"未找到包含 '{keyword}' 的城市"

def test_city_lookup():
    """测试城市查询功能"""
    print("=== 城市查询测试 ===")
    
    test_cities = ["上海", "成都", "济南", "乌鲁木齐", "枣庄", "忻州", "不存在的城市"]
    
    for city in test_cities:
        result = get_city_tier_test(city)
        print(f"{city}: {result}")
    
    print()

def test_tier_statistics():
    """测试分级统计"""
    print("=== 分级统计测试 ===")
    
    result = get_all_tiers_test()
    print(result)
    print()

def test_tier_listing():
    """测试分级列表"""
    print("=== 分级列表测试 ===")
    
    for tier in ["一线城市", "新一线城市"]:
        result = list_cities_by_tier_test(tier)
        print(f"{tier}: {result[:100]}...")  # 只显示前100个字符
    print()

def test_search():
    """测试搜索功能"""
    print("=== 搜索测试 ===")
    
    search_terms = ["州", "海", "山"]
    for term in search_terms:
        result = search_cities_test(term)
        print(f"搜索'{term}': {result[:150]}...")  # 只显示前150个字符
    print()

if __name__ == "__main__":
    test_city_lookup()
    test_tier_statistics()
    test_tier_listing()
    test_search()
    
    print("测试完成！")