# MCP SSE 传输故障排除指南

## 问题诊断

### 1. 405 Method Not Allowed 错误

**症状**: 服务器返回 405 错误，日志显示 "POST /sse HTTP/1.1" 405 Method Not Allowed

**原因**: SSE 端点通常不接受 POST 请求，需要使用正确的消息端点

**解决方案**:
1. 检查服务器的实际端点结构
2. 使用调试工具确定正确的消息端点
3. 更新配置使用正确的 URL

### 2. 端点检测

使用调试脚本检查服务器端点：

```bash
python scripts/debug_mcp_server.py http://localhost:8080
```

这将测试所有可能的端点并显示哪些可用。

### 3. 常见的 MCP SSE 端点模式

#### 模式 1: 分离的消息和 SSE 端点
- 消息端点: `POST /message` 或 `POST /api/message`
- SSE 端点: `GET /sse` (用于接收流)

#### 模式 2: 统一端点
- 消息端点: `POST /sse/message`
- SSE 流: `GET /sse`

#### 模式 3: RPC 风格
- 消息端点: `POST /rpc` 或 `POST /jsonrpc`
- SSE 流: `GET /sse`

## 配置示例

### 城市分级服务器配置

```json
{
  "mcpServers": {
    "city-tier": {
      "transport": "sse",
      "url": "http://localhost:8080/sse",
      "env": {},
      "disabled": false,
      "autoApprove": [],
      "timeout": 30,
      "retryCount": 3
    }
  }
}
```

## 测试步骤

### 1. 手动测试服务器

```bash
# 测试根路径
curl http://localhost:8080/

# 测试健康检查
curl http://localhost:8080/health

# 测试消息端点
curl -X POST http://localhost:8080/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":"test","method":"tools/list"}'
```

### 2. 使用调试脚本

```bash
# 全面调试
python scripts/debug_mcp_server.py http://localhost:8080

# 快速测试
python scripts/quick_test_sse.py

# 测试特定服务器
python scripts/test_city_tier_mcp.py
```

### 3. 检查服务器日志

观察 MCP 服务器的日志输出，查看：
- 哪些端点被访问
- 返回的状态码
- 错误信息

## 常见问题解决

### 问题 1: 连接超时

**解决方案**:
1. 增加超时时间
2. 检查网络连接
3. 确认服务器正在运行

```json
{
  "timeout": 60,
  "retryCount": 5
}
```

### 问题 2: 工具发现失败

**解决方案**:
1. 检查服务器是否实现了 `tools/list` 方法
2. 验证 JSON-RPC 请求格式
3. 查看服务器响应格式

### 问题 3: SSE 流解析错误

**解决方案**:
1. 检查 Content-Type 是否为 `text/event-stream`
2. 验证 SSE 数据格式
3. 确认数据编码 (UTF-8)

## 调试技巧

### 1. 启用详细日志

在后端配置中启用 DEBUG 级别日志：

```python
import logging
logging.getLogger('core.mcp_client').setLevel(logging.DEBUG)
```

### 2. 使用网络抓包工具

使用 Wireshark 或浏览器开发者工具查看实际的 HTTP 请求和响应。

### 3. 检查服务器文档

查看 MCP 服务器的文档，了解：
- 支持的端点
- 请求格式
- 响应格式
- 认证要求

## 最佳实践

### 1. 配置验证

在添加新服务器前，先使用调试工具验证端点。

### 2. 错误处理

配置适当的超时和重试次数。

### 3. 监控

定期检查服务器状态和连接健康度。

### 4. 文档

为自定义 MCP 服务器编写清晰的端点文档。

## 支持的 MCP 协议版本

当前实现支持：
- MCP 协议版本: 2024-11-05
- JSON-RPC 2.0
- SSE (Server-Sent Events)
- HTTP/HTTPS 传输

## 获取帮助

如果问题仍然存在：

1. 检查 MCP 服务器的官方文档
2. 查看服务器的示例配置
3. 在 GitHub 上提交 issue
4. 联系服务器开发者获取支持