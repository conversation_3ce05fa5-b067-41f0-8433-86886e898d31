# Company Information Design Document

## Overview
This document outlines the design for storing and utilizing company-specific information to improve the accuracy of the accounting agent system, particularly in subject matching and transaction analysis.

## Current State Analysis
The current system lacks dedicated storage for company-specific information that could help improve accounting subject matching. The system currently only stores:
- Accounting subjects
- Assets
- Staff information

However, it does not store company-specific context such as business scope, industry, size, or other characteristics that could significantly improve the accuracy of subject matching.

## Proposed Company Information Data Structure

### Database Model
We propose adding a new `Company` model with the following fields:

```python
class Company(Base):
    __tablename__ = "companies"
    
    # Basic company information
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False, comment="公司名称")
    business_scope = Column(Text, nullable=True, comment="经营范围")
    
    # Industry and classification
    industry = Column(String, nullable=True, comment="行业")
    company_size = Column(String, nullable=True, comment="公司规模")
    
    # Tax and accounting information
    tax_id = Column(String, nullable=True, comment="税号")
    accounting_standards = Column(String, nullable=True, comment="会计准则")
    
    # Additional metadata
    established_date = Column(Date, nullable=True, comment="成立日期")
    registered_capital = Column(DECIMAL(18, 2), nullable=True, comment="注册资本")
    status = Column(String, default="active", comment="状态")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
```

### Key Fields for Accounting Subject Matching

1. **business_scope (经营范围)** - This is the most important field as mentioned in the user's request. It describes what the company actually does, which can help determine which accounting subjects are most relevant.

2. **industry (行业)** - Helps categorize the company and apply industry-specific accounting rules.

3. **accounting_standards (会计准则)** - Indicates which accounting standards the company follows (e.g., enterprise vs small enterprise).

## Integration with Existing System

### Context Data Gathering
The agent system will be updated to gather company information alongside other context data in the `_gather_context_data` method:

```python
async def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
    # Existing data gathering
    subjects_result = await db.execute(select(SubjectAccount))
    assets_result = await db.execute(select(Asset))
    staff_result = await db.execute(select(Staff))
    
    # New company information gathering
    company_result = await db.execute(select(Company).where(Company.id == 1))  # Assuming single company for now
    company = company_result.scalar_one_or_none()
    
    return {
        "subjects": [dict(row) for row in subjects_result],
        "assets": [dict(row) for row in assets_result],
        "staff": [dict(row) for row in staff_result],
        "company": company.to_dict() if company else None  # New company data
    }
```

### Prompt Template Enhancement
The prompt templates will be enhanced to include company information where relevant:

1. **Subject Matching Prompt** - Will receive company business scope to better match subjects
2. **Transaction Analysis Prompt** - Can use company context to better understand transactions
3. **Amount Calculation Prompt** - May use company tax information for calculations

## Benefits of This Approach

1. **Improved Subject Matching Accuracy** - By knowing what a company actually does (business scope), the system can better match transactions to appropriate accounting subjects.

2. **Industry-Specific Processing** - Different industries have different accounting requirements and common subjects.

3. **Better Context Understanding** - The system will have better context for interpreting user inputs and making accounting decisions.

4. **Reduced Ambiguity** - Company information can help resolve ambiguities in transaction descriptions.

## Implementation Plan

1. Create the Company model and database table
2. Add company information to the context data gathering process
3. Modify relevant prompt templates to accept and utilize company information
4. Update the agent system to pass company information to prompts
5. Test with sample transactions to verify improvements

## Sample Usage in Subject Matching

With company information, the subject matching prompt can be enhanced like this:

```
你是一个专业的会计科目匹配专家，请根据提取的交易信息和公司背景信息匹配最合适的会计科目。

公司信息：
名称：{company_name}
经营范围：{business_scope}
行业：{industry}

交易信息：
类型：{transaction_type}
内容：{content}
背景：{context}
金额：{amount}

现有科目表：
{subjects}

请根据公司实际经营范围和行业特点，匹配最合适的会计科目...
```

This enhancement allows the LLM to make more informed decisions based on what the company actually does rather than just generic accounting rules.