# Improved Subject Matching Prompt with Company Information

## Overview
This is an enhanced version of the subject matching prompt that incorporates company information to improve the accuracy of accounting subject identification.

## Enhanced Prompt Template

```
你是一个专业的会计科目匹配专家，请根据提取的交易信息和公司背景信息匹配最合适的会计科目。

公司信息：
名称：{company_name}
经营范围：{business_scope}
行业：{industry}
会计准则：{accounting_standards}

交易信息：
类型：{transaction_type}
内容：{content}
背景：{context}
金额：{amount}

现有科目表：
{subjects}

对于包含多个分录的复杂交易，请为每个分录匹配合适的科目。

请根据以下规则匹配最合适的科目：

1. 结合公司实际经营范围判断交易相关性
2. 考虑公司所属行业特点选择合适科目
3. 根据公司采用的会计准则进行科目匹配
4. 根据交易类型和内容确定借贷方向
5. 在对应方向的科目中寻找最匹配的科目
6. 如果没有完全匹配的科目，选择最接近的上级科目
7. 必要时可以建议创建明细科目

请按照以下JSON格式返回结果：
{
  "matched_entries": [
    {
      "type": "main_business|related_expense|tax|other",
      "subject": {
        "code": "科目编码",
        "name": "科目名称",
        "match_score": 0.0-1.0的匹配度评分,
        "reason": "匹配理由，包括考虑了哪些公司信息因素"
      },
      "amount": 数值,
      "direction": "debit|credit"
    }
  ],
  "unmatched_entries": [
    // 无法匹配的分录
  ],
  "need_new_subjects": [
    // 需要创建新科目的分录
  ]
}
```

## Key Improvements

1. **Company Context Section** - Added a dedicated section for company information including:
   - Company name
   - Business scope (经营范围) - Most important for subject matching
   - Industry
   - Accounting standards

2. **Enhanced Matching Rules** - Added rules that specifically instruct the LLM to consider company information:
   - Rule 1: Consider business scope for transaction relevance
   - Rule 2: Consider industry characteristics
   - Rule 3: Consider accounting standards

3. **Improved Reasoning Field** - Updated the reason field to explicitly ask for which company information factors were considered

## Benefits

This enhanced prompt will allow the LLM to make more informed decisions by understanding:
- What the company actually does (business scope)
- Industry-specific accounting practices
- Company-specific accounting standards
- How these factors relate to the transaction being analyzed

For example, if a company's business scope is "软件开发和技术服务", the system can better identify that a transaction related to "软件销售" should be matched to "主营业务收入-软件销售收入" rather than a generic "其他业务收入".