# Agent System Updates for Company Information Integration

## Overview
This document outlines the necessary changes to the agent system to gather and pass company information to the prompts for improved accounting subject matching and transaction analysis.

## Current System Architecture
The current system works as follows:
1. `_gather_context_data()` method collects subjects, assets, staff, and experience data
2. Process message method calls `_gather_context_data()` to get context
3. Specific data is extracted from context and passed to individual chain inputs
4. Chains use the data to build prompts via the prompt manager

## Required Changes

### 1. Update Company Model
First, we need to create the Company model as designed in the company information design document.

### 2. Update `_gather_context_data` Method
Modify the `_gather_context_data` method in [`backend/core/agent/agent.py`](file:///media/shun/bigdata/Projects/app365/account_2/account_app/backend/core/agent/agent.py) to also collect company information:

```python
async def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
    """异步收集上下文数据"""
    logger.info(f"Entering _gather_context_data with user_id={user_id}")
    db = AsyncSessionLocal(bind=engine)
    try:
        subjects_result = await db.execute(select(SubjectAccount))
        assets_result = await db.execute(select(Asset))
        staffs_result = await db.execute(select(Staff))
        
        # NEW: Collect company information
        company_result = await db.execute(select(Company))
        company = company_result.scalars().first()  # Assuming single company for now
        
        subjects = [
            {c.name: getattr(s, c.name) for c in s.__table__.columns}
            for s in subjects_result.scalars().all()
        ]
        assets = [
            {c.name: getattr(a, c.name) for c in a.__table__.columns}
            for a in assets_result.scalars().all()
        ]
        staff = [
            {c.name: getattr(st, c.name) for c in st.__table__.columns}
            for st in staffs_result.scalars().all()
        ]
    finally:
        await db.close()
    from api.experience import get_experience
    experience = get_experience(user_id, limit=5) if user_id else []
    return {
        "subjects": subjects,
        "assets": assets,
        "staff": staff,
        "experience": experience,
        "company": {  # NEW: Add company data
            "name": company.name if company else "",
            "business_scope": company.business_scope if company else "",
            "industry": company.industry if company else "",
            "accounting_standards": company.accounting_standards if company else "",
            "tax_info": company.tax_id if company else ""
        } if company else None
    }
```

### 3. Update Chain Inputs
Modify the chain inputs in the process_message and stream_message methods to pass company information to relevant chains:

#### Transaction Analysis Chain Input Update
```python
# Step 1: Analyze the transaction
context_company = context_data.get("company", {})
transaction_inputs = {
    "user_input": enhanced_message,
    "current_date": datetime.now().strftime("%Y-%m-%d"),
    "company_name": context_company.get("name", ""),
    "business_scope": context_company.get("business_scope", ""),
    "industry": context_company.get("industry", "")
}
```

#### Subject Matching Chain Input Update
```python
# Step 2: Match subjects
context_company = context_data.get("company", {})
subject_inputs = {
    "transaction_type": transaction_data.get("transaction_type", ""),
    "content": transaction_data.get("content", ""),
    "context": transaction_data.get("context", ""),
    "amount": transaction_data.get("primary_amount", 0),
    "subjects": subjects_compact,
    "current_date": datetime.now().strftime("%Y-%m-%d"),
    "user_id": user_id,
    "company_name": context_company.get("name", ""),
    "business_scope": context_company.get("business_scope", ""),
    "industry": context_company.get("industry", ""),
    "accounting_standards": context_company.get("accounting_standards", "")
}
```

#### Amount Calculation Chain Input Update
```python
# Step 3: Calculate amounts
context_company = context_data.get("company", {})
amount_inputs = {
    "transaction_type": transaction_data.get("transaction_type", ""),
    "content": transaction_data.get("content", ""),
    "context": transaction_data.get("context", ""),
    "primary_amount": transaction_data.get("primary_amount", 0),
    "secondary_amounts": transaction_data.get("secondary_amounts", []),
    "current_date": datetime.now().strftime("%Y-%m-%d"),
    "user_id": user_id,
    "company_name": context_company.get("name", ""),
    "industry": context_company.get("industry", ""),
    "tax_info": context_company.get("tax_info", "")
}
```

### 4. Update Prompt Manager
The prompt manager already supports variable substitution, so no changes are needed there. The updated prompt templates will automatically receive the company information through the chain inputs.

### 5. Update Chain Definitions
The chain definitions in [`backend/core/agent/chains.py`](file:///media/shun/bigdata/Projects/app365/account_2/account_app/backend/core/agent/chains.py) need to be updated to include the new company variables:

#### Subject Matching Chain Update
```python
# 构建链式调用
self.chain = (
    {"transaction_type": lambda x: x["transaction_type"],
     "content": lambda x: x["content"],
     "context": lambda x: x["context"],
     "amount": lambda x: x["amount"],
     "subjects": lambda x: json.dumps(x.get("subjects", []), ensure_ascii=False),
     "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
     "user_id": lambda x: x.get("user_id", "anonymous"),
     "company_name": lambda x: x.get("company_name", ""),
     "business_scope": lambda x: x.get("business_scope", ""),
     "industry": lambda x: x.get("industry", ""),
     "accounting_standards": lambda x: x.get("accounting_standards", "")}
    | chat_prompt
    | llm
    | MatchingOutputParser()
)
```

Similar updates would be needed for the Transaction Analysis Chain and Amount Calculation Chain.

## Benefits of These Changes

1. **Seamless Integration** - The changes integrate smoothly with the existing architecture
2. **Minimal Disruption** - Existing functionality remains unchanged
3. **Scalable** - Easy to extend with additional company information fields
4. **Performance** - Company information is gathered once per request and reused across chains

## Implementation Order

1. Create Company model and database table
2. Update `_gather_context_data` method
3. Update chain inputs in process_message and stream_message methods
4. Update chain definitions to accept new variables
5. Test with sample transactions

## Automatic Imbalance Correction Mechanism

### Overview
Added a comprehensive automatic imbalance correction mechanism that detects imbalances in generated accounting vouchers and automatically retries the entire chain process with adjustment information.

### Key Features
1. **Comprehensive Retry**: When an imbalance is detected, the system automatically retries the entire chain process (transaction analysis, subject matching, amount calculation, and voucher generation) up to 3 times
2. **Adjustment Information**: Adjustment suggestions from balance verification are passed to all steps in subsequent attempts
3. **Progressive Improvement**: Each retry attempt includes information from previous attempts to help the LLM make better decisions
4. **Final Output**: If all attempts fail, the system still outputs the final result with validation information

### Implementation Details
- Added `_process_chain_with_retry` method to `AgentExecutor` class that encompasses the entire chain process
- Modified `process_message` and `stream_message` methods to use the new comprehensive retry mechanism
- Updated all chain prompts (transaction analysis, subject matching, amount calculation, and voucher generation) to accept retry information and adjustment suggestions
- Implemented retry loop with maximum attempt limit (3 attempts) for the entire chain process

### How It Works
1. The system processes the entire chain (transaction analysis, subject matching, amount calculation, voucher generation) using the standard process
2. It performs mathematical and LLM-based balance verification on the generated voucher
3. If an imbalance is detected:
   - The system captures adjustment suggestions from the LLM-based verification
   - It restarts the entire chain process with the adjustment information passed to all steps
   - This process repeats up to 3 times
4. If all attempts fail, the system outputs the final result with validation information

### New Parameters
All chain prompts now accept these additional parameters:
- `current_attempt`: Current attempt number (1 for first attempt)
- `max_attempts`: Maximum number of attempts allowed (3)
- `adjustment_suggestions`: Suggestions from previous balance verification

### Benefits
1. **Holistic Correction**: Unlike the previous implementation that only retried voucher generation, this new approach retries the entire chain process, addressing issues that might originate from earlier steps
2. **Better Accuracy**: By providing adjustment information to all steps, each part of the process can make more informed decisions
3. **Robustness**: The system is more resilient to various types of errors that might cause imbalances
4. **Transparency**: Users can see how many attempts were made and what adjustments were suggested
</content>
</file>
</files>