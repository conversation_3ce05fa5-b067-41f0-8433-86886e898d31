# Improved Chain Retry Mechanism for Handling Imbalances

## Overview

This document describes the design for an improved chain retry mechanism that automatically handles imbalances in the accounting voucher generation process. When an imbalance is detected, the system will automatically attempt to correct it by restarting the chain process with adjustment information.

## Current Workflow

```mermaid
graph TD
    A[Start] --> B[Transaction Analysis]
    B --> C[Subject Matching]
    C --> D[Amount Calculation]
    D --> E[Voucher Generation]
    E --> F[Mathematical Balance Check]
    F --> G[LLM Balance Verification]
    G --> H{Is Balanced?}
    H -->|Yes| I[Return Result]
    H -->|No| J[Return With Validation Info]
```

## Proposed Improved Workflow

```mermaid
graph TD
    A[Start] --> B[Transaction Analysis]
    B --> C[Subject Matching]
    C --> D[Amount Calculation]
    D --> E[Voucher Generation]
    E --> F[Mathematical Balance Check]
    F --> G[LLM Balance Verification]
    G --> H{Is Balanced?}
    H -->|Yes| I[Return Result]
    H -->|No| J[Check Retry Count]
    J --> K{Retry < Max Attempts?}
    K -->|Yes| L[Add Adjustment Info]
    L --> M[Restart Chain]
    M --> N[Transaction Analysis]
    N --> O[Subject Matching]
    O --> P[Amount Calculation]
    P --> Q[Voucher Generation]
    Q --> R[Mathematical Balance Check]
    R --> S[LLM Balance Verification]
    S --> T{Is Balanced?}
    T -->|Yes| I
    T -->|No| U[Increment Retry Count]
    U --> K
    K -->|No| I
```

## Implementation Details

### 1. Retry Mechanism Configuration

- Maximum retry attempts: 3
- Retry counter reset for each new transaction
- Adjustment information passed to subsequent attempts

### 2. Imbalance Detection

Imbalances are detected at two points:
1. Mathematical verification using `BalanceCheckTool`
2. LLM-based verification using `BalanceVerificationChain`

### 3. Adjustment Information

When an imbalance is detected, the following information will be passed to subsequent attempts:
- Previous attempt results
- LLM adjustment suggestions
- Retry attempt count

### 4. Chain Input Modifications

Chain inputs will be extended to include:
- `retry_count`: Number of retry attempts
- `adjustment_suggestions`: Suggestions from previous balance verification
- `previous_attempts`: Summary of previous attempt results

## Implementation Plan

### Step 1: Create a Wrapper Method

Create a new method in `AgentExecutor` that wraps the voucher generation process with retry logic.

### Step 2: Modify Chain Inputs

Update chain input preparation to include retry information and adjustment suggestions.

### Step 3: Implement Retry Loop

Implement the retry loop with maximum attempt limit and adjustment information passing.

### Step 4: Update Response Handling

Modify the response to include retry attempt information and final validation status.