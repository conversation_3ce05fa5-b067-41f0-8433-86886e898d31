# Improved Transaction Analysis Prompt with Company Information

## Overview
This is an enhanced version of the transaction analysis prompt that incorporates company information to improve the accuracy of transaction information extraction.

## Enhanced Prompt Template

```
你是一个专业的会计信息分析师，请根据公司背景信息从用户提供的交易描述中提取关键信息。

公司信息：
名称：{company_name}
经营范围：{business_scope}
行业：{industry}

用户输入: {user_input}

请分析并提取以下信息：

1. 交易类型：收入/支出/转账/其他
2. 主要交易金额：交易的核心金额数值
3. 次要金额：税费、手续费、折扣等其他金额
4. 交易日期：明确的日期或相对日期描述
5. 交易参与方：涉及的个人、公司或部门
6. 交易内容：具体购买或销售的物品/服务
7. 交易背景：业务场景描述

在分析过程中，请特别注意：
1. 结合公司经营范围理解交易内容的相关性
2. 考虑公司行业特点识别专业术语
3. 对于不明确的信息，结合公司业务背景进行合理推断

请按照以下JSON格式返回结果：
{
  "transaction_type": "收入|支出|转账|其他",
  "primary_amount": 数值,
  "secondary_amounts": [
    {
      "type": "税费|手续费|折扣|其他",
      "amount": 数值,
      "description": "金额描述"
    }
  ],
  "date": "YYYY-MM-DD格式的日期",
  "parties": ["参与方1", "参与方2"],
  "content": "交易内容描述",
  "context": "业务背景描述",
  "confidence": 0.0-1.0的置信度评分,
  "amount_analysis": "关于如何确定主要金额和次要金额的分析说明"
}

如果信息不完整，请在返回的JSON中包含：
{
  "missing_info": ["缺少的信息1", "缺少的信息2"],
  "ask_user": "需要向用户询问的问题"
}
```

## Key Improvements

1. **Company Context Section** - Added company information at the beginning to provide context for analysis
2. **Enhanced Analysis Instructions** - Added specific instructions to consider company information during analysis:
   - Use business scope to understand transaction relevance
   - Consider industry characteristics for terminology recognition
   - Use company context for reasonable assumptions
3. **Improved Context Understanding** - The LLM will better understand what constitutes a relevant transaction for this specific company

## Benefits

This enhanced prompt will allow the LLM to:
- Better understand industry-specific terminology
- Make more accurate assumptions based on company context
- Identify relevant transactions more effectively
- Provide more accurate confidence scoring based on company familiarity