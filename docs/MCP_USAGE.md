# MCP (Model Context Protocol) 使用指南

## 概述

本系统支持 MCP 的三种传输方式：
- **stdio**: 标准输入输出，适用于本地命令行工具
- **SSE**: Server-Sent Events，适用于实时流式数据
- **streamable-http**: 流式HTTP，适用于RESTful API

## 配置文件

MCP 配置文件位于 `.kiro/settings/mcp.json`，支持工作区级别和用户级别配置。

### 配置结构

```json
{
  "mcpServers": {
    "服务器名称": {
      "transport": "stdio|sse|streamable-http",
      "command": "启动命令 (仅stdio)",
      "args": ["命令参数数组 (仅stdio)"],
      "url": "服务器URL (仅http/sse)",
      "env": {"环境变量": "值"},
      "disabled": false,
      "autoApprove": ["自动批准的工具列表"],
      "timeout": 30,
      "retryCount": 3
    }
  }
}
```

## 传输方式详解

### 1. stdio 传输

最常用的方式，适用于本地工具和命令行程序。

```json
{
  "filesystem": {
    "transport": "stdio",
    "command": "uvx",
    "args": ["mcp-server-filesystem@latest", "--path", "."],
    "env": {},
    "disabled": false,
    "autoApprove": ["read_file", "list_directory"]
  }
}
```

**常用 stdio 服务器：**
- `uvx mcp-server-filesystem@latest` - 文件系统操作
- `uvx awslabs.aws-documentation-mcp-server@latest` - AWS 文档
- `uvx mcp-server-git@latest` - Git 操作
- `uvx mcp-server-sqlite@latest` - SQLite 数据库

### 2. SSE 传输

适用于需要实时流式数据的场景。

```json
{
  "realtime-data": {
    "transport": "sse",
    "url": "http://localhost:3001/sse",
    "env": {},
    "disabled": false,
    "timeout": 60
  }
}
```

### 3. streamable-http 传输

适用于标准的 HTTP API 服务。

```json
{
  "api-service": {
    "transport": "streamable-http",
    "url": "http://localhost:3002/api",
    "env": {},
    "disabled": false,
    "timeout": 30
  }
}
```

## 使用方法

### 1. 通过界面管理

1. 启动应用程序
2. 进入 **设置 -> MCP服务**
3. 点击 **添加服务器**
4. 选择传输方式并填写配置
5. 保存并启动服务器

### 2. 手动配置

直接编辑 `.kiro/settings/mcp.json` 文件，然后在界面中点击 **重新加载** 按钮。

### 3. API 调用

```javascript
// 获取可用工具
const tools = await api.get('/api/mcp/tools');

// 调用工具
const result = await api.post('/api/mcp/tool/call', {
  tool_name: 'read_file',
  parameters: { path: 'README.md' }
});
```

## 开发自定义 MCP 服务器

### HTTP 服务器示例

参考 `examples/mcp_http_server.py`，这是一个完整的 HTTP MCP 服务器示例。

启动示例服务器：
```bash
cd examples
python mcp_http_server.py --port 3001
```

### 必需端点

HTTP/SSE 服务器必须实现以下端点：

1. **健康检查**: `GET /health`
2. **消息处理**: `POST /api/message` (HTTP) 或 `POST /sse/message` (SSE)

### MCP 协议

所有请求和响应都使用 JSON-RPC 2.0 格式：

```json
// 请求
{
  "jsonrpc": "2.0",
  "id": "request-id",
  "method": "tools/list",
  "params": {}
}

// 响应
{
  "jsonrpc": "2.0",
  "id": "request-id",
  "result": {
    "tools": [...]
  }
}
```

## 故障排除

### 常见问题

1. **stdio 服务器启动失败**
   - 检查 `uvx` 是否已安装
   - 验证命令和参数是否正确
   - 查看错误日志

2. **HTTP 服务器连接失败**
   - 确认服务器正在运行
   - 检查 URL 是否正确
   - 验证网络连接

3. **工具调用失败**
   - 检查工具名称是否正确
   - 验证参数格式
   - 查看服务器日志

### 日志查看

后端日志会显示详细的 MCP 操作信息，包括：
- 服务器启动/停止
- 工具发现
- 工具调用
- 错误信息

## 最佳实践

1. **选择合适的传输方式**
   - 本地工具使用 stdio
   - 远程服务使用 HTTP
   - 实时数据使用 SSE

2. **配置自动批准**
   - 对于安全的只读操作，可以配置自动批准
   - 避免对写操作自动批准

3. **设置合理的超时**
   - 根据工具的执行时间设置超时
   - 网络服务建议设置较长超时

4. **监控服务器状态**
   - 定期检查服务器状态
   - 及时处理错误和重启失败的服务器

## 扩展开发

### 添加新的传输方式

1. 在 `MCPTransportType` 枚举中添加新类型
2. 创建对应的连接类继承 `MCPConnection`
3. 在 `MCPClient._create_connection` 中添加处理逻辑
4. 更新前端界面支持新的传输方式

### 集成到 Agent 系统

MCP 工具会自动集成到 Agent 系统中，可以通过以下方式使用：

```python
# 获取可用工具
tools = await mcp_integration.get_available_tools_for_agent()

# 在 Agent 中调用工具
result = await mcp_integration.call_mcp_tool_for_agent(tool_name, parameters)
```