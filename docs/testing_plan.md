# Testing Plan for Automatic Imbalance Correction Mechanism

## Overview
This document outlines the testing plan for the automatic imbalance correction mechanism implemented in the accounting agent system.

## Test Cases

### Test Case 1: Successful Voucher Generation on First Attempt
**Objective**: Verify that the system works correctly when a voucher is balanced on the first attempt.

**Steps**:
1. Send a transaction request that should generate a balanced voucher
2. Monitor the system logs for retry attempts
3. Verify that only one attempt is made

**Expected Results**:
- Voucher is generated successfully
- No retry attempts are logged
- Validation shows the voucher is balanced

### Test Case 2: Successful Correction After One Retry
**Objective**: Verify that the system can correct an imbalance with one retry attempt.

**Steps**:
1. Send a transaction request that initially generates an unbalanced voucher
2. Configure the LLM to provide adjustment suggestions that would lead to a balanced voucher on retry
3. Monitor the system logs for retry attempts
4. Verify that the second attempt generates a balanced voucher

**Expected Results**:
- Two attempts are logged (initial + 1 retry)
- Voucher is generated successfully on the second attempt
- Validation shows the voucher is balanced
- Retry information is included in the response

### Test Case 3: Successful Correction After Two Retries
**Objective**: Verify that the system can correct an imbalance with two retry attempts.

**Steps**:
1. Send a transaction request that initially generates an unbalanced voucher
2. Configure the LLM to provide adjustment suggestions that would lead to a balanced voucher only on the third attempt
3. Monitor the system logs for retry attempts
4. Verify that the third attempt generates a balanced voucher

**Expected Results**:
- Three attempts are logged (initial + 2 retries)
- Voucher is generated successfully on the third attempt
- Validation shows the voucher is balanced
- Retry information is included in the response

### Test Case 4: Failed Correction After Maximum Retries
**Objective**: Verify that the system handles cases where all retry attempts fail.

**Steps**:
1. Send a transaction request that generates an unbalanced voucher
2. Configure the LLM to consistently generate unbalanced vouchers even with adjustment suggestions
3. Monitor the system logs for retry attempts
4. Verify that the system stops after 3 attempts

**Expected Results**:
- Three attempts are logged (initial + 2 retries)
- System outputs the final result even though it's unbalanced
- Validation shows the voucher is unbalanced
- Retry information is included in the response with all attempts

### Test Case 5: Stream Message Functionality
**Objective**: Verify that the streaming version of the functionality works correctly.

**Steps**:
1. Send a transaction request using the streaming API
2. Monitor the streamed responses
3. Verify that retry information is properly streamed

**Expected Results**:
- Streamed responses include retry information
- Final response includes complete validation and retry data

## Test Data

### Balanced Transaction Example
```
"今天收到客户支付的货款5000元，已存入银行账户。"
```

### Unbalanced Transaction Example
```
"购买办公用品1200元，用现金支付。"
```
(This might initially generate unbalanced entries if the system doesn't properly match subjects)

## Monitoring Points

1. **Log Analysis**: Check logs for retry attempt messages
2. **Performance Impact**: Measure response times with and without retries
3. **LLM Behavior**: Verify that adjustment suggestions are properly passed to subsequent attempts
4. **Memory Usage**: Ensure that previous attempt data doesn't cause memory issues

## Success Criteria

1. All test cases pass as described
2. System handles edge cases gracefully
3. Performance impact is minimal
4. Retry mechanism works consistently across different types of imbalances
5. User-facing responses include appropriate retry information

## Rollback Plan

If issues are discovered during testing:
1. Revert changes to AgentExecutor class
2. Restore original voucher generation prompt
3. Remove retry-related documentation updates
4. Investigate and fix issues before re-implementing