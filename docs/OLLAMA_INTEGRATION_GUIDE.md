# Ollama 本地大模型服务集成指南

## 概述

本系统现已支持 Ollama 本地大模型服务，允许您在本地运行大型语言模型，无需依赖云服务提供商。这提供了更好的隐私保护、更快的响应速度和更低的成本。

## Ollama 简介

Ollama 是一个开源工具，用于在本地运行大型语言模型。它支持多种模型，如 Llama、Mistral、Gemma 等，并提供了简单的命令行界面和 API。

## 系统要求

- 操作系统：Linux、macOS 或 Windows（通过 WSL2）
- 内存：至少 8GB RAM（推荐 16GB 或更多）
- 存储：至少 10GB 可用空间
- CPU：支持 AVX2 指令集的现代处理器（推荐 GPU 加速）

## 安装 Ollama

### Linux 和 macOS

1. 访问 [Ollama 官网](https://ollama.com/) 下载安装程序
2. 或使用以下命令安装：
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ```

### Windows

1. 安装 [WSL2](https://learn.microsoft.com/en-us/windows/wsl/install)
2. 在 WSL2 中安装 Ollama：
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ```

## 下载模型

安装 Ollama 后，您需要下载一个模型。以下是一些推荐的模型：

```bash
# 下载 Llama 3.1 8B 模型（推荐）
ollama pull llama3.1:8b

# 下载 Mistral 7B 模型（轻量级）
ollama pull mistral

# 下载 Gemma 2B 模型（超轻量级）
ollama pull gemma:2b
```

## 启动 Ollama 服务

Ollama 安装后会自动启动服务。您可以使用以下命令检查服务状态：

```bash
# 检查 Ollama 服务状态
ollama ps

# 手动启动服务
ollama serve
```

默认情况下，Ollama 服务运行在 `http://localhost:11434`。

## 系统配置

### 环境变量配置

在项目根目录创建或编辑 `.env` 文件，添加以下配置：

```env
# 启用 Ollama
OLLAMA_ENABLED=true

# Ollama 服务地址（默认）
OLLAMA_BASE_URL=http://localhost:11434

# 使用的模型名称
OLLAMA_MODEL=llama3.1:8b
```

### 前端界面配置

1. 打开应用，进入"AI设置中心"
2. 在"AI服务器设置"选项卡中，找到"使用本地大模型服务 (Ollama)"开关
3. 启用该开关
4. 根据需要配置 Ollama 服务地址和模型名称
5. 点击"测试连接"验证配置是否正确
6. 点击"保存设置"

## 测试集成

运行提供的测试脚本验证 Ollama 集成：

```bash
# 在项目根目录运行
python test_ollama_integration.py
```

测试脚本会验证：
- 基本 Ollama 连接
- 流式响应功能

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 Ollama 服务是否正在运行：`ollama ps`
   - 验证服务地址是否正确：`http://localhost:11434`
   - 检查防火墙设置

2. **模型未找到**
   - 确认模型已正确下载：`ollama list`
   - 检查模型名称拼写是否正确

3. **内存不足**
   - 尝试使用较小的模型（如 `gemma:2b`）
   - 关闭其他内存密集型应用程序

4. **响应缓慢**
   - 确保有足够的系统资源
   - 考虑使用 GPU 加速（如果支持）

### 日志检查

查看应用日志以获取更多信息：

```bash
# 查看后端日志
tail -f logs/backend.log

# 查看 Ollama 服务日志
journalctl -u ollama -f
```

## 性能优化

### 硬件加速

如果您的系统支持 GPU 加速，可以显著提高性能：

1. 安装适当的 GPU 驱动程序
2. 安装支持 GPU 的 Ollama 版本
3. 在运行模型时，Ollama 会自动使用 GPU

### 模型选择

根据您的硬件配置选择合适的模型：

- **低配置设备**（<8GB RAM）：`gemma:2b`、`qwen:1.8b`
- **中等配置设备**（8-16GB RAM）：`mistral`、`llama3:8b`
- **高配置设备**（>16GB RAM）：`llama3.1:8b`、`llama3:70b`

## 安全注意事项

1. **本地运行**：Ollama 在本地运行，数据不会发送到外部服务器
2. **模型来源**：仅从官方源下载模型，避免使用未知来源的模型
3. **端口安全**：如果需要在网络中访问 Ollama，请配置适当的防火墙规则
4. **权限管理**：确保只有授权用户可以访问 Ollama 服务

## 高级配置

### 自定义模型参数

您可以通过修改 Ollama 配置文件来自定义模型参数：

```bash
# 创建自定义模型文件
cat > Modelfile << EOF
FROM llama3.1:8b
PARAMETER temperature 0.2
PARAMETER top_p 0.8
SYSTEM "你是一个专业的会计助手..."
EOF

# 构建自定义模型
ollama create my-accounting-model -f Modelfile
```

### API 直接调用

您也可以直接使用 Ollama API：

```bash
curl -X POST http://localhost:11434/api/generate -d '{
  "model": "llama3.1:8b",
  "prompt": "你好",
  "stream": false
}'
```

## 更新和维护

### 更新 Ollama

```bash
# 更新 Ollama
ollama update

# 重新安装最新版本
curl -fsSL https://ollama.com/install.sh | sh
```

### 更新模型

```bash
# 更新特定模型
ollama pull llama3.1:8b

# 删除旧模型
ollama rm old-model-name
```

## 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 提交新的 Issue 描述您的问题

---

*最后更新：2025年8月*