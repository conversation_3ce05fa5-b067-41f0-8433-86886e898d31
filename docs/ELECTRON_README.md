# 会计助手桌面应用

基于 Electron 的跨平台桌面版本，提供类似 Cherry Studio 的现代化界面设计。

## 功能特性

- 🖥️ **跨平台支持**: Windows、macOS、Linux
- 🎨 **Headless 模式**: 使用系统原生标题栏，简洁高效
- 📱 **响应式布局**: 适配不同屏幕尺寸
- ⚡ **高性能**: 基于 Chromium 内核
- 🔄 **热更新**: 开发模式支持实时更新
- 🔧 **原生体验**: 完全融入系统环境

## 界面设计

### Cherry Studio 风格
- **左侧边栏**: 浅灰色背景，助手/功能切换
- **顶部状态栏**: 显示当前模型和连接状态
- **主内容区**: 白色背景，聊天或功能界面
- **现代化设计**: 圆角、阴影、渐变等现代元素

### 功能布局
- **助手标签**: 显示会话列表，支持新建对话
- **功能标签**: 显示所有业务功能模块
- **设置标签**: 应用配置和偏好设置
- **搜索功能**: 快速查找助手或功能

## 快速开始

### 1. 安装依赖

```bash
# 安装 Electron 和相关依赖
yarn install

# 安装前端依赖
cd frontend && yarn install && cd ..
```

### 2. 开发模式

```bash
# 启动桌面应用（开发模式）
./start.sh electron
```

这将会：
1. 启动后端服务 (http://localhost:8000)
2. 启动前端开发服务器 (http://localhost:5173)
3. 启动 Electron 桌面应用

### 3. 构建安装包

```bash
# 构建生产版本
./start.sh build
```

构建完成后，安装包将位于 `./dist/` 目录中：
- **Windows**: `.exe` 安装程序
- **macOS**: `.dmg` 磁盘映像
- **Linux**: `.AppImage` 可执行文件

## 启动命令说明

```bash
# Web 版本（浏览器访问）
./start.sh start

# 桌面版本（Electron 应用）
./start.sh electron

# 构建桌面应用安装包
./start.sh build

# 显示帮助信息
./start.sh help
```

## 技术栈

- **Electron**: 跨平台桌面应用框架
- **React**: 前端 UI 框架
- **Vite**: 前端构建工具
- **Tailwind CSS**: 样式框架
- **FastAPI**: 后端 API 服务
- **Yarn**: 包管理器

## 开发说明

### 项目结构

```
├── electron/                 # Electron 主进程文件
│   ├── main.js              # 主进程入口
│   ├── preload.js           # 预加载脚本
│   └── assets/              # 应用资源
├── frontend/                # 前端源码
│   ├── src/
│   │   ├── components/
│   │   │   ├── TitleBar.jsx      # 自定义标题栏
│   │   │   └── ElectronLayout.jsx # Electron 布局
│   │   └── App.jsx          # 主应用组件
│   └── dist/                # 构建输出
├── package.json             # Electron 配置
└── start.sh                 # 启动脚本
```

### 环境检测

应用会自动检测运行环境：
- **Electron 环境**: 显示自定义标题栏和桌面布局
- **Web 环境**: 显示原有的 Web 布局

### 窗口控制

- **拖拽区域**: 标题栏支持窗口拖拽
- **窗口按钮**: 
  - macOS: 红黄绿圆形按钮
  - Windows/Linux: 最小化、最大化、关闭按钮

## 自定义配置

### 窗口设置

在 `electron/main.js` 中可以修改：
- 窗口大小和最小尺寸
- 标题栏样式
- 应用图标
- 菜单配置

### 样式定制

在 `frontend/src/components/` 中可以修改：
- 标题栏样式
- 侧边栏布局
- 主题颜色

## 常见问题

### Q: 如何更换应用图标？
A: 替换 `electron/assets/icon.png` 文件，建议使用 256x256 像素的 PNG 格式。

### Q: 如何修改窗口标题？
A: 在 `TitleBar.jsx` 组件中修改 `title` 属性。

### Q: 如何添加新的菜单项？
A: 在 `electron/main.js` 的 `createMenu()` 函数中添加菜单配置。

### Q: 构建失败怎么办？
A: 确保已安装所有依赖，并检查 Node.js 版本是否兼容（推荐 16+）。

## 许可证

与主项目保持一致的许可证。