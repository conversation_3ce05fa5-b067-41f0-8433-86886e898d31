#!/usr/bin/env python3
"""
简单的 MCP HTTP 服务器示例
演示如何创建支持 streamable-http 和 SSE 传输的 MCP 服务器
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import json
import asyncio
import uvicorn
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="MCP HTTP Server Example", version="1.0.0")

class MCPRequest(BaseModel):
    jsonrpc: str = "2.0"
    id: str
    method: str
    params: Optional[Dict[str, Any]] = None

class MCPResponse(BaseModel):
    jsonrpc: str = "2.0"
    id: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

# 模拟工具
AVAILABLE_TOOLS = [
    {
        "name": "echo",
        "description": "回显输入的文本",
        "inputSchema": {
            "type": "object",
            "properties": {
                "text": {
                    "type": "string",
                    "description": "要回显的文本"
                }
            },
            "required": ["text"]
        }
    },
    {
        "name": "calculate",
        "description": "执行简单的数学计算",
        "inputSchema": {
            "type": "object",
            "properties": {
                "expression": {
                    "type": "string",
                    "description": "数学表达式，如 '2 + 3'"
                }
            },
            "required": ["expression"]
        }
    }
]

def handle_mcp_request(request: MCPRequest) -> MCPResponse:
    """处理 MCP 请求"""
    try:
        if request.method == "tools/list":
            return MCPResponse(
                id=request.id,
                result={"tools": AVAILABLE_TOOLS}
            )
        
        elif request.method == "tools/call":
            tool_name = request.params.get("name")
            arguments = request.params.get("arguments", {})
            
            if tool_name == "echo":
                text = arguments.get("text", "")
                return MCPResponse(
                    id=request.id,
                    result={
                        "content": [
                            {
                                "type": "text",
                                "text": f"Echo: {text}"
                            }
                        ]
                    }
                )
            
            elif tool_name == "calculate":
                expression = arguments.get("expression", "")
                try:
                    # 简单的计算（生产环境中应该使用更安全的方法）
                    result = eval(expression)
                    return MCPResponse(
                        id=request.id,
                        result={
                            "content": [
                                {
                                    "type": "text",
                                    "text": f"{expression} = {result}"
                                }
                            ]
                        }
                    )
                except Exception as e:
                    return MCPResponse(
                        id=request.id,
                        error={
                            "code": -32000,
                            "message": f"计算错误: {str(e)}"
                        }
                    )
            
            else:
                return MCPResponse(
                    id=request.id,
                    error={
                        "code": -32601,
                        "message": f"未知工具: {tool_name}"
                    }
                )
        
        else:
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32601,
                    "message": f"未知方法: {request.method}"
                }
            )
    
    except Exception as e:
        logger.error(f"处理请求失败: {e}")
        return MCPResponse(
            id=request.id,
            error={
                "code": -32603,
                "message": f"内部错误: {str(e)}"
            }
        )

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok", "transport": ["streamable-http", "sse"]}

@app.post("/api/message")
async def handle_http_message(request: MCPRequest):
    """处理 streamable-http 消息"""
    logger.info(f"收到 HTTP 请求: {request.method}")
    response = handle_mcp_request(request)
    return response.dict()

@app.post("/sse/message")
async def handle_sse_message(request: MCPRequest):
    """处理 SSE 消息"""
    logger.info(f"收到 SSE 请求: {request.method}")
    
    async def generate_sse():
        response = handle_mcp_request(request)
        yield f"data: {json.dumps(response.dict())}\n\n"
    
    return StreamingResponse(
        generate_sse(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@app.get("/")
async def root():
    """根端点"""
    return {
        "name": "MCP HTTP Server Example",
        "version": "1.0.0",
        "transports": ["streamable-http", "sse"],
        "endpoints": {
            "health": "/health",
            "http": "/api/message",
            "sse": "/sse/message"
        },
        "tools": len(AVAILABLE_TOOLS)
    }

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP HTTP Server Example")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=3001, help="Port to bind to")
    parser.add_argument("--log-level", default="info", help="Log level")
    
    args = parser.parse_args()
    
    print(f"启动 MCP HTTP 服务器...")
    print(f"地址: http://{args.host}:{args.port}")
    print(f"Streamable HTTP: http://{args.host}:{args.port}/api/message")
    print(f"SSE: http://{args.host}:{args.port}/sse/message")
    print(f"健康检查: http://{args.host}:{args.port}/health")
    
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level=args.log_level
    )