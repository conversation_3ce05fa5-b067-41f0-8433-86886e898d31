# Agent-to-Agent (A2A) 和 Agent-GUI (AG-UI) 协议演示

这个演示展示了几个成熟的 A2A 和 AG-UI 协议框架的使用。

## 框架介绍

### A2A 协议框架
1. **SPADE** - Python 多智能体系统平台
2. **Mesa** - 基于智能体的建模框架
3. **自定义 WebSocket 协议** - 轻量级实时通信

### AG-UI 协议框架
1. **Gradio** - 机器学习界面
2. **Streamlit** - 数据科学应用界面
3. **FastAPI + WebSocket** - 自定义 Web 界面

## 运行演示

```bash
# 安装依赖
pip install -r requirements.txt

# 运行 SPADE 多智能体演示
python spade_demo.py

# 运行 Mesa 智能体建模演示
python mesa_demo.py

# 运行 Gradio 界面演示
python gradio_demo.py

# 运行 Streamlit 界面演示
streamlit run streamlit_demo.py

# 运行自定义 WebSocket 演示
python websocket_demo.py
```

## 文件结构

- `spade_demo.py` - SPADE 多智能体通信演示
- `mesa_demo.py` - Mesa 智能体建模演示
- `gradio_demo.py` - Gradio 界面演示
- `streamlit_demo.py` - Streamlit 界面演示
- `websocket_demo.py` - 自定义 WebSocket 协议演示
- `requirements.txt` - 依赖包列表