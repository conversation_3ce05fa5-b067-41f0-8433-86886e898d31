# Electron 多智能体文档分析助手

## 应用场景

这是一个基于 Electron + 多智能体架构的智能文档分析助手，支持：

- 📄 **文档上传与解析**: 支持 PDF、Word、TXT 等格式
- 🤖 **多智能体协作**: 文档解析、内容分析、摘要生成、问答等
- 💬 **实时对话**: 与智能体进行自然语言交互
- 🔧 **配置管理**: OpenAI API 配置和智能体参数调整
- 📊 **可视化展示**: 分析结果的图表和统计信息

## 架构设计

### 前端 (Electron + AG-UI 协议)
- **主界面**: 文档上传、对话窗口、配置面板
- **AG-UI 协议**: 前端与后端智能体的通信协议
- **实时更新**: WebSocket 连接实现实时状态更新

### 后端 (多智能体 + A2A 协议)
- **文档解析智能体**: 处理文档上传和内容提取
- **分析智能体**: 执行文本分析和语义理解
- **对话智能体**: 处理用户问答和交互
- **协调智能体**: 管理任务分配和结果整合
- **A2A 协议**: 智能体间的通信和协作

## 技术栈

- **前端**: Electron, HTML5, CSS3, JavaScript
- **后端**: Python, FastAPI, WebSocket
- **AI 服务**: OpenAI GPT API
- **协议**: A2A (Agent-to-Agent), AG-UI (Agent-GUI)

## 安装和运行

### 1. 安装依赖

```bash
# 后端依赖
cd backend
pip install -r requirements.txt

# 前端依赖
cd ../frontend
npm install
```

### 2. 配置 OpenAI API

在前端界面中配置你的 OpenAI API Key，或者在 `backend/config.json` 中设置。

### 3. 启动应用

```bash
# 启动后端服务
cd backend
python main.py

# 启动 Electron 应用
cd ../frontend
npm start
```

## 使用说明

1. **配置 API**: 在设置面板中输入 OpenAI API Key
2. **上传文档**: 拖拽或选择文件上传
3. **开始分析**: 智能体自动协作分析文档
4. **交互对话**: 在对话框中询问文档相关问题
5. **查看结果**: 浏览分析报告和可视化结果

## 文件结构

```
electron_multiagent_app/
├── README.md
├── backend/                 # Python 后端
│   ├── main.py             # FastAPI 主服务
│   ├── agents/             # 智能体模块
│   ├── protocols/          # A2A 协议实现
│   ├── services/           # 业务服务
│   └── requirements.txt    # Python 依赖
└── frontend/               # Electron 前端
    ├── package.json        # Node.js 依赖
    ├── main.js            # Electron 主进程
    ├── renderer/          # 渲染进程
    └── assets/            # 静态资源
```