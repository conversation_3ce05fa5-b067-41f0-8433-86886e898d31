# 多智能体文档分析助手演示指南

## 🎯 项目概述

这是一个基于 **Electron** 前端和**多智能体**后端架构的智能文档分析助手，展示了 **A2A (Agent-to-Agent)** 和 **AG-UI (Agent-GUI)** 协议的实际应用。

### 核心特性

- 📄 **智能文档解析**: 支持 PDF、DOCX、TXT 等格式
- 🤖 **多智能体协作**: 文档解析、内容分析、摘要生成、智能对话
- 💬 **实时对话界面**: 与智能体进行自然语言交互
- 🔄 **工作流管理**: 可视化的智能体协作流程
- ⚙️ **灵活配置**: 支持 OpenAI API 集成和参数调整

### 技术架构

```
┌─────────────────┐    AG-UI 协议    ┌─────────────────┐
│   Electron      │ ◄──────────────► │   FastAPI       │
│   前端界面      │                  │   后端服务      │
└─────────────────┘                  └─────────────────┘
                                              │
                                     A2A 协议 │
                                              ▼
                                    ┌─────────────────┐
                                    │   多智能体系统   │
                                    │ ┌─────────────┐ │
                                    │ │文档解析智能体│ │
                                    │ └─────────────┘ │
                                    │ ┌─────────────┐ │
                                    │ │内容分析智能体│ │
                                    │ └─────────────┘ │
                                    │ ┌─────────────┐ │
                                    │ │对话智能体   │ │
                                    │ └─────────────┘ │
                                    │ ┌─────────────┐ │
                                    │ │摘要生成智能体│ │
                                    │ └─────────────┘ │
                                    │ ┌─────────────┐ │
                                    │ │协调智能体   │ │
                                    │ └─────────────┘ │
                                    └─────────────────┘
```

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 进入项目目录
cd examples/agent_protocols_demo/electron_multiagent_app

# 运行启动脚本（自动安装依赖并启动）
python start_demo.py
```

### 方法二：手动启动

#### 1. 安装依赖

```bash
# 后端依赖（最小化）
cd backend
pip install -r requirements_minimal.txt

# 完整功能依赖（可选）
pip install -r requirements.txt

# 前端依赖（如果要运行 Electron）
cd ../frontend
npm install
```

#### 2. 启动后端服务

```bash
cd backend
python main.py
```

#### 3. 启动前端（二选一）

**选项 A: Electron 应用**
```bash
cd frontend
npm start
```

**选项 B: Web 界面**
```bash
# 浏览器访问 http://localhost:8000
# 或运行 Web 演示
python -c "import webbrowser; webbrowser.open('http://localhost:8000')"
```

## 🧪 测试验证

### 基础功能测试

```bash
# 运行简化测试（无需外部依赖）
python simple_test.py

# 运行完整测试（需要安装完整依赖）
python test_backend.py
```

### 预期输出

```
🧪 多智能体后端简化测试
==================================================
🔍 测试基本导入...
✅ 基础智能体类导入成功
✅ AG-UI 协议导入成功
✅ OpenAI 服务导入成功
✅ 文档服务导入成功

🤖 测试简单智能体...
✅ 智能体状态: idle
✅ UI 命令结果: True
✅ 简单智能体测试完成

🔗 测试 AG-UI 协议...
✅ 协议响应: True
✅ 协议统计: 注册智能体 1 个
✅ AG-UI 协议测试完成

💬 测试智能体通信...
✅ 消息发送: True
✅ 消息接收: agent2 收到 2 条消息
✅ 智能体通信测试完成

==================================================
🎯 测试结果: 6/6 通过
🎉 所有基础功能测试通过！
```

## 📋 使用说明

### 1. 配置 OpenAI API（可选）

在界面中点击设置按钮，输入您的 OpenAI API Key 以启用 AI 功能：

- 智能对话
- 文档摘要生成
- 情感分析
- 内容洞察

### 2. 上传文档

- 拖拽文件到上传区域
- 或点击上传区域选择文件
- 支持格式：PDF、DOCX、DOC、TXT、MD

### 3. 智能分析

文档上传后，系统会自动启动多智能体协作流程：

1. **文档解析智能体** 提取文本内容
2. **内容分析智能体** 进行语义分析
3. **摘要生成智能体** 生成文档摘要
4. **协调智能体** 管理整个工作流

### 4. 交互对话

在对话界面中：

- 询问文档相关问题
- 请求详细分析
- 获取建议和洞察
- 查看分析结果

## 🔧 协议详解

### A2A (Agent-to-Agent) 协议

智能体间通信协议，支持：

- **消息传递**: 异步消息队列
- **协作请求**: 智能体间任务协作
- **状态同步**: 实时状态更新
- **工作流管理**: 复杂任务的分解和协调

**消息格式示例：**
```json
{
  "id": "msg_1234567890",
  "timestamp": "2024-01-01T12:00:00Z",
  "sender": "document_parser",
  "receiver": "content_analyzer",
  "message_type": "parse_complete",
  "content": {
    "document_id": "doc_123",
    "parsed_content": "文档内容...",
    "metadata": {...}
  }
}
```

### AG-UI (Agent-GUI) 协议

前端与智能体通信协议，支持：

- **命令发送**: UI 向智能体发送指令
- **状态查询**: 获取智能体实时状态
- **结果接收**: 接收智能体处理结果
- **配置管理**: 动态配置智能体参数

**命令格式示例：**
```json
{
  "id": "cmd_1234567890",
  "timestamp": "2024-01-01T12:00:00Z",
  "command": "analyze_document",
  "parameters": {
    "document_id": "doc_123",
    "analysis_type": "comprehensive"
  },
  "agent_id": "content_analyzer",
  "client_id": "electron_client"
}
```

## 🏗️ 项目结构

```
electron_multiagent_app/
├── README.md                    # 项目说明
├── DEMO_GUIDE.md               # 演示指南
├── start_demo.py               # 一键启动脚本
├── simple_test.py              # 简化测试脚本
├── test_backend.py             # 完整测试脚本
├── backend/                    # Python 后端
│   ├── main.py                # FastAPI 主服务
│   ├── requirements.txt       # 完整依赖
│   ├── requirements_minimal.txt # 最小依赖
│   ├── agents/                # 智能体模块
│   │   ├── base_agent.py      # 基础智能体类
│   │   ├── agent_manager.py   # 智能体管理器
│   │   ├── document_parser_agent.py    # 文档解析
│   │   ├── content_analyzer_agent.py   # 内容分析
│   │   ├── chat_agent.py      # 对话智能体
│   │   └── summary_generator_agent.py  # 摘要生成
│   ├── protocols/             # 协议实现
│   │   └── ag_ui_protocol.py  # AG-UI 协议
│   └── services/              # 业务服务
│       ├── openai_service.py  # OpenAI 集成
│       └── document_service.py # 文档管理
└── frontend/                  # Electron 前端
    ├── package.json           # Node.js 依赖
    ├── main.js               # Electron 主进程
    └── renderer/             # 渲染进程
        ├── index.html        # 主界面
        ├── styles/           # 样式文件
        └── js/               # JavaScript 模块
```

## 🎨 界面预览

### 主界面
- 左侧：文档上传、文档列表、智能体状态
- 中央：对话界面、分析结果、工作流视图
- 顶部：连接状态、配置按钮
- 底部：状态栏、系统信息

### 功能标签页
1. **💬 对话**: 与智能体实时对话
2. **📊 分析结果**: 查看文档分析报告
3. **🔄 工作流**: 监控智能体协作过程

## 🔍 故障排除

### 常见问题

**Q: 导入模块失败**
```bash
# 确保在正确目录运行
cd examples/agent_protocols_demo/electron_multiagent_app

# 安装最小依赖
pip install -r backend/requirements_minimal.txt
```

**Q: WebSocket 连接失败**
```bash
# 检查后端服务是否启动
curl http://localhost:8000/api/agents/status

# 检查端口是否被占用
netstat -an | grep 8000
```

**Q: Electron 应用无法启动**
```bash
# 检查 Node.js 版本
node --version

# 重新安装依赖
cd frontend
rm -rf node_modules
npm install
```

### 日志查看

- 后端日志：控制台输出
- 前端日志：Electron 开发者工具 (F12)
- WebSocket 消息：浏览器网络面板

## 🚀 扩展开发

### 添加新智能体

1. 继承 `BaseAgent` 类
2. 实现 `process_message` 和 `execute_ui_command` 方法
3. 在 `AgentManager` 中注册
4. 更新前端界面显示

### 自定义协议

1. 扩展 `AGUIProtocol` 类
2. 添加新的消息类型
3. 实现对应的处理逻辑
4. 更新前端通信模块

### 集成新服务

1. 在 `services/` 目录添加服务类
2. 在智能体中引用服务
3. 更新配置界面
4. 添加相应的测试

## 📚 相关资源

- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Electron 文档](https://www.electronjs.org/docs)
- [OpenAI API 文档](https://platform.openai.com/docs)
- [WebSocket 协议](https://tools.ietf.org/html/rfc6455)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**🎉 恭喜！您已成功运行多智能体文档分析助手演示！**

这个演示展示了现代多智能体系统的核心概念和实现方式，包括 A2A 和 AG-UI 协议的实际应用。您可以基于这个框架开发更复杂的智能体应用。