#!/usr/bin/env python3
"""
简化的后端功能测试脚本 - 不依赖外部库
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加后端路径到 Python 路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))


async def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        from agents.base_agent import BaseAgent, Message, AgentStatus
        print("✅ 基础智能体类导入成功")
        
        from protocols.ag_ui_protocol import AGUIProtocol
        print("✅ AG-UI 协议导入成功")
        
        from services.openai_service import OpenAIService
        print("✅ OpenAI 服务导入成功")
        
        from services.document_service import DocumentService
        print("✅ 文档服务导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


async def test_simple_agent():
    """测试简单智能体"""
    print("\n🤖 测试简单智能体...")
    
    try:
        from agents.base_agent import BaseAgent, Message, AgentStatus
        
        # 创建一个简单的测试智能体
        class TestAgent(BaseAgent):
            async def process_message(self, message):
                print(f"处理消息: {message.message_type}")
            
            async def execute_ui_command(self, command, parameters):
                return {"result": f"执行命令: {command}"}
        
        # 创建并启动智能体
        agent = TestAgent("test_agent", "test_type")
        await agent.start()
        
        # 测试状态
        status = agent.get_status()
        print(f"✅ 智能体状态: {status['status']}")
        
        # 测试 UI 命令
        result = await agent.handle_ui_command("test_command", {})
        print(f"✅ UI 命令结果: {result['success']}")
        
        await agent.stop()
        print("✅ 简单智能体测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 简单智能体测试失败: {e}")
        return False


async def test_ag_ui_protocol():
    """测试 AG-UI 协议"""
    print("\n🔗 测试 AG-UI 协议...")
    
    try:
        from protocols.ag_ui_protocol import AGUIProtocol
        from agents.base_agent import BaseAgent
        
        # 创建协议实例
        protocol = AGUIProtocol()
        
        # 创建测试智能体
        class SimpleAgent(BaseAgent):
            async def process_message(self, message):
                pass
            
            async def execute_ui_command(self, command, parameters):
                return f"执行了命令: {command}"
        
        agent = SimpleAgent("test_agent", "test")
        await agent.start()
        
        # 注册智能体
        protocol.register_agent("test_agent", agent)
        
        # 测试命令发送
        response = await protocol.send_command_to_agent("test_agent", "ping")
        print(f"✅ 协议响应: {response['success']}")
        
        # 测试协议统计
        stats = protocol.get_protocol_stats()
        print(f"✅ 协议统计: 注册智能体 {stats['registered_agents']} 个")
        
        await agent.stop()
        print("✅ AG-UI 协议测试完成")
        return True
        
    except Exception as e:
        print(f"❌ AG-UI 协议测试失败: {e}")
        return False


def test_openai_service():
    """测试 OpenAI 服务"""
    print("\n🧠 测试 OpenAI 服务...")
    
    try:
        from services.openai_service import OpenAIService
        
        service = OpenAIService()
        
        # 测试配置
        config = service.get_config()
        print(f"✅ OpenAI 配置状态: {'已配置' if config['is_configured'] else '未配置'}")
        
        # 测试设置
        service.set_model("gpt-3.5-turbo")
        service.set_parameters(max_tokens=1000, temperature=0.5)
        
        updated_config = service.get_config()
        print(f"✅ 模型设置: {updated_config['model']}")
        print(f"✅ 参数设置: tokens={updated_config['max_tokens']}, temp={updated_config['temperature']}")
        
        print("✅ OpenAI 服务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI 服务测试失败: {e}")
        return False


def test_document_service():
    """测试文档服务"""
    print("\n📄 测试文档服务...")
    
    try:
        from services.document_service import DocumentService
        
        service = DocumentService()
        
        # 测试统计
        stats = service.get_document_stats()
        print(f"✅ 文档统计: {stats['total_documents']} 个文档")
        
        # 测试文档ID生成
        doc_id = service.generate_document_id("test.txt")
        print(f"✅ 文档ID生成: {doc_id}")
        
        # 测试搜索
        results = service.search_documents("test")
        print(f"✅ 搜索功能: 找到 {len(results)} 个结果")
        
        print("✅ 文档服务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文档服务测试失败: {e}")
        return False


async def test_agent_communication():
    """测试智能体通信"""
    print("\n💬 测试智能体通信...")
    
    try:
        from agents.base_agent import BaseAgent, Message
        
        class CommunicatingAgent(BaseAgent):
            def __init__(self, agent_id, agent_type):
                super().__init__(agent_id, agent_type)
                self.received_messages = []
            
            async def process_message(self, message):
                self.received_messages.append(message)
                print(f"[{self.id}] 收到消息: {message.message_type}")
            
            async def execute_ui_command(self, command, parameters):
                return f"[{self.id}] 执行命令: {command}"
        
        # 创建两个智能体
        agent1 = CommunicatingAgent("agent1", "sender")
        agent2 = CommunicatingAgent("agent2", "receiver")
        
        await agent1.start()
        await agent2.start()
        
        # 设置注册表
        registry = {"agent1": agent1, "agent2": agent2}
        agent1.set_agent_registry(registry)
        agent2.set_agent_registry(registry)
        
        # 发送消息
        success = await agent1.send_message("agent2", "test_message", "Hello!")
        print(f"✅ 消息发送: {success}")
        
        # 等待消息处理
        await asyncio.sleep(0.1)
        
        # 检查接收
        print(f"✅ 消息接收: agent2 收到 {len(agent2.received_messages)} 条消息")
        
        await agent1.stop()
        await agent2.stop()
        
        print("✅ 智能体通信测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 智能体通信测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 多智能体后端简化测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_basic_imports(),
        test_simple_agent(),
        test_ag_ui_protocol(),
        test_openai_service(),
        test_document_service(),
        test_agent_communication()
    ]
    
    results = []
    for test in tests:
        if asyncio.iscoroutine(test):
            result = await test
        else:
            result = test
        results.append(result)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("\n💡 下一步:")
        print("1. 安装完整依赖: pip install -r backend/requirements.txt")
        print("2. 运行完整测试: python test_backend.py")
        print("3. 启动演示: python start_demo.py")
    else:
        print("⚠️ 部分测试失败，请检查代码")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)