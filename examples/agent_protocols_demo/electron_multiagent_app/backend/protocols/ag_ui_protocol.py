"""
AG-UI 协议实现 - Agent-GUI 通信协议
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from agents.base_agent import BaseAgent


class AGUIMessage:
    """AG-UI 协议消息"""
    
    def __init__(self, command: str, parameters: Dict = None, agent_id: str = None, 
                 client_id: str = None, message_id: str = None):
        self.id = message_id or f"agui_{int(datetime.now().timestamp() * 1000)}"
        self.timestamp = datetime.now().isoformat()
        self.command = command
        self.parameters = parameters or {}
        self.agent_id = agent_id
        self.client_id = client_id
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "command": self.command,
            "parameters": self.parameters,
            "agent_id": self.agent_id,
            "client_id": self.client_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'AGUIMessage':
        """从字典创建消息对象"""
        return cls(
            command=data["command"],
            parameters=data.get("parameters", {}),
            agent_id=data.get("agent_id"),
            client_id=data.get("client_id"),
            message_id=data.get("id")
        )


class AGUIResponse:
    """AG-UI 协议响应"""
    
    def __init__(self, success: bool, data: Any = None, error: str = None, 
                 agent_id: str = None, request_id: str = None):
        self.id = f"resp_{int(datetime.now().timestamp() * 1000)}"
        self.timestamp = datetime.now().isoformat()
        self.success = success
        self.data = data
        self.error = error
        self.agent_id = agent_id
        self.request_id = request_id
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "agent_id": self.agent_id,
            "request_id": self.request_id
        }


class AGUIProtocol:
    """AG-UI 协议处理器"""
    
    def __init__(self):
        self.registered_agents: Dict[str, BaseAgent] = {}
        self.message_history: List[Dict] = []
        self.active_sessions: Dict[str, Dict] = {}  # 客户端会话
        self.command_handlers: Dict[str, callable] = {}
        
        # 注册默认命令处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认命令处理器"""
        self.command_handlers.update({
            "ping": self._handle_ping,
            "get_agent_status": self._handle_get_agent_status,
            "get_agent_capabilities": self._handle_get_agent_capabilities,
            "list_agents": self._handle_list_agents,
            "get_session_info": self._handle_get_session_info
        })
    
    def register_agent(self, agent_id: str, agent: BaseAgent):
        """注册智能体到 AG-UI 协议"""
        self.registered_agents[agent_id] = agent
        print(f"[AG-UI] 智能体 {agent_id} 已注册到 AG-UI 协议")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.registered_agents:
            del self.registered_agents[agent_id]
            print(f"[AG-UI] 智能体 {agent_id} 已从 AG-UI 协议注销")
    
    def register_command_handler(self, command: str, handler: callable):
        """注册命令处理器"""
        self.command_handlers[command] = handler
    
    async def send_command_to_agent(self, agent_id: str, command: str, 
                                   parameters: Dict = None, client_id: str = None) -> Dict:
        """发送命令到智能体"""
        try:
            # 创建 AG-UI 消息
            message = AGUIMessage(
                command=command,
                parameters=parameters or {},
                agent_id=agent_id,
                client_id=client_id
            )
            
            # 记录消息历史
            self._log_message(message, "ui_to_agent")
            
            # 检查是否有自定义处理器
            if command in self.command_handlers:
                result = await self.command_handlers[command](message)
                response = AGUIResponse(
                    success=True,
                    data=result,
                    agent_id=agent_id,
                    request_id=message.id
                )
            else:
                # 发送到指定智能体
                if agent_id not in self.registered_agents:
                    response = AGUIResponse(
                        success=False,
                        error=f"智能体 {agent_id} 未注册",
                        agent_id=agent_id,
                        request_id=message.id
                    )
                else:
                    agent = self.registered_agents[agent_id]
                    result = await agent.handle_ui_command(command, parameters or {})
                    
                    response = AGUIResponse(
                        success=result.get("success", True),
                        data=result.get("result") if result.get("success") else None,
                        error=result.get("error") if not result.get("success") else None,
                        agent_id=agent_id,
                        request_id=message.id
                    )
            
            # 记录响应
            self._log_response(response, "agent_to_ui")
            
            return response.to_dict()
            
        except Exception as e:
            error_response = AGUIResponse(
                success=False,
                error=f"命令执行失败: {str(e)}",
                agent_id=agent_id,
                request_id=message.id if 'message' in locals() else None
            )
            
            self._log_response(error_response, "agent_to_ui")
            return error_response.to_dict()
    
    async def broadcast_command(self, command: str, parameters: Dict = None, 
                               exclude_agents: List[str] = None) -> Dict[str, Dict]:
        """广播命令到所有智能体"""
        exclude_agents = exclude_agents or []
        results = {}
        
        tasks = []
        for agent_id in self.registered_agents:
            if agent_id not in exclude_agents:
                task = self.send_command_to_agent(agent_id, command, parameters)
                tasks.append((agent_id, task))
        
        # 并发执行所有命令
        for agent_id, task in tasks:
            try:
                result = await task
                results[agent_id] = result
            except Exception as e:
                results[agent_id] = {
                    "success": False,
                    "error": f"广播命令失败: {str(e)}",
                    "agent_id": agent_id
                }
        
        return results
    
    def create_session(self, client_id: str, session_data: Dict = None) -> Dict:
        """创建客户端会话"""
        session = {
            "client_id": client_id,
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "data": session_data or {},
            "message_count": 0
        }
        
        self.active_sessions[client_id] = session
        return session
    
    def update_session(self, client_id: str, data: Dict):
        """更新会话数据"""
        if client_id in self.active_sessions:
            self.active_sessions[client_id]["data"].update(data)
            self.active_sessions[client_id]["last_activity"] = datetime.now().isoformat()
    
    def get_session(self, client_id: str) -> Optional[Dict]:
        """获取会话信息"""
        return self.active_sessions.get(client_id)
    
    def remove_session(self, client_id: str):
        """移除会话"""
        if client_id in self.active_sessions:
            del self.active_sessions[client_id]
    
    def _log_message(self, message: AGUIMessage, direction: str):
        """记录消息"""
        log_entry = {
            "timestamp": message.timestamp,
            "direction": direction,
            "type": "command",
            "message": message.to_dict()
        }
        
        self.message_history.append(log_entry)
        
        # 限制历史记录长度
        if len(self.message_history) > 1000:
            self.message_history = self.message_history[-500:]
    
    def _log_response(self, response: AGUIResponse, direction: str):
        """记录响应"""
        log_entry = {
            "timestamp": response.timestamp,
            "direction": direction,
            "type": "response",
            "response": response.to_dict()
        }
        
        self.message_history.append(log_entry)
    
    # 默认命令处理器
    async def _handle_ping(self, message: AGUIMessage) -> Dict:
        """处理 ping 命令"""
        return {
            "pong": True,
            "timestamp": datetime.now().isoformat(),
            "message": "AG-UI 协议正常运行"
        }
    
    async def _handle_get_agent_status(self, message: AGUIMessage) -> Dict:
        """获取智能体状态"""
        agent_id = message.parameters.get("agent_id")
        
        if agent_id:
            if agent_id in self.registered_agents:
                agent = self.registered_agents[agent_id]
                return agent.get_status()
            else:
                raise ValueError(f"智能体 {agent_id} 未注册")
        else:
            # 返回所有智能体状态
            status = {}
            for aid, agent in self.registered_agents.items():
                status[aid] = agent.get_status()
            return status
    
    async def _handle_get_agent_capabilities(self, message: AGUIMessage) -> Dict:
        """获取智能体能力"""
        agent_id = message.parameters.get("agent_id")
        
        if agent_id:
            if agent_id in self.registered_agents:
                agent = self.registered_agents[agent_id]
                return {"capabilities": agent.capabilities}
            else:
                raise ValueError(f"智能体 {agent_id} 未注册")
        else:
            # 返回所有智能体能力
            capabilities = {}
            for aid, agent in self.registered_agents.items():
                capabilities[aid] = agent.capabilities
            return capabilities
    
    async def _handle_list_agents(self, message: AGUIMessage) -> Dict:
        """列出所有智能体"""
        agents = {}
        for agent_id, agent in self.registered_agents.items():
            agents[agent_id] = {
                "id": agent.id,
                "type": agent.type,
                "status": agent.status.value,
                "capabilities": agent.capabilities
            }
        
        return {"agents": agents, "count": len(agents)}
    
    async def _handle_get_session_info(self, message: AGUIMessage) -> Dict:
        """获取会话信息"""
        client_id = message.client_id or message.parameters.get("client_id")
        
        if not client_id:
            raise ValueError("缺少客户端ID")
        
        session = self.get_session(client_id)
        if not session:
            # 创建新会话
            session = self.create_session(client_id)
        
        return session
    
    def get_protocol_stats(self) -> Dict:
        """获取协议统计信息"""
        return {
            "registered_agents": len(self.registered_agents),
            "active_sessions": len(self.active_sessions),
            "message_history_count": len(self.message_history),
            "command_handlers": len(self.command_handlers),
            "last_activity": datetime.now().isoformat()
        }
    
    def get_message_history(self, limit: int = 50, client_id: str = None) -> List[Dict]:
        """获取消息历史"""
        history = self.message_history
        
        # 按客户端ID过滤
        if client_id:
            history = [
                msg for msg in history 
                if msg.get("message", {}).get("client_id") == client_id or
                   msg.get("response", {}).get("client_id") == client_id
            ]
        
        return history[-limit:] if limit > 0 else history
    
    def clear_message_history(self):
        """清空消息历史"""
        self.message_history.clear()
    
    async def health_check(self) -> Dict:
        """健康检查"""
        try:
            # 检查所有注册的智能体
            agent_health = {}
            for agent_id, agent in self.registered_agents.items():
                try:
                    status = agent.get_status()
                    agent_health[agent_id] = {
                        "healthy": status["status"] != "error",
                        "status": status["status"]
                    }
                except Exception as e:
                    agent_health[agent_id] = {
                        "healthy": False,
                        "error": str(e)
                    }
            
            overall_health = all(info["healthy"] for info in agent_health.values())
            
            return {
                "healthy": overall_health,
                "agents": agent_health,
                "protocol_stats": self.get_protocol_stats(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }