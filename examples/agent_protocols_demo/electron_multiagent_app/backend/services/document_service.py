"""
文档服务 - 处理文档上传、存储和管理
"""

import os
import uuid
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiofiles
from fastapi import UploadFile


class DocumentService:
    """文档服务类"""
    
    def __init__(self, upload_dir: str = "uploads", max_file_size: int = 50 * 1024 * 1024):
        self.upload_dir = Path(upload_dir)
        self.max_file_size = max_file_size  # 50MB
        self.documents: Dict[str, Dict] = {}  # 文档注册表
        self.supported_formats = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.rtf': 'application/rtf'
        }
        
        # 确保上传目录存在
        self.upload_dir.mkdir(exist_ok=True)
        print(f"📁 文档服务初始化，上传目录: {self.upload_dir}")
    
    async def save_uploaded_file(self, file: UploadFile) -> Path:
        """保存上传的文件"""
        # 验证文件
        await self._validate_file(file)
        
        # 生成唯一文件名
        file_extension = Path(file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = self.upload_dir / unique_filename
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        print(f"📄 文件已保存: {file.filename} -> {file_path}")
        return file_path
    
    async def _validate_file(self, file: UploadFile):
        """验证上传的文件"""
        # 检查文件大小
        content = await file.read()
        file_size = len(content)
        
        if file_size > self.max_file_size:
            raise ValueError(f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)")
        
        # 重置文件指针
        await file.seek(0)
        
        # 检查文件格式
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_extension}")
        
        # 检查 MIME 类型
        if file.content_type and file.content_type not in self.supported_formats.values():
            print(f"⚠️ MIME 类型警告: {file.content_type}")
    
    def generate_document_id(self, filename: str) -> str:
        """生成文档ID"""
        # 使用时间戳和文件名生成唯一ID
        timestamp = str(int(datetime.now().timestamp() * 1000))
        filename_hash = hashlib.md5(filename.encode()).hexdigest()[:8]
        return f"doc_{timestamp}_{filename_hash}"
    
    def register_document(self, doc_id: str, file_path: Path, original_filename: str, 
                         metadata: Dict = None):
        """注册文档"""
        document_info = {
            "id": doc_id,
            "original_filename": original_filename,
            "file_path": str(file_path),
            "file_size": file_path.stat().st_size,
            "file_extension": file_path.suffix.lower(),
            "mime_type": self.supported_formats.get(file_path.suffix.lower()),
            "uploaded_at": datetime.now().isoformat(),
            "status": "uploaded",
            "metadata": metadata or {},
            "processing_history": []
        }
        
        self.documents[doc_id] = document_info
        print(f"📋 文档已注册: {doc_id} ({original_filename})")
        
        return document_info
    
    def get_document(self, doc_id: str) -> Optional[Dict]:
        """获取文档信息"""
        return self.documents.get(doc_id)
    
    def get_all_documents(self) -> List[Dict]:
        """获取所有文档"""
        return list(self.documents.values())
    
    def update_document_status(self, doc_id: str, status: str, metadata: Dict = None):
        """更新文档状态"""
        if doc_id in self.documents:
            self.documents[doc_id]["status"] = status
            self.documents[doc_id]["last_updated"] = datetime.now().isoformat()
            
            if metadata:
                self.documents[doc_id]["metadata"].update(metadata)
            
            print(f"📝 文档状态更新: {doc_id} -> {status}")
    
    def add_processing_record(self, doc_id: str, process_type: str, result: Dict):
        """添加处理记录"""
        if doc_id in self.documents:
            record = {
                "process_type": process_type,
                "timestamp": datetime.now().isoformat(),
                "result": result
            }
            
            self.documents[doc_id]["processing_history"].append(record)
            print(f"🔄 处理记录已添加: {doc_id} - {process_type}")
    
    def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        if doc_id not in self.documents:
            return False
        
        document_info = self.documents[doc_id]
        file_path = Path(document_info["file_path"])
        
        try:
            # 删除文件
            if file_path.exists():
                file_path.unlink()
            
            # 从注册表中移除
            del self.documents[doc_id]
            
            print(f"🗑️ 文档已删除: {doc_id}")
            return True
            
        except Exception as e:
            print(f"❌ 删除文档失败: {doc_id} - {str(e)}")
            return False
    
    def get_document_stats(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        if not self.documents:
            return {
                "total_documents": 0,
                "total_size": 0,
                "formats": {},
                "status_distribution": {}
            }
        
        total_size = sum(doc["file_size"] for doc in self.documents.values())
        
        # 格式分布
        formats = {}
        for doc in self.documents.values():
            ext = doc["file_extension"]
            formats[ext] = formats.get(ext, 0) + 1
        
        # 状态分布
        status_distribution = {}
        for doc in self.documents.values():
            status = doc["status"]
            status_distribution[status] = status_distribution.get(status, 0) + 1
        
        return {
            "total_documents": len(self.documents),
            "total_size": total_size,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "formats": formats,
            "status_distribution": status_distribution,
            "upload_dir": str(self.upload_dir),
            "max_file_size_mb": self.max_file_size / 1024 / 1024
        }
    
    def search_documents(self, query: str = None, status: str = None, 
                        format_filter: str = None) -> List[Dict]:
        """搜索文档"""
        results = list(self.documents.values())
        
        # 按查询词过滤
        if query:
            query_lower = query.lower()
            results = [
                doc for doc in results
                if query_lower in doc["original_filename"].lower() or
                   query_lower in doc.get("metadata", {}).get("title", "").lower()
            ]
        
        # 按状态过滤
        if status:
            results = [doc for doc in results if doc["status"] == status]
        
        # 按格式过滤
        if format_filter:
            results = [doc for doc in results if doc["file_extension"] == format_filter]
        
        return results
    
    def get_recent_documents(self, limit: int = 10) -> List[Dict]:
        """获取最近的文档"""
        documents = list(self.documents.values())
        documents.sort(key=lambda x: x["uploaded_at"], reverse=True)
        return documents[:limit]
    
    def cleanup_old_files(self, days: int = 30) -> int:
        """清理旧文件"""
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        deleted_count = 0
        
        docs_to_delete = []
        for doc_id, doc_info in self.documents.items():
            uploaded_at = datetime.fromisoformat(doc_info["uploaded_at"])
            if uploaded_at < cutoff_date:
                docs_to_delete.append(doc_id)
        
        for doc_id in docs_to_delete:
            if self.delete_document(doc_id):
                deleted_count += 1
        
        print(f"🧹 清理完成，删除了 {deleted_count} 个旧文件")
        return deleted_count
    
    def export_document_list(self, format: str = "json") -> str:
        """导出文档列表"""
        if format == "json":
            import json
            return json.dumps(list(self.documents.values()), indent=2, ensure_ascii=False)
        
        elif format == "csv":
            import csv
            import io
            
            output = io.StringIO()
            if self.documents:
                fieldnames = ["id", "original_filename", "file_size", "file_extension", 
                             "uploaded_at", "status"]
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                
                for doc in self.documents.values():
                    row = {field: doc.get(field, "") for field in fieldnames}
                    writer.writerow(row)
            
            return output.getvalue()
        
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def get_file_content(self, doc_id: str) -> Optional[bytes]:
        """获取文件内容"""
        if doc_id not in self.documents:
            return None
        
        file_path = Path(self.documents[doc_id]["file_path"])
        
        try:
            if file_path.exists():
                return file_path.read_bytes()
            else:
                print(f"❌ 文件不存在: {file_path}")
                return None
        except Exception as e:
            print(f"❌ 读取文件失败: {str(e)}")
            return None
    
    async def get_file_content_async(self, doc_id: str) -> Optional[bytes]:
        """异步获取文件内容"""
        if doc_id not in self.documents:
            return None
        
        file_path = Path(self.documents[doc_id]["file_path"])
        
        try:
            if file_path.exists():
                async with aiofiles.open(file_path, 'rb') as f:
                    return await f.read()
            else:
                print(f"❌ 文件不存在: {file_path}")
                return None
        except Exception as e:
            print(f"❌ 异步读取文件失败: {str(e)}")
            return None