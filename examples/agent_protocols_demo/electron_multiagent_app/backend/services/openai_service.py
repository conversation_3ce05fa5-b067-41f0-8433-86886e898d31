"""
OpenAI 服务 - 处理与 OpenAI API 的交互
"""

import asyncio
import os
from typing import Optional, Dict, Any, List
import openai
from openai import AsyncOpenAI


class OpenAIService:
    """OpenAI 服务类"""
    
    def __init__(self):
        self.client: Optional[AsyncOpenAI] = None
        self.api_key: Optional[str] = None
        self.model: str = "gpt-3.5-turbo"
        self.max_tokens: int = 2000
        self.temperature: float = 0.7
        self.timeout: int = 30
        
        # 尝试从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_api_key = os.getenv("OPENAI_API_KEY")
        if env_api_key:
            self.set_api_key(env_api_key)
        
        env_model = os.getenv("OPENAI_MODEL")
        if env_model:
            self.model = env_model
    
    def set_api_key(self, api_key: str):
        """设置 API Key"""
        self.api_key = api_key
        if api_key:
            self.client = AsyncOpenAI(api_key=api_key)
            print("✅ OpenAI API Key 已设置")
        else:
            self.client = None
            print("❌ OpenAI API Key 已清除")
    
    def set_model(self, model: str):
        """设置模型"""
        self.model = model
        print(f"🤖 OpenAI 模型设置为: {model}")
    
    def set_parameters(self, max_tokens: int = None, temperature: float = None, timeout: int = None):
        """设置参数"""
        if max_tokens is not None:
            self.max_tokens = max_tokens
        if temperature is not None:
            self.temperature = temperature
        if timeout is not None:
            self.timeout = timeout
        
        print(f"⚙️ OpenAI 参数更新: max_tokens={self.max_tokens}, temperature={self.temperature}")
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return self.client is not None and self.api_key is not None
    
    async def generate_response(self, prompt: str, system_message: str = None, 
                               max_tokens: int = None, temperature: float = None) -> str:
        """生成响应"""
        if not self.is_configured():
            raise ValueError("OpenAI API 未配置，请设置 API Key")
        
        try:
            messages = []
            
            # 添加系统消息
            if system_message:
                messages.append({"role": "system", "content": system_message})
            
            # 添加用户消息
            messages.append({"role": "user", "content": prompt})
            
            # 调用 OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens or self.max_tokens,
                temperature=temperature if temperature is not None else self.temperature,
                timeout=self.timeout
            )
            
            return response.choices[0].message.content.strip()
            
        except openai.RateLimitError as e:
            raise Exception(f"OpenAI API 速率限制: {str(e)}")
        except openai.AuthenticationError as e:
            raise Exception(f"OpenAI API 认证失败: {str(e)}")
        except openai.APIError as e:
            raise Exception(f"OpenAI API 错误: {str(e)}")
        except Exception as e:
            raise Exception(f"生成响应失败: {str(e)}")
    
    async def generate_chat_response(self, messages: List[Dict[str, str]], 
                                   max_tokens: int = None, temperature: float = None) -> str:
        """生成对话响应"""
        if not self.is_configured():
            raise ValueError("OpenAI API 未配置，请设置 API Key")
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens or self.max_tokens,
                temperature=temperature if temperature is not None else self.temperature,
                timeout=self.timeout
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise Exception(f"生成对话响应失败: {str(e)}")
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """分析情感"""
        prompt = f"""
        请分析以下文本的情感倾向，返回 JSON 格式：
        {{
            "sentiment": "positive/negative/neutral",
            "confidence": 0.0-1.0,
            "explanation": "简短解释"
        }}
        
        文本：{text[:1000]}
        """
        
        try:
            response = await self.generate_response(prompt)
            
            # 尝试解析 JSON
            import json
            return json.loads(response)
            
        except json.JSONDecodeError:
            # 如果解析失败，返回默认结果
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "explanation": "无法解析 AI 响应"
            }
        except Exception as e:
            return {
                "sentiment": "neutral",
                "confidence": 0.0,
                "explanation": f"情感分析失败: {str(e)}"
            }
    
    async def summarize_text(self, text: str, max_length: int = 200) -> str:
        """文本摘要"""
        prompt = f"""
        请为以下文本生成一个简洁的摘要（不超过{max_length}字）：
        
        {text[:3000]}
        
        摘要：
        """
        
        try:
            return await self.generate_response(prompt, max_tokens=max_length)
        except Exception as e:
            return f"摘要生成失败: {str(e)}"
    
    async def extract_keywords(self, text: str, num_keywords: int = 10) -> List[str]:
        """提取关键词"""
        prompt = f"""
        请从以下文本中提取{num_keywords}个最重要的关键词，用逗号分隔：
        
        {text[:2000]}
        
        关键词：
        """
        
        try:
            response = await self.generate_response(prompt, max_tokens=100)
            
            # 解析关键词
            keywords = [kw.strip() for kw in response.split(',')]
            return keywords[:num_keywords]
            
        except Exception as e:
            return [f"关键词提取失败: {str(e)}"]
    
    async def answer_question(self, question: str, context: str) -> str:
        """基于上下文回答问题"""
        prompt = f"""
        基于以下上下文回答问题。如果上下文中没有相关信息，请明确说明。
        
        上下文：
        {context[:3000]}
        
        问题：{question}
        
        回答：
        """
        
        try:
            return await self.generate_response(prompt)
        except Exception as e:
            return f"回答问题失败: {str(e)}"
    
    async def translate_text(self, text: str, target_language: str = "中文") -> str:
        """翻译文本"""
        prompt = f"""
        请将以下文本翻译成{target_language}：
        
        {text[:2000]}
        
        翻译：
        """
        
        try:
            return await self.generate_response(prompt)
        except Exception as e:
            return f"翻译失败: {str(e)}"
    
    async def generate_suggestions(self, text: str, context: str = "") -> List[str]:
        """生成建议"""
        prompt = f"""
        基于以下内容，请提供5个实用的建议或改进意见：
        
        内容：{text[:2000]}
        
        {f"上下文：{context[:1000]}" if context else ""}
        
        建议：
        1.
        """
        
        try:
            response = await self.generate_response(prompt)
            
            # 解析建议
            lines = response.split('\n')
            suggestions = []
            for line in lines:
                line = line.strip()
                if line and (line.startswith(tuple('123456789')) or line.startswith('•') or line.startswith('-')):
                    clean_suggestion = line.lstrip('123456789.•- ').strip()
                    if clean_suggestion:
                        suggestions.append(clean_suggestion)
            
            return suggestions[:5]
            
        except Exception as e:
            return [f"建议生成失败: {str(e)}"]
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            "api_key_configured": bool(self.api_key),
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "is_configured": self.is_configured()
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        if not self.is_configured():
            return {
                "success": False,
                "error": "API Key 未配置"
            }
        
        try:
            # 发送简单的测试请求
            response = await self.generate_response(
                "请回复'连接测试成功'",
                max_tokens=50,
                temperature=0
            )
            
            return {
                "success": True,
                "response": response,
                "model": self.model,
                "message": "OpenAI API 连接正常"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "OpenAI API 连接失败"
            }
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计（简化版本）"""
        # 在实际应用中，这里可以跟踪 API 使用情况
        return {
            "model": self.model,
            "configured": self.is_configured(),
            "last_used": "未实现统计功能",
            "total_requests": "未实现统计功能",
            "total_tokens": "未实现统计功能"
        }