"""
内容分析智能体 - 负责分析文档内容
"""

import asyncio
import re
from typing import Dict, Any, List
from collections import Counter

try:
    import nltk
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import KMeans
    import numpy as np
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from agents.base_agent import BaseAgent, Message
from services.openai_service import OpenAIService


class ContentAnalyzerAgent(BaseAgent):
    """内容分析智能体"""
    
    def __init__(self):
        super().__init__("content_analyzer", "content_processor")
        self.add_capability("text_analysis")
        self.add_capability("keyword_extraction")
        self.add_capability("sentiment_analysis")
        self.add_capability("topic_modeling")
        self.add_capability("entity_recognition")
        
        self.openai_service = OpenAIService()
        self._initialize_nltk()
    
    def _initialize_nltk(self):
        """初始化 NLTK 资源"""
        if not NLTK_AVAILABLE:
            print("⚠️ NLTK 未安装，将使用简化的文本处理功能")
            return
            
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
        except Exception as e:
            print(f"NLTK 初始化警告: {e}")
    
    async def process_message(self, message: Message):
        """处理 A2A 消息"""
        message_type = message.message_type
        content = message.content
        sender = message.sender
        
        if message_type == "analyze_content_request":
            # 处理内容分析请求
            text_content = content.get("text_content", "")
            doc_id = content.get("document_id")
            analysis_type = content.get("analysis_type", "comprehensive")
            
            try:
                analysis_result = await self._analyze_content(text_content, analysis_type)
                
                # 发送分析结果
                await self.send_message(
                    sender,
                    "analyze_content_response",
                    {
                        "document_id": doc_id,
                        "success": True,
                        "analysis_result": analysis_result
                    }
                )
                
            except Exception as e:
                await self.send_message(
                    sender,
                    "analyze_content_response",
                    {
                        "document_id": doc_id,
                        "success": False,
                        "error": str(e)
                    }
                )
        
        elif message_type == "collaboration_request":
            # 处理协作请求
            task_description = content.get("task_description", "")
            
            if any(keyword in task_description.lower() for keyword in 
                   ["analysis", "analyze", "content", "text", "sentiment"]):
                await self.respond_to_collaboration(sender, True, {
                    "message": "我可以协助内容分析任务",
                    "capabilities": self.capabilities
                })
            else:
                await self.respond_to_collaboration(sender, False, {
                    "message": "该任务不在我的能力范围内"
                })
    
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令"""
        if command == "analyze_content":
            document_id = parameters.get("document_id")
            parsed_content = parameters.get("parsed_content", {})
            text_content = parsed_content.get("parsed_content", "")
            
            if not text_content:
                raise ValueError("没有可分析的文本内容")
            
            # 执行综合分析
            analysis_result = await self._analyze_content(text_content, "comprehensive")
            
            return {
                "document_id": document_id,
                "analysis": analysis_result
            }
        
        elif command == "extract_keywords":
            text_content = parameters.get("text_content", "")
            top_k = parameters.get("top_k", 10)
            
            keywords = await self._extract_keywords(text_content, top_k)
            return {"keywords": keywords}
        
        elif command == "analyze_sentiment":
            text_content = parameters.get("text_content", "")
            
            sentiment = await self._analyze_sentiment(text_content)
            return {"sentiment": sentiment}
        
        elif command == "detect_topics":
            text_content = parameters.get("text_content", "")
            num_topics = parameters.get("num_topics", 5)
            
            topics = await self._detect_topics(text_content, num_topics)
            return {"topics": topics}
        
        else:
            raise ValueError(f"不支持的命令: {command}")
    
    async def _analyze_content(self, text_content: str, analysis_type: str = "comprehensive") -> Dict:
        """分析文本内容"""
        if not text_content.strip():
            raise ValueError("文本内容为空")
        
        analysis_result = {
            "basic_stats": await self._get_basic_statistics(text_content),
            "timestamp": self._get_timestamp()
        }
        
        if analysis_type in ["comprehensive", "keywords"]:
            analysis_result["keywords"] = await self._extract_keywords(text_content)
        
        if analysis_type in ["comprehensive", "sentiment"]:
            analysis_result["sentiment"] = await self._analyze_sentiment(text_content)
        
        if analysis_type in ["comprehensive", "topics"]:
            analysis_result["topics"] = await self._detect_topics(text_content)
        
        if analysis_type in ["comprehensive", "entities"]:
            analysis_result["entities"] = await self._extract_entities(text_content)
        
        if analysis_type in ["comprehensive", "ai_insights"]:
            analysis_result["ai_insights"] = await self._get_ai_insights(text_content)
        
        return analysis_result
    
    async def _get_basic_statistics(self, text: str) -> Dict:
        """获取基本统计信息"""
        if NLTK_AVAILABLE:
            try:
                sentences = nltk.sent_tokenize(text)
                words = nltk.word_tokenize(text)
                words_only = [word for word in words if word.isalnum()]
            except:
                # 如果 NLTK 失败，使用简单方法
                sentences = text.split('.')
                words_only = text.split()
        else:
            # 简单的文本分割
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            words_only = [word for word in text.split() if word.isalnum()]
        
        return {
            "character_count": len(text),
            "word_count": len(words_only),
            "sentence_count": len(sentences),
            "paragraph_count": len([p for p in text.split('\n\n') if p.strip()]),
            "average_words_per_sentence": len(words_only) / len(sentences) if sentences else 0,
            "average_sentence_length": len(text) / len(sentences) if sentences else 0
        }
    
    async def _extract_keywords(self, text: str, top_k: int = 10) -> List[Dict]:
        """提取关键词"""
        if SKLEARN_AVAILABLE:
            try:
                # 使用 TF-IDF 提取关键词
                vectorizer = TfidfVectorizer(
                    max_features=100,
                    stop_words='english',
                    ngram_range=(1, 2)
                )
                
                tfidf_matrix = vectorizer.fit_transform([text])
                feature_names = vectorizer.get_feature_names_out()
                tfidf_scores = tfidf_matrix.toarray()[0]
                
                # 获取 top-k 关键词
                top_indices = np.argsort(tfidf_scores)[-top_k:][::-1]
                
                keywords = []
                for idx in top_indices:
                    if tfidf_scores[idx] > 0:
                        keywords.append({
                            "keyword": feature_names[idx],
                            "score": float(tfidf_scores[idx]),
                            "type": "tfidf"
                        })
                
                return keywords
                
            except Exception as e:
                print(f"TF-IDF 关键词提取错误: {e}")
        
        # 简单的词频统计作为备选方案
        if NLTK_AVAILABLE:
            try:
                words = nltk.word_tokenize(text.lower())
            except:
                words = text.lower().split()
        else:
            words = text.lower().split()
            
        words = [word for word in words if word.isalnum() and len(word) > 3]
        
        word_freq = Counter(words)
        return [
            {"keyword": word, "score": freq, "type": "frequency"}
            for word, freq in word_freq.most_common(top_k)
        ]
    
    async def _analyze_sentiment(self, text: str) -> Dict:
        """分析情感倾向"""
        try:
            # 使用 OpenAI 进行情感分析
            if self.openai_service.is_configured():
                prompt = f"""
                请分析以下文本的情感倾向，返回 JSON 格式：
                {{
                    "sentiment": "positive/negative/neutral",
                    "confidence": 0.0-1.0,
                    "explanation": "简短解释"
                }}
                
                文本：{text[:1000]}...
                """
                
                response = await self.openai_service.generate_response(prompt)
                
                # 尝试解析 JSON 响应
                import json
                try:
                    return json.loads(response)
                except:
                    pass
            
            # 简单的情感分析备选方案
            positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic']
            negative_words = ['bad', 'terrible', 'awful', 'horrible', 'disappointing', 'poor']
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                sentiment = "positive"
                confidence = min(0.8, positive_count / (positive_count + negative_count + 1))
            elif negative_count > positive_count:
                sentiment = "negative"
                confidence = min(0.8, negative_count / (positive_count + negative_count + 1))
            else:
                sentiment = "neutral"
                confidence = 0.5
            
            return {
                "sentiment": sentiment,
                "confidence": confidence,
                "explanation": f"基于关键词分析: 积极词汇 {positive_count} 个，消极词汇 {negative_count} 个"
            }
            
        except Exception as e:
            return {
                "sentiment": "neutral",
                "confidence": 0.0,
                "explanation": f"情感分析失败: {str(e)}"
            }
    
    async def _detect_topics(self, text: str, num_topics: int = 5) -> List[Dict]:
        """主题检测"""
        if NLTK_AVAILABLE:
            try:
                sentences = nltk.sent_tokenize(text)
            except:
                sentences = [s.strip() for s in text.split('.') if s.strip()]
        else:
            sentences = [s.strip() for s in text.split('.') if s.strip()]
        
        if len(sentences) < num_topics:
            return [{"topic": f"主题 {i+1}", "keywords": [], "sentences": sentences[i:i+1] if i < len(sentences) else []} 
                   for i in range(num_topics)]
        
        if SKLEARN_AVAILABLE:
            try:
                # 使用 TF-IDF 和 K-means 聚类
                vectorizer = TfidfVectorizer(max_features=50, stop_words='english')
                tfidf_matrix = vectorizer.fit_transform(sentences)
                
                kmeans = KMeans(n_clusters=min(num_topics, len(sentences)), random_state=42)
                clusters = kmeans.fit_predict(tfidf_matrix)
                
                topics = []
                feature_names = vectorizer.get_feature_names_out()
                
                for i in range(min(num_topics, len(set(clusters)))):
                    cluster_sentences = [sentences[j] for j in range(len(sentences)) if clusters[j] == i]
                    
                    # 获取该聚类的关键词
                    if i < len(kmeans.cluster_centers_):
                        cluster_center = kmeans.cluster_centers_[i]
                        top_indices = np.argsort(cluster_center)[-5:][::-1]
                        keywords = [feature_names[idx] for idx in top_indices if cluster_center[idx] > 0]
                    else:
                        keywords = []
                    
                    topics.append({
                        "topic": f"主题 {i+1}",
                        "keywords": keywords,
                        "sentences": cluster_sentences[:3],  # 最多3个代表性句子
                        "sentence_count": len(cluster_sentences)
                    })
                
                return topics
                
            except Exception as e:
                print(f"机器学习主题检测错误: {e}")
        
        # 简单的主题检测备选方案
        topics = []
        sentences_per_topic = max(1, len(sentences) // num_topics)
        
        for i in range(num_topics):
            start_idx = i * sentences_per_topic
            end_idx = min((i + 1) * sentences_per_topic, len(sentences))
            topic_sentences = sentences[start_idx:end_idx]
            
            # 简单的关键词提取
            topic_text = ' '.join(topic_sentences)
            words = topic_text.lower().split()
            word_freq = Counter([w for w in words if len(w) > 3 and w.isalnum()])
            keywords = [word for word, _ in word_freq.most_common(3)]
            
            topics.append({
                "topic": f"主题 {i+1}",
                "keywords": keywords,
                "sentences": topic_sentences[:2],
                "sentence_count": len(topic_sentences)
            })
        
        return topics
    
    async def _extract_entities(self, text: str) -> List[Dict]:
        """实体识别"""
        try:
            # 简单的实体识别
            entities = []
            
            # 邮箱地址
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text)
            for email in emails:
                entities.append({"text": email, "type": "EMAIL", "confidence": 0.9})
            
            # 电话号码
            phone_pattern = r'\b\d{3}-\d{3}-\d{4}\b|\b\d{10}\b'
            phones = re.findall(phone_pattern, text)
            for phone in phones:
                entities.append({"text": phone, "type": "PHONE", "confidence": 0.8})
            
            # 日期
            date_pattern = r'\b\d{4}-\d{2}-\d{2}\b|\b\d{1,2}/\d{1,2}/\d{4}\b'
            dates = re.findall(date_pattern, text)
            for date in dates:
                entities.append({"text": date, "type": "DATE", "confidence": 0.7})
            
            # URL
            url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
            urls = re.findall(url_pattern, text)
            for url in urls:
                entities.append({"text": url, "type": "URL", "confidence": 0.9})
            
            return entities
            
        except Exception as e:
            return [{"text": "实体识别失败", "type": "ERROR", "confidence": 0.0, "error": str(e)}]
    
    async def _get_ai_insights(self, text: str) -> Dict:
        """获取 AI 洞察"""
        try:
            if not self.openai_service.is_configured():
                return {
                    "summary": "AI 服务未配置",
                    "key_points": [],
                    "recommendations": []
                }
            
            prompt = f"""
            请分析以下文本并提供洞察，返回 JSON 格式：
            {{
                "summary": "文本摘要（100字以内）",
                "key_points": ["要点1", "要点2", "要点3"],
                "recommendations": ["建议1", "建议2"],
                "tone": "文本语调描述",
                "complexity": "简单/中等/复杂"
            }}
            
            文本：{text[:2000]}...
            """
            
            response = await self.openai_service.generate_response(prompt)
            
            # 尝试解析 JSON 响应
            import json
            try:
                return json.loads(response)
            except:
                return {
                    "summary": response[:200] + "..." if len(response) > 200 else response,
                    "key_points": [],
                    "recommendations": [],
                    "tone": "未知",
                    "complexity": "未知"
                }
                
        except Exception as e:
            return {
                "summary": f"AI 洞察生成失败: {str(e)}",
                "key_points": [],
                "recommendations": [],
                "error": str(e)
            }
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()