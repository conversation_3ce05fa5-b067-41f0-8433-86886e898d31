"""
基础智能体类 - A2A 协议实现
"""

import asyncio
import json
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum


class AgentStatus(Enum):
    """智能体状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


class Message:
    """A2A 协议消息类"""
    
    def __init__(self, sender: str, receiver: str, message_type: str, content: Any, 
                 message_id: str = None, reply_to: str = None):
        self.id = message_id or f"msg_{int(datetime.now().timestamp() * 1000)}"
        self.timestamp = datetime.now().isoformat()
        self.sender = sender
        self.receiver = receiver
        self.message_type = message_type
        self.content = content
        self.reply_to = reply_to
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "sender": self.sender,
            "receiver": self.receiver,
            "message_type": self.message_type,
            "content": self.content,
            "reply_to": self.reply_to
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Message':
        """从字典创建消息对象"""
        return cls(
            sender=data["sender"],
            receiver=data["receiver"],
            message_type=data["message_type"],
            content=data["content"],
            message_id=data.get("id"),
            reply_to=data.get("reply_to")
        )


class BaseAgent(ABC):
    """基础智能体类"""
    
    def __init__(self, agent_id: str, agent_type: str):
        self.id = agent_id
        self.type = agent_type
        self.status = AgentStatus.IDLE
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.sent_messages: List[Message] = []
        self.received_messages: List[Message] = []
        self.capabilities: List[str] = []
        self.is_running = False
        self.agent_registry: Dict[str, 'BaseAgent'] = {}
        
    async def start(self):
        """启动智能体"""
        self.is_running = True
        self.status = AgentStatus.IDLE
        print(f"[{self.id}] 智能体已启动")
        
        # 启动消息处理循环
        asyncio.create_task(self._message_processing_loop())
    
    async def stop(self):
        """停止智能体"""
        self.is_running = False
        self.status = AgentStatus.OFFLINE
        print(f"[{self.id}] 智能体已停止")
    
    def set_agent_registry(self, registry: Dict[str, 'BaseAgent']):
        """设置智能体注册表"""
        self.agent_registry = registry
    
    async def send_message(self, receiver_id: str, message_type: str, content: Any, 
                          reply_to: str = None) -> bool:
        """发送消息到其他智能体 (A2A 协议)"""
        try:
            message = Message(
                sender=self.id,
                receiver=receiver_id,
                message_type=message_type,
                content=content,
                reply_to=reply_to
            )
            
            # 查找目标智能体
            if receiver_id in self.agent_registry:
                target_agent = self.agent_registry[receiver_id]
                await target_agent.receive_message(message)
                
                # 记录发送的消息
                self.sent_messages.append(message)
                
                print(f"[A2A] {self.id} -> {receiver_id}: {message_type}")
                return True
            else:
                print(f"[A2A] 错误: 找不到智能体 {receiver_id}")
                return False
                
        except Exception as e:
            print(f"[A2A] 发送消息失败: {e}")
            return False
    
    async def receive_message(self, message: Message):
        """接收消息"""
        await self.message_queue.put(message)
        self.received_messages.append(message)
    
    async def _message_processing_loop(self):
        """消息处理循环"""
        while self.is_running:
            try:
                # 等待消息
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                # 设置状态为忙碌
                self.status = AgentStatus.BUSY
                
                # 处理消息
                await self.process_message(message)
                
                # 设置状态为空闲
                self.status = AgentStatus.IDLE
                
            except asyncio.TimeoutError:
                # 超时，继续循环
                continue
            except Exception as e:
                print(f"[{self.id}] 消息处理错误: {e}")
                self.status = AgentStatus.ERROR
                await asyncio.sleep(1)  # 错误恢复延迟
                self.status = AgentStatus.IDLE
    
    @abstractmethod
    async def process_message(self, message: Message):
        """处理收到的消息 - 子类必须实现"""
        pass
    
    async def handle_ui_command(self, command: str, parameters: Dict) -> Dict:
        """处理来自 UI 的命令 (AG-UI 协议)"""
        try:
            # 设置状态为忙碌
            self.status = AgentStatus.BUSY
            
            # 处理命令
            result = await self.execute_ui_command(command, parameters)
            
            # 设置状态为空闲
            self.status = AgentStatus.IDLE
            
            return {
                "success": True,
                "result": result,
                "agent_id": self.id,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.status = AgentStatus.ERROR
            await asyncio.sleep(0.1)  # 短暂延迟
            self.status = AgentStatus.IDLE
            
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.id,
                "timestamp": datetime.now().isoformat()
            }
    
    @abstractmethod
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令 - 子类必须实现"""
        pass
    
    def get_status(self) -> Dict:
        """获取智能体状态"""
        return {
            "id": self.id,
            "type": self.type,
            "status": self.status.value,
            "capabilities": self.capabilities,
            "sent_messages": len(self.sent_messages),
            "received_messages": len(self.received_messages),
            "queue_size": self.message_queue.qsize(),
            "last_activity": datetime.now().isoformat()
        }
    
    async def broadcast_message(self, message_type: str, content: Any, exclude_self: bool = True):
        """广播消息到所有智能体"""
        tasks = []
        for agent_id, agent in self.agent_registry.items():
            if exclude_self and agent_id == self.id:
                continue
            task = self.send_message(agent_id, message_type, content)
            tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def request_collaboration(self, target_agent_id: str, task_description: str, 
                                  task_data: Any = None) -> bool:
        """请求与其他智能体协作"""
        collaboration_request = {
            "task_description": task_description,
            "task_data": task_data,
            "requester": self.id,
            "timestamp": datetime.now().isoformat()
        }
        
        return await self.send_message(
            target_agent_id,
            "collaboration_request",
            collaboration_request
        )
    
    async def respond_to_collaboration(self, requester_id: str, accepted: bool, 
                                     response_data: Any = None):
        """响应协作请求"""
        response = {
            "accepted": accepted,
            "response_data": response_data,
            "responder": self.id,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.send_message(
            requester_id,
            "collaboration_response",
            response
        )
    
    def add_capability(self, capability: str):
        """添加能力"""
        if capability not in self.capabilities:
            self.capabilities.append(capability)
    
    def remove_capability(self, capability: str):
        """移除能力"""
        if capability in self.capabilities:
            self.capabilities.remove(capability)
    
    def has_capability(self, capability: str) -> bool:
        """检查是否具有某种能力"""
        return capability in self.capabilities