"""
文档解析智能体 - 负责解析上传的文档
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, Any

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

from agents.base_agent import BaseAgent, Message


class DocumentParserAgent(BaseAgent):
    """文档解析智能体"""
    
    def __init__(self):
        super().__init__("document_parser", "document_processor")
        self.add_capability("pdf_parsing")
        self.add_capability("docx_parsing")
        self.add_capability("txt_parsing")
        self.supported_formats = [".pdf", ".docx", ".doc", ".txt"]
    
    async def process_message(self, message: Message):
        """处理 A2A 消息"""
        message_type = message.message_type
        content = message.content
        sender = message.sender
        
        if message_type == "parse_document_request":
            # 处理文档解析请求
            doc_path = content.get("document_path")
            doc_id = content.get("document_id")
            
            try:
                parsed_content = await self._parse_document(doc_path)
                
                # 发送解析结果
                await self.send_message(
                    sender,
                    "parse_document_response",
                    {
                        "document_id": doc_id,
                        "success": True,
                        "parsed_content": parsed_content,
                        "metadata": {
                            "file_size": os.path.getsize(doc_path),
                            "format": Path(doc_path).suffix,
                            "word_count": len(parsed_content.split()) if parsed_content else 0
                        }
                    }
                )
                
            except Exception as e:
                await self.send_message(
                    sender,
                    "parse_document_response",
                    {
                        "document_id": doc_id,
                        "success": False,
                        "error": str(e)
                    }
                )
        
        elif message_type == "collaboration_request":
            # 处理协作请求
            task_description = content.get("task_description", "")
            
            if "document parsing" in task_description.lower():
                await self.respond_to_collaboration(sender, True, {
                    "message": "我可以协助文档解析任务",
                    "capabilities": self.capabilities
                })
            else:
                await self.respond_to_collaboration(sender, False, {
                    "message": "该任务不在我的能力范围内"
                })
    
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令"""
        if command == "parse_document":
            document_id = parameters.get("document_id")
            if not document_id:
                raise ValueError("缺少文档ID")
            
            # 模拟从文档服务获取文档路径
            doc_path = self._get_document_path(document_id)
            
            if not os.path.exists(doc_path):
                raise FileNotFoundError(f"文档文件不存在: {doc_path}")
            
            # 解析文档
            parsed_content = await self._parse_document(doc_path)
            
            return {
                "document_id": document_id,
                "parsed_content": parsed_content,
                "metadata": {
                    "file_size": os.path.getsize(doc_path),
                    "format": Path(doc_path).suffix,
                    "word_count": len(parsed_content.split()) if parsed_content else 0,
                    "character_count": len(parsed_content) if parsed_content else 0
                }
            }
        
        elif command == "get_supported_formats":
            return {
                "supported_formats": self.supported_formats,
                "capabilities": self.capabilities
            }
        
        elif command == "validate_document":
            document_path = parameters.get("document_path")
            if not document_path:
                raise ValueError("缺少文档路径")
            
            return await self._validate_document(document_path)
        
        else:
            raise ValueError(f"不支持的命令: {command}")
    
    async def _parse_document(self, file_path: str) -> str:
        """解析文档内容"""
        file_path = Path(file_path)
        file_extension = file_path.suffix.lower()
        
        if file_extension == ".pdf":
            return await self._parse_pdf(file_path)
        elif file_extension in [".docx", ".doc"]:
            return await self._parse_docx(file_path)
        elif file_extension == ".txt":
            return await self._parse_txt(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")
    
    async def _parse_pdf(self, file_path: Path) -> str:
        """解析 PDF 文件"""
        if not PDF_AVAILABLE:
            raise Exception("PDF 解析库未安装，请安装 PyPDF2: pip install PyPDF2")
            
        try:
            text_content = ""
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text_content += page.extract_text() + "\n"
            
            return text_content.strip()
            
        except Exception as e:
            raise Exception(f"PDF 解析失败: {str(e)}")
    
    async def _parse_docx(self, file_path: Path) -> str:
        """解析 DOCX 文件"""
        if not DOCX_AVAILABLE:
            raise Exception("DOCX 解析库未安装，请安装 python-docx: pip install python-docx")
            
        try:
            doc = Document(file_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            return text_content.strip()
            
        except Exception as e:
            raise Exception(f"DOCX 解析失败: {str(e)}")
    
    async def _parse_txt(self, file_path: Path) -> str:
        """解析 TXT 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            return content.strip()
            
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()
                return content.strip()
            except Exception as e:
                raise Exception(f"TXT 文件编码解析失败: {str(e)}")
        except Exception as e:
            raise Exception(f"TXT 解析失败: {str(e)}")
    
    async def _validate_document(self, file_path: str) -> Dict:
        """验证文档"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                "valid": False,
                "error": "文件不存在"
            }
        
        if file_path.suffix.lower() not in self.supported_formats:
            return {
                "valid": False,
                "error": f"不支持的文件格式: {file_path.suffix}"
            }
        
        try:
            file_size = file_path.stat().st_size
            if file_size > 50 * 1024 * 1024:  # 50MB 限制
                return {
                    "valid": False,
                    "error": "文件大小超过限制 (50MB)"
                }
            
            return {
                "valid": True,
                "file_size": file_size,
                "format": file_path.suffix,
                "name": file_path.name
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"文件验证失败: {str(e)}"
            }
    
    def _get_document_path(self, document_id: str) -> str:
        """获取文档路径 (模拟实现)"""
        # 在实际应用中，这里应该从数据库或文档服务获取路径
        uploads_dir = Path("uploads")
        
        # 简单的文档ID到路径映射
        for file_path in uploads_dir.glob("*"):
            if document_id in file_path.stem:
                return str(file_path)
        
        # 如果找不到，返回默认路径
        return str(uploads_dir / f"{document_id}.txt")