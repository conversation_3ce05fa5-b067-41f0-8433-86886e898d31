"""
摘要生成智能体 - 负责生成文档摘要和报告
"""

import asyncio
from typing import Dict, Any, List
from datetime import datetime

from agents.base_agent import BaseAgent, Message
from services.openai_service import OpenAIService


class SummaryGeneratorAgent(BaseAgent):
    """摘要生成智能体"""
    
    def __init__(self):
        super().__init__("summary_generator", "content_generator")
        self.add_capability("text_summarization")
        self.add_capability("report_generation")
        self.add_capability("key_points_extraction")
        self.add_capability("executive_summary")
        
        self.openai_service = OpenAIService()
    
    async def process_message(self, message: Message):
        """处理 A2A 消息"""
        message_type = message.message_type
        content = message.content
        sender = message.sender
        
        if message_type == "generate_summary_request":
            # 处理摘要生成请求
            text_content = content.get("text_content", "")
            doc_id = content.get("document_id")
            summary_type = content.get("summary_type", "comprehensive")
            
            try:
                summary_result = await self._generate_summary(text_content, summary_type)
                
                await self.send_message(
                    sender,
                    "generate_summary_response",
                    {
                        "document_id": doc_id,
                        "success": True,
                        "summary_result": summary_result
                    }
                )
                
            except Exception as e:
                await self.send_message(
                    sender,
                    "generate_summary_response",
                    {
                        "document_id": doc_id,
                        "success": False,
                        "error": str(e)
                    }
                )
        
        elif message_type == "collaboration_request":
            # 处理协作请求
            task_description = content.get("task_description", "")
            
            if any(keyword in task_description.lower() for keyword in 
                   ["summary", "摘要", "总结", "报告", "report"]):
                await self.respond_to_collaboration(sender, True, {
                    "message": "我可以协助摘要生成任务",
                    "capabilities": self.capabilities
                })
            else:
                await self.respond_to_collaboration(sender, False, {
                    "message": "该任务不在我的能力范围内"
                })
    
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令"""
        if command == "generate_summary":
            document_id = parameters.get("document_id")
            analysis = parameters.get("analysis", {})
            
            # 从分析结果中提取内容
            text_content = self._extract_text_from_analysis(analysis)
            
            if not text_content:
                raise ValueError("没有可用于生成摘要的内容")
            
            # 生成综合摘要
            summary_result = await self._generate_comprehensive_summary(
                text_content, analysis, document_id
            )
            
            return {
                "document_id": document_id,
                "summary": summary_result
            }
        
        elif command == "generate_executive_summary":
            text_content = parameters.get("text_content", "")
            max_length = parameters.get("max_length", 200)
            
            summary = await self._generate_executive_summary(text_content, max_length)
            return {"executive_summary": summary}
        
        elif command == "extract_key_points":
            text_content = parameters.get("text_content", "")
            num_points = parameters.get("num_points", 5)
            
            key_points = await self._extract_key_points(text_content, num_points)
            return {"key_points": key_points}
        
        elif command == "generate_report":
            document_id = parameters.get("document_id")
            analysis_data = parameters.get("analysis_data", {})
            
            report = await self._generate_comprehensive_report(document_id, analysis_data)
            return {"report": report}
        
        else:
            raise ValueError(f"不支持的命令: {command}")
    
    def _extract_text_from_analysis(self, analysis: Dict) -> str:
        """从分析结果中提取文本内容"""
        # 尝试从不同的字段中提取文本
        if "parsed_content" in analysis:
            return analysis["parsed_content"]
        
        if "content" in analysis:
            return analysis["content"]
        
        if "text" in analysis:
            return analysis["text"]
        
        # 如果是嵌套结构，递归查找
        for key, value in analysis.items():
            if isinstance(value, dict) and "parsed_content" in value:
                return value["parsed_content"]
            elif isinstance(value, str) and len(value) > 100:
                return value
        
        return ""
    
    async def _generate_summary(self, text_content: str, summary_type: str = "comprehensive") -> Dict:
        """生成摘要"""
        if not text_content.strip():
            raise ValueError("文本内容为空")
        
        summary_result = {
            "type": summary_type,
            "timestamp": datetime.now().isoformat()
        }
        
        if summary_type == "brief":
            summary_result["summary"] = await self._generate_brief_summary(text_content)
        elif summary_type == "executive":
            summary_result["summary"] = await self._generate_executive_summary(text_content)
        elif summary_type == "detailed":
            summary_result["summary"] = await self._generate_detailed_summary(text_content)
        else:  # comprehensive
            summary_result = await self._generate_comprehensive_summary(text_content, {}, "")
        
        return summary_result
    
    async def _generate_brief_summary(self, text_content: str, max_length: int = 100) -> str:
        """生成简短摘要"""
        try:
            if self.openai_service.is_configured():
                prompt = f"""
                请为以下文本生成一个简短摘要（不超过{max_length}字）：
                
                {text_content[:2000]}...
                
                摘要：
                """
                
                return await self.openai_service.generate_response(prompt)
            else:
                # 简单的摘要生成
                sentences = text_content.split('.')
                if len(sentences) > 3:
                    return '. '.join(sentences[:3]) + '.'
                else:
                    return text_content[:max_length] + "..." if len(text_content) > max_length else text_content
                    
        except Exception as e:
            return f"摘要生成失败: {str(e)}"
    
    async def _generate_executive_summary(self, text_content: str, max_length: int = 200) -> str:
        """生成执行摘要"""
        try:
            if self.openai_service.is_configured():
                prompt = f"""
                请为以下文本生成一个执行摘要，突出关键点和主要结论（不超过{max_length}字）：
                
                {text_content[:2000]}...
                
                执行摘要：
                """
                
                return await self.openai_service.generate_response(prompt)
            else:
                # 提取开头和结尾部分
                text_length = len(text_content)
                if text_length > max_length * 2:
                    beginning = text_content[:max_length//2]
                    ending = text_content[-max_length//2:]
                    return f"{beginning}...\n\n...{ending}"
                else:
                    return text_content[:max_length] + "..." if len(text_content) > max_length else text_content
                    
        except Exception as e:
            return f"执行摘要生成失败: {str(e)}"
    
    async def _generate_detailed_summary(self, text_content: str) -> str:
        """生成详细摘要"""
        try:
            if self.openai_service.is_configured():
                prompt = f"""
                请为以下文本生成一个详细摘要，包括：
                1. 主要内容概述
                2. 关键点分析
                3. 重要结论
                4. 建议或下一步行动
                
                文本内容：
                {text_content[:3000]}...
                
                详细摘要：
                """
                
                return await self.openai_service.generate_response(prompt)
            else:
                # 分段摘要
                paragraphs = text_content.split('\n\n')
                summary_parts = []
                
                for i, paragraph in enumerate(paragraphs[:5]):  # 最多处理5段
                    if paragraph.strip():
                        summary_parts.append(f"第{i+1}部分: {paragraph[:200]}...")
                
                return '\n\n'.join(summary_parts)
                
        except Exception as e:
            return f"详细摘要生成失败: {str(e)}"
    
    async def _generate_comprehensive_summary(self, text_content: str, analysis: Dict, document_id: str) -> Dict:
        """生成综合摘要"""
        try:
            result = {
                "document_id": document_id,
                "timestamp": datetime.now().isoformat(),
                "brief_summary": await self._generate_brief_summary(text_content, 150),
                "key_points": await self._extract_key_points(text_content, 5),
                "statistics": self._extract_statistics_from_analysis(analysis),
                "recommendations": await self._generate_recommendations(text_content, analysis)
            }
            
            # 如果有分析数据，添加更多信息
            if analysis:
                if "keywords" in analysis:
                    result["top_keywords"] = analysis["keywords"][:5]
                
                if "sentiment" in analysis:
                    result["sentiment_summary"] = analysis["sentiment"]
                
                if "topics" in analysis:
                    result["main_topics"] = [topic.get("topic", "") for topic in analysis["topics"][:3]]
            
            return result
            
        except Exception as e:
            return {
                "document_id": document_id,
                "error": f"综合摘要生成失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _extract_key_points(self, text_content: str, num_points: int = 5) -> List[str]:
        """提取关键点"""
        try:
            if self.openai_service.is_configured():
                prompt = f"""
                请从以下文本中提取{num_points}个最重要的关键点，每个关键点用一句话概括：
                
                {text_content[:2000]}...
                
                关键点：
                1.
                """
                
                response = await self.openai_service.generate_response(prompt)
                
                # 解析响应中的关键点
                lines = response.split('\n')
                key_points = []
                for line in lines:
                    line = line.strip()
                    if line and (line.startswith(tuple('123456789')) or line.startswith('•') or line.startswith('-')):
                        # 清理编号和符号
                        clean_point = line.lstrip('123456789.•- ').strip()
                        if clean_point:
                            key_points.append(clean_point)
                
                return key_points[:num_points]
            else:
                # 简单的关键点提取
                sentences = text_content.split('.')
                # 选择较长的句子作为关键点
                important_sentences = [s.strip() for s in sentences if len(s.strip()) > 50]
                return important_sentences[:num_points]
                
        except Exception as e:
            return [f"关键点提取失败: {str(e)}"]
    
    def _extract_statistics_from_analysis(self, analysis: Dict) -> Dict:
        """从分析结果中提取统计信息"""
        stats = {}
        
        if "basic_stats" in analysis:
            basic_stats = analysis["basic_stats"]
            stats.update({
                "word_count": basic_stats.get("word_count", 0),
                "sentence_count": basic_stats.get("sentence_count", 0),
                "paragraph_count": basic_stats.get("paragraph_count", 0)
            })
        
        if "keywords" in analysis:
            stats["keyword_count"] = len(analysis["keywords"])
        
        if "topics" in analysis:
            stats["topic_count"] = len(analysis["topics"])
        
        if "entities" in analysis:
            stats["entity_count"] = len(analysis["entities"])
        
        return stats
    
    async def _generate_recommendations(self, text_content: str, analysis: Dict) -> List[str]:
        """生成建议"""
        try:
            recommendations = []
            
            # 基于分析结果生成建议
            if analysis.get("sentiment", {}).get("sentiment") == "negative":
                recommendations.append("文档情感倾向偏负面，建议关注其中提到的问题和挑战")
            
            word_count = analysis.get("basic_stats", {}).get("word_count", 0)
            if word_count > 5000:
                recommendations.append("文档较长，建议分段阅读或制作详细目录")
            elif word_count < 500:
                recommendations.append("文档较短，可能需要补充更多详细信息")
            
            # 使用 AI 生成更智能的建议
            if self.openai_service.is_configured():
                prompt = f"""
                基于以下文档分析结果，请提供3-5个实用的建议：
                
                文档统计: {analysis.get('basic_stats', {})}
                关键词: {[kw.get('keyword', '') for kw in analysis.get('keywords', [])[:5]]}
                情感分析: {analysis.get('sentiment', {})}
                
                建议：
                """
                
                ai_recommendations = await self.openai_service.generate_response(prompt)
                
                # 解析 AI 建议
                lines = ai_recommendations.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and (line.startswith(tuple('123456789')) or line.startswith('•') or line.startswith('-')):
                        clean_rec = line.lstrip('123456789.•- ').strip()
                        if clean_rec:
                            recommendations.append(clean_rec)
            
            return recommendations[:5] if recommendations else ["建议进一步分析文档内容"]
            
        except Exception as e:
            return [f"建议生成失败: {str(e)}"]
    
    async def _generate_comprehensive_report(self, document_id: str, analysis_data: Dict) -> Dict:
        """生成综合报告"""
        try:
            report = {
                "document_id": document_id,
                "generated_at": datetime.now().isoformat(),
                "report_type": "comprehensive_analysis",
                "sections": {}
            }
            
            # 文档概览
            report["sections"]["overview"] = {
                "title": "文档概览",
                "content": analysis_data.get("basic_stats", {}),
                "summary": "文档基本信息和统计数据"
            }
            
            # 内容分析
            if "analysis" in analysis_data:
                report["sections"]["content_analysis"] = {
                    "title": "内容分析",
                    "content": analysis_data["analysis"],
                    "summary": "文档内容的深度分析结果"
                }
            
            # 摘要部分
            if "summary" in analysis_data:
                report["sections"]["summary"] = {
                    "title": "文档摘要",
                    "content": analysis_data["summary"],
                    "summary": "文档的核心内容总结"
                }
            
            # 结论和建议
            recommendations = await self._generate_recommendations("", analysis_data)
            report["sections"]["conclusions"] = {
                "title": "结论和建议",
                "content": {
                    "recommendations": recommendations,
                    "next_steps": ["进一步分析特定主题", "与相关人员讨论", "制定行动计划"]
                },
                "summary": "基于分析结果的建议和后续步骤"
            }
            
            return report
            
        except Exception as e:
            return {
                "document_id": document_id,
                "error": f"报告生成失败: {str(e)}",
                "generated_at": datetime.now().isoformat()
            }