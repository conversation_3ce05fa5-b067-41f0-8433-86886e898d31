"""
对话智能体 - 负责处理用户对话和问答
"""

import asyncio
from typing import Dict, Any, List
from datetime import datetime

from agents.base_agent import BaseAgent, Message
from services.openai_service import OpenAIService


class ChatAgent(BaseAgent):
    """对话智能体"""
    
    def __init__(self):
        super().__init__("chat_agent", "conversational")
        self.add_capability("natural_language_processing")
        self.add_capability("question_answering")
        self.add_capability("document_qa")
        self.add_capability("conversation_management")
        
        self.openai_service = OpenAIService()
        self.conversation_history: Dict[str, List[Dict]] = {}
        self.document_context: Dict[str, str] = {}  # 存储文档上下文
    
    async def process_message(self, message: Message):
        """处理 A2A 消息"""
        message_type = message.message_type
        content = message.content
        sender = message.sender
        
        if message_type == "document_context_update":
            # 更新文档上下文
            doc_id = content.get("document_id")
            doc_content = content.get("content", "")
            
            self.document_context[doc_id] = doc_content
            
            await self.send_message(
                sender,
                "context_update_ack",
                {"document_id": doc_id, "status": "updated"}
            )
        
        elif message_type == "qa_request":
            # 处理问答请求
            question = content.get("question", "")
            doc_id = content.get("document_id")
            client_id = content.get("client_id")
            
            try:
                answer = await self._answer_question(question, doc_id)
                
                await self.send_message(
                    sender,
                    "qa_response",
                    {
                        "question": question,
                        "answer": answer,
                        "document_id": doc_id,
                        "client_id": client_id
                    }
                )
                
            except Exception as e:
                await self.send_message(
                    sender,
                    "qa_response",
                    {
                        "question": question,
                        "error": str(e),
                        "document_id": doc_id,
                        "client_id": client_id
                    }
                )
        
        elif message_type == "collaboration_request":
            # 处理协作请求
            task_description = content.get("task_description", "")
            
            if any(keyword in task_description.lower() for keyword in 
                   ["chat", "conversation", "question", "answer", "dialogue"]):
                await self.respond_to_collaboration(sender, True, {
                    "message": "我可以协助对话和问答任务",
                    "capabilities": self.capabilities
                })
            else:
                await self.respond_to_collaboration(sender, False, {
                    "message": "该任务不在我的能力范围内"
                })
    
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令"""
        if command == "process_message":
            message = parameters.get("message", "")
            client_id = parameters.get("client_id", "default")
            
            if not message.strip():
                raise ValueError("消息内容不能为空")
            
            # 处理用户消息
            response = await self._process_user_message(message, client_id)
            
            return {"response": response}
        
        elif command == "set_document_context":
            doc_id = parameters.get("document_id")
            content = parameters.get("content", "")
            
            self.document_context[doc_id] = content
            
            return {
                "status": "success",
                "message": f"文档 {doc_id} 的上下文已设置"
            }
        
        elif command == "get_conversation_history":
            client_id = parameters.get("client_id", "default")
            limit = parameters.get("limit", 10)
            
            history = self.conversation_history.get(client_id, [])
            return {
                "history": history[-limit:] if limit > 0 else history,
                "total_messages": len(history)
            }
        
        elif command == "clear_conversation":
            client_id = parameters.get("client_id", "default")
            
            if client_id in self.conversation_history:
                del self.conversation_history[client_id]
            
            return {"status": "success", "message": "对话历史已清除"}
        
        else:
            raise ValueError(f"不支持的命令: {command}")
    
    async def _process_user_message(self, message: str, client_id: str) -> str:
        """处理用户消息"""
        # 初始化对话历史
        if client_id not in self.conversation_history:
            self.conversation_history[client_id] = []
        
        # 记录用户消息
        self.conversation_history[client_id].append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })
        
        # 检查是否是文档相关问题
        if self._is_document_question(message):
            response = await self._handle_document_question(message, client_id)
        else:
            response = await self._handle_general_conversation(message, client_id)
        
        # 记录助手回复
        self.conversation_history[client_id].append({
            "role": "assistant",
            "content": response,
            "timestamp": datetime.now().isoformat()
        })
        
        # 限制对话历史长度
        if len(self.conversation_history[client_id]) > 20:
            self.conversation_history[client_id] = self.conversation_history[client_id][-20:]
        
        return response
    
    def _is_document_question(self, message: str) -> bool:
        """判断是否是文档相关问题"""
        document_keywords = [
            "文档", "document", "文件", "file", "内容", "content",
            "分析", "analysis", "摘要", "summary", "关键词", "keywords",
            "这个", "这份", "上传的", "刚才的"
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in document_keywords)
    
    async def _handle_document_question(self, message: str, client_id: str) -> str:
        """处理文档相关问题"""
        try:
            # 获取最新的文档上下文
            if not self.document_context:
                return "抱歉，我没有找到任何文档内容。请先上传并分析文档。"
            
            # 使用最新的文档内容
            latest_doc_id = max(self.document_context.keys())
            doc_content = self.document_context[latest_doc_id]
            
            if not doc_content.strip():
                return "文档内容为空，无法回答相关问题。"
            
            # 使用 OpenAI 进行问答
            if self.openai_service.is_configured():
                return await self._openai_document_qa(message, doc_content)
            else:
                return await self._simple_document_qa(message, doc_content)
                
        except Exception as e:
            return f"处理文档问题时出错: {str(e)}"
    
    async def _handle_general_conversation(self, message: str, client_id: str) -> str:
        """处理一般对话"""
        try:
            if self.openai_service.is_configured():
                return await self._openai_conversation(message, client_id)
            else:
                return await self._simple_conversation(message)
                
        except Exception as e:
            return f"处理对话时出错: {str(e)}"
    
    async def _openai_document_qa(self, question: str, doc_content: str) -> str:
        """使用 OpenAI 进行文档问答"""
        try:
            # 截取文档内容以适应 token 限制
            max_content_length = 3000
            if len(doc_content) > max_content_length:
                doc_content = doc_content[:max_content_length] + "..."
            
            prompt = f"""
            基于以下文档内容回答用户问题。如果问题无法从文档中找到答案，请明确说明。
            
            文档内容：
            {doc_content}
            
            用户问题：{question}
            
            请提供准确、有用的回答：
            """
            
            response = await self.openai_service.generate_response(prompt)
            return response
            
        except Exception as e:
            return f"AI 问答服务出错: {str(e)}"
    
    async def _openai_conversation(self, message: str, client_id: str) -> str:
        """使用 OpenAI 进行对话"""
        try:
            # 构建对话上下文
            conversation_context = ""
            history = self.conversation_history.get(client_id, [])
            
            # 包含最近的对话历史
            for msg in history[-6:]:  # 最近3轮对话
                role = "用户" if msg["role"] == "user" else "助手"
                conversation_context += f"{role}: {msg['content']}\n"
            
            prompt = f"""
            你是一个智能文档分析助手，可以帮助用户分析文档、回答问题、提供建议。
            请以友好、专业的方式回应用户。
            
            对话历史：
            {conversation_context}
            
            用户: {message}
            助手:
            """
            
            response = await self.openai_service.generate_response(prompt)
            return response
            
        except Exception as e:
            return f"AI 对话服务出错: {str(e)}"
    
    async def _simple_document_qa(self, question: str, doc_content: str) -> str:
        """简单的文档问答（无 AI 服务时的备选方案）"""
        question_lower = question.lower()
        doc_content_lower = doc_content.lower()
        
        # 简单的关键词匹配
        if "摘要" in question_lower or "summary" in question_lower:
            # 返回文档开头部分作为摘要
            summary = doc_content[:300] + "..." if len(doc_content) > 300 else doc_content
            return f"文档摘要：\n{summary}"
        
        elif "长度" in question_lower or "多长" in question_lower:
            word_count = len(doc_content.split())
            char_count = len(doc_content)
            return f"文档包含约 {word_count} 个词，{char_count} 个字符。"
        
        elif "关键词" in question_lower or "keyword" in question_lower:
            # 简单的词频统计
            words = doc_content.lower().split()
            word_freq = {}
            for word in words:
                if len(word) > 3 and word.isalnum():
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
            keywords = [word for word, freq in top_words]
            return f"文档的主要关键词：{', '.join(keywords)}"
        
        else:
            # 尝试在文档中查找相关内容
            sentences = doc_content.split('.')
            relevant_sentences = []
            
            for sentence in sentences:
                if any(word in sentence.lower() for word in question_lower.split()):
                    relevant_sentences.append(sentence.strip())
            
            if relevant_sentences:
                return f"相关内容：\n{'. '.join(relevant_sentences[:3])}"
            else:
                return "抱歉，我在文档中没有找到与您问题相关的内容。"
    
    async def _simple_conversation(self, message: str) -> str:
        """简单对话（无 AI 服务时的备选方案）"""
        message_lower = message.lower()
        
        # 预定义的回复模式
        if any(greeting in message_lower for greeting in ["你好", "hello", "hi"]):
            return "您好！我是智能文档分析助手，可以帮您分析文档、回答问题。请问有什么可以帮助您的吗？"
        
        elif any(thanks in message_lower for thanks in ["谢谢", "thank"]):
            return "不客气！如果您还有其他问题，随时可以问我。"
        
        elif "帮助" in message_lower or "help" in message_lower:
            return """我可以帮助您：
1. 分析上传的文档内容
2. 回答关于文档的问题
3. 提取关键词和摘要
4. 进行情感分析和主题检测

请上传文档或询问相关问题！"""
        
        elif "功能" in message_lower or "能做什么" in message_lower:
            return f"我具备以下能力：{', '.join(self.capabilities)}。您可以上传文档让我分析，或者询问任何相关问题。"
        
        else:
            return "我理解您的问题。不过为了更好地帮助您，建议您上传文档进行分析，或者询问具体的文档相关问题。"
    
    async def _answer_question(self, question: str, doc_id: str = None) -> str:
        """回答问题"""
        if doc_id and doc_id in self.document_context:
            return await self._simple_document_qa(question, self.document_context[doc_id])
        else:
            return await self._simple_conversation(question)