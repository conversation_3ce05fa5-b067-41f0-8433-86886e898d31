"""
智能体管理器 - 管理所有智能体的生命周期和通信
"""

import asyncio
from typing import Dict, List, Any
from datetime import datetime

from agents.base_agent import BaseAgent
from agents.document_parser_agent import DocumentParserAgent
from agents.content_analyzer_agent import ContentAnalyzerAgent
from agents.chat_agent import ChatAgent
from agents.summary_generator_agent import SummaryGeneratorAgent


class CoordinatorAgent(BaseAgent):
    """协调智能体 - 管理任务分配和工作流"""
    
    def __init__(self):
        super().__init__("coordinator", "system_coordinator")
        self.add_capability("task_coordination")
        self.add_capability("workflow_management")
        self.add_capability("resource_allocation")
        
        self.active_workflows: Dict[str, Dict] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
    
    async def process_message(self, message):
        """处理消息"""
        message_type = message.message_type
        content = message.content
        sender = message.sender
        
        if message_type == "workflow_request":
            # 处理工作流请求
            workflow_type = content.get("workflow_type")
            workflow_data = content.get("data", {})
            
            workflow_id = await self._start_workflow(workflow_type, workflow_data, sender)
            
            await self.send_message(
                sender,
                "workflow_started",
                {"workflow_id": workflow_id, "status": "started"}
            )
        
        elif message_type == "task_completed":
            # 处理任务完成通知
            workflow_id = content.get("workflow_id")
            task_result = content.get("result")
            
            await self._handle_task_completion(workflow_id, sender, task_result)
    
    async def execute_ui_command(self, command: str, parameters: Dict) -> Any:
        """执行 UI 命令"""
        if command == "start_document_analysis":
            doc_id = parameters.get("document_id")
            return await self._coordinate_document_analysis(doc_id)
        
        elif command == "get_workflow_status":
            workflow_id = parameters.get("workflow_id")
            return self.active_workflows.get(workflow_id, {"status": "not_found"})
        
        else:
            raise ValueError(f"不支持的命令: {command}")
    
    async def _start_workflow(self, workflow_type: str, data: Dict, requester: str) -> str:
        """启动工作流"""
        workflow_id = f"workflow_{int(datetime.now().timestamp() * 1000)}"
        
        workflow = {
            "id": workflow_id,
            "type": workflow_type,
            "status": "running",
            "requester": requester,
            "data": data,
            "started_at": datetime.now().isoformat(),
            "steps": [],
            "current_step": 0
        }
        
        self.active_workflows[workflow_id] = workflow
        
        if workflow_type == "document_analysis":
            await self._coordinate_document_analysis_workflow(workflow_id, data)
        
        return workflow_id
    
    async def _coordinate_document_analysis_workflow(self, workflow_id: str, data: Dict):
        """协调文档分析工作流"""
        workflow = self.active_workflows[workflow_id]
        doc_id = data.get("document_id")
        
        try:
            # 步骤1: 文档解析
            workflow["steps"].append({"step": "parsing", "status": "running"})
            await self.send_message(
                "document_parser",
                "parse_document_request",
                {"document_id": doc_id, "workflow_id": workflow_id}
            )
            
        except Exception as e:
            workflow["status"] = "failed"
            workflow["error"] = str(e)
    
    async def _handle_task_completion(self, workflow_id: str, agent_id: str, result: Dict):
        """处理任务完成"""
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        current_step = workflow["current_step"]
        
        if current_step < len(workflow["steps"]):
            workflow["steps"][current_step]["status"] = "completed"
            workflow["steps"][current_step]["result"] = result
            workflow["current_step"] += 1
            
            # 继续下一步
            await self._continue_workflow(workflow_id)
    
    async def _continue_workflow(self, workflow_id: str):
        """继续工作流执行"""
        workflow = self.active_workflows[workflow_id]
        
        if workflow["type"] == "document_analysis":
            await self._continue_document_analysis(workflow_id)
    
    async def _continue_document_analysis(self, workflow_id: str):
        """继续文档分析工作流"""
        workflow = self.active_workflows[workflow_id]
        current_step = workflow["current_step"]
        
        if current_step == 1:  # 解析完成，开始分析
            workflow["steps"].append({"step": "analysis", "status": "running"})
            
            parse_result = workflow["steps"][0]["result"]
            await self.send_message(
                "content_analyzer",
                "analyze_content_request",
                {
                    "document_id": workflow["data"]["document_id"],
                    "text_content": parse_result.get("parsed_content", ""),
                    "workflow_id": workflow_id
                }
            )
        
        elif current_step == 2:  # 分析完成，生成摘要
            workflow["steps"].append({"step": "summary", "status": "running"})
            
            analysis_result = workflow["steps"][1]["result"]
            await self.send_message(
                "summary_generator",
                "generate_summary_request",
                {
                    "document_id": workflow["data"]["document_id"],
                    "text_content": analysis_result.get("text_content", ""),
                    "workflow_id": workflow_id
                }
            )
        
        elif current_step == 3:  # 所有步骤完成
            workflow["status"] = "completed"
            workflow["completed_at"] = datetime.now().isoformat()
            
            # 通知请求者工作流完成
            await self.send_message(
                workflow["requester"],
                "workflow_completed",
                {
                    "workflow_id": workflow_id,
                    "results": {
                        "parsing": workflow["steps"][0]["result"],
                        "analysis": workflow["steps"][1]["result"],
                        "summary": workflow["steps"][2]["result"]
                    }
                }
            )


class AgentManager:
    """智能体管理器"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.is_initialized = False
    
    async def initialize(self):
        """初始化所有智能体"""
        if self.is_initialized:
            return
        
        print("初始化智能体系统...")
        
        # 创建智能体实例
        self.agents = {
            "coordinator": CoordinatorAgent(),
            "document_parser": DocumentParserAgent(),
            "content_analyzer": ContentAnalyzerAgent(),
            "chat_agent": ChatAgent(),
            "summary_generator": SummaryGeneratorAgent()
        }
        
        # 设置智能体注册表（用于 A2A 通信）
        for agent in self.agents.values():
            agent.set_agent_registry(self.agents)
        
        # 启动所有智能体
        for agent_id, agent in self.agents.items():
            await agent.start()
            print(f"✅ {agent_id} 智能体已启动")
        
        self.is_initialized = True
        print("🎉 智能体系统初始化完成")
    
    async def shutdown(self):
        """关闭所有智能体"""
        if not self.is_initialized:
            return
        
        print("关闭智能体系统...")
        
        for agent_id, agent in self.agents.items():
            await agent.stop()
            print(f"🛑 {agent_id} 智能体已停止")
        
        self.is_initialized = False
        print("智能体系统已关闭")
    
    def get_agent(self, agent_id: str) -> BaseAgent:
        """获取指定智能体"""
        return self.agents.get(agent_id)
    
    def get_all_agents(self) -> Dict[str, BaseAgent]:
        """获取所有智能体"""
        return self.agents.copy()
    
    def get_agents_status(self) -> Dict[str, Dict]:
        """获取所有智能体状态"""
        status = {}
        for agent_id, agent in self.agents.items():
            status[agent_id] = agent.get_status()
        return status
    
    async def broadcast_message(self, message_type: str, content: Any, sender_id: str = "system"):
        """广播消息到所有智能体"""
        tasks = []
        for agent_id, agent in self.agents.items():
            if agent_id != sender_id:
                task = agent.receive_message(
                    type("Message", (), {
                        "sender": sender_id,
                        "receiver": agent_id,
                        "message_type": message_type,
                        "content": content,
                        "timestamp": datetime.now().isoformat()
                    })()
                )
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_message_to_agent(self, agent_id: str, message_type: str, content: Any, sender_id: str = "system"):
        """发送消息到指定智能体"""
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            await agent.receive_message(
                type("Message", (), {
                    "sender": sender_id,
                    "receiver": agent_id,
                    "message_type": message_type,
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                })()
            )
            return True
        return False
    
    def get_agent_capabilities(self) -> Dict[str, List[str]]:
        """获取所有智能体的能力"""
        capabilities = {}
        for agent_id, agent in self.agents.items():
            capabilities[agent_id] = agent.capabilities
        return capabilities
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health_status = {}
        for agent_id, agent in self.agents.items():
            try:
                # 简单的健康检查 - 检查智能体是否在运行
                health_status[agent_id] = agent.is_running and agent.status.value != "error"
            except Exception:
                health_status[agent_id] = False
        
        return health_status
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        total_sent = sum(len(agent.sent_messages) for agent in self.agents.values())
        total_received = sum(len(agent.received_messages) for agent in self.agents.values())
        total_queue_size = sum(agent.message_queue.qsize() for agent in self.agents.values())
        
        return {
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.is_running]),
            "total_messages_sent": total_sent,
            "total_messages_received": total_received,
            "total_queue_size": total_queue_size,
            "system_uptime": "运行中" if self.is_initialized else "未启动",
            "last_updated": datetime.now().isoformat()
        }