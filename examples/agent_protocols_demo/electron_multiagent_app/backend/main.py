"""
多智能体文档分析助手 - 后端主服务
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from agents.agent_manager import AgentManager
from protocols.ag_ui_protocol import AGUIProtocol
from services.document_service import DocumentService
from services.openai_service import OpenAIService


# 数据模型
class ConfigModel(BaseModel):
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    max_tokens: int = 2000
    temperature: float = 0.7


class MessageModel(BaseModel):
    type: str
    content: str
    timestamp: Optional[str] = None


# FastAPI 应用
app = FastAPI(title="多智能体文档分析助手", version="1.0.0")

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例
agent_manager = AgentManager()
ag_ui_protocol = AGUIProtocol()
document_service = DocumentService()
openai_service = OpenAIService()

# WebSocket 连接管理
active_connections: Dict[str, WebSocket] = {}


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("🚀 启动多智能体文档分析助手...")
    
    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("outputs", exist_ok=True)
    
    # 初始化智能体管理器
    await agent_manager.initialize()
    
    # 注册智能体到 AG-UI 协议
    for agent_id, agent in agent_manager.agents.items():
        ag_ui_protocol.register_agent(agent_id, agent)
    
    print("✅ 系统初始化完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("🛑 关闭多智能体系统...")
    await agent_manager.shutdown()


# WebSocket 端点
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket 连接端点"""
    await websocket.accept()
    active_connections[client_id] = websocket
    
    try:
        # 发送欢迎消息
        await websocket.send_json({
            "type": "system",
            "content": "连接成功！智能体系统已就绪。",
            "timestamp": datetime.now().isoformat(),
            "agents_status": agent_manager.get_agents_status()
        })
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()
            message_type = data.get("type")
            content = data.get("content", "")
            
            if message_type == "chat":
                # 处理对话消息
                response = await handle_chat_message(client_id, content)
                await websocket.send_json(response)
                
            elif message_type == "command":
                # 处理命令消息
                response = await handle_command_message(client_id, data)
                await websocket.send_json(response)
                
            elif message_type == "config":
                # 处理配置更新
                response = await handle_config_update(data.get("config", {}))
                await websocket.send_json(response)
                
    except WebSocketDisconnect:
        if client_id in active_connections:
            del active_connections[client_id]
        print(f"客户端 {client_id} 断开连接")
    except Exception as e:
        print(f"WebSocket 错误: {e}")
        if client_id in active_connections:
            del active_connections[client_id]


async def handle_chat_message(client_id: str, content: str) -> Dict:
    """处理对话消息"""
    try:
        # 使用对话智能体处理消息
        response = await ag_ui_protocol.send_command_to_agent(
            "chat_agent",
            "process_message",
            {"message": content, "client_id": client_id}
        )
        
        return {
            "type": "chat_response",
            "content": response.get("response", "抱歉，我无法理解您的问题。"),
            "timestamp": datetime.now().isoformat(),
            "agent": "chat_agent"
        }
        
    except Exception as e:
        return {
            "type": "error",
            "content": f"处理消息时出错: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def handle_command_message(client_id: str, data: Dict) -> Dict:
    """处理命令消息"""
    try:
        command = data.get("command")
        parameters = data.get("parameters", {})
        
        if command == "analyze_document":
            # 分析文档命令
            doc_id = parameters.get("document_id")
            if not doc_id:
                raise ValueError("缺少文档ID")
            
            # 启动文档分析工作流
            result = await start_document_analysis_workflow(doc_id, client_id)
            return result
            
        elif command == "get_analysis_status":
            # 获取分析状态
            doc_id = parameters.get("document_id")
            status = await get_document_analysis_status(doc_id)
            return {
                "type": "analysis_status",
                "content": status,
                "timestamp": datetime.now().isoformat()
            }
            
        else:
            return {
                "type": "error",
                "content": f"未知命令: {command}",
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        return {
            "type": "error",
            "content": f"处理命令时出错: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def handle_config_update(config: Dict) -> Dict:
    """处理配置更新"""
    try:
        # 更新 OpenAI 服务配置
        if "openai_api_key" in config:
            openai_service.set_api_key(config["openai_api_key"])
        
        if "openai_model" in config:
            openai_service.set_model(config["openai_model"])
        
        # 更新其他配置...
        
        return {
            "type": "config_updated",
            "content": "配置更新成功",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "type": "error",
            "content": f"更新配置时出错: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def start_document_analysis_workflow(doc_id: str, client_id: str) -> Dict:
    """启动文档分析工作流"""
    try:
        # 通知客户端开始分析
        await broadcast_to_client(client_id, {
            "type": "workflow_started",
            "content": "开始文档分析工作流...",
            "document_id": doc_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 步骤1: 文档解析
        parse_result = await ag_ui_protocol.send_command_to_agent(
            "document_parser",
            "parse_document",
            {"document_id": doc_id}
        )
        
        await broadcast_to_client(client_id, {
            "type": "workflow_progress",
            "content": "文档解析完成",
            "step": "parsing",
            "result": parse_result,
            "timestamp": datetime.now().isoformat()
        })
        
        # 步骤2: 内容分析
        analysis_result = await ag_ui_protocol.send_command_to_agent(
            "content_analyzer",
            "analyze_content",
            {"document_id": doc_id, "parsed_content": parse_result}
        )
        
        await broadcast_to_client(client_id, {
            "type": "workflow_progress",
            "content": "内容分析完成",
            "step": "analysis",
            "result": analysis_result,
            "timestamp": datetime.now().isoformat()
        })
        
        # 步骤3: 摘要生成
        summary_result = await ag_ui_protocol.send_command_to_agent(
            "summary_generator",
            "generate_summary",
            {"document_id": doc_id, "analysis": analysis_result}
        )
        
        await broadcast_to_client(client_id, {
            "type": "workflow_completed",
            "content": "文档分析完成",
            "document_id": doc_id,
            "results": {
                "parsing": parse_result,
                "analysis": analysis_result,
                "summary": summary_result
            },
            "timestamp": datetime.now().isoformat()
        })
        
        return {
            "type": "workflow_started",
            "content": "文档分析工作流已启动",
            "document_id": doc_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        await broadcast_to_client(client_id, {
            "type": "workflow_error",
            "content": f"工作流执行出错: {str(e)}",
            "document_id": doc_id,
            "timestamp": datetime.now().isoformat()
        })
        
        return {
            "type": "error",
            "content": f"启动工作流时出错: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def get_document_analysis_status(doc_id: str) -> Dict:
    """获取文档分析状态"""
    # 这里可以查询数据库或缓存获取状态
    return {
        "document_id": doc_id,
        "status": "completed",
        "progress": 100,
        "steps_completed": ["parsing", "analysis", "summary"],
        "last_updated": datetime.now().isoformat()
    }


async def broadcast_to_client(client_id: str, message: Dict):
    """向特定客户端广播消息"""
    if client_id in active_connections:
        try:
            await active_connections[client_id].send_json(message)
        except Exception as e:
            print(f"广播消息失败: {e}")


# REST API 端点
@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传端点"""
    try:
        # 保存上传的文件
        file_path = await document_service.save_uploaded_file(file)
        
        # 生成文档ID
        doc_id = document_service.generate_document_id(file.filename)
        
        # 注册文档
        document_service.register_document(doc_id, file_path, file.filename)
        
        return {
            "success": True,
            "document_id": doc_id,
            "filename": file.filename,
            "file_path": str(file_path),
            "message": "文件上传成功"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@app.get("/api/documents")
async def get_documents():
    """获取文档列表"""
    try:
        documents = document_service.get_all_documents()
        return {"success": True, "documents": documents}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@app.get("/api/agents/status")
async def get_agents_status():
    """获取智能体状态"""
    try:
        status = agent_manager.get_agents_status()
        return {"success": True, "agents": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取智能体状态失败: {str(e)}")


@app.post("/api/config")
async def update_config(config: ConfigModel):
    """更新系统配置"""
    try:
        # 更新 OpenAI 配置
        if config.openai_api_key:
            openai_service.set_api_key(config.openai_api_key)
        
        openai_service.set_model(config.openai_model)
        openai_service.set_parameters(
            max_tokens=config.max_tokens,
            temperature=config.temperature
        )
        
        return {"success": True, "message": "配置更新成功"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动多智能体文档分析助手后端服务...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )