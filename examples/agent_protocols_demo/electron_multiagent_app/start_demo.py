#!/usr/bin/env python3
"""
多智能体文档分析助手演示启动脚本
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
from pathlib import Path


def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    print(f"✅ Python 版本: {sys.version}")
    return True


def check_node_version():
    """检查 Node.js 版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js 版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js 未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return False


def install_python_dependencies():
    """安装 Python 依赖"""
    print("📦 安装 Python 依赖...")
    
    backend_dir = Path(__file__).parent / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True, cwd=backend_dir)
        print("✅ Python 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Python 依赖安装失败: {e}")
        return False


def install_node_dependencies():
    """安装 Node.js 依赖"""
    print("📦 安装 Node.js 依赖...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    package_json = frontend_dir / "package.json"
    
    if not package_json.exists():
        print("❌ package.json 文件不存在")
        return False
    
    try:
        subprocess.run(["npm", "install"], check=True, cwd=frontend_dir)
        print("✅ Node.js 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Node.js 依赖安装失败: {e}")
        return False


def start_backend_server():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    backend_dir = Path(__file__).parent / "backend"
    main_py = backend_dir / "main.py"
    
    if not main_py.exists():
        print("❌ backend/main.py 文件不存在")
        return None
    
    try:
        process = subprocess.Popen([
            sys.executable, str(main_py)
        ], cwd=backend_dir)
        
        # 等待服务启动
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 后端服务启动成功")
            return process
        else:
            print("❌ 后端服务启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None


def start_electron_app():
    """启动 Electron 应用"""
    print("🖥️ 启动 Electron 应用...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    
    try:
        process = subprocess.Popen([
            "npm", "start"
        ], cwd=frontend_dir)
        
        print("✅ Electron 应用启动成功")
        return process
        
    except Exception as e:
        print(f"❌ 启动 Electron 应用失败: {e}")
        return None


def start_web_demo():
    """启动 Web 演示（备选方案）"""
    print("🌐 启动 Web 演示...")
    
    # 等待后端服务完全启动
    time.sleep(2)
    
    # 打开浏览器
    webbrowser.open("http://localhost:8000")
    print("✅ Web 演示已在浏览器中打开")


def create_simple_web_interface():
    """创建简单的 Web 界面"""
    web_dir = Path(__file__).parent / "web_demo"
    web_dir.mkdir(exist_ok=True)
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多智能体文档分析助手 - Web 演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .demo-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .upload-area:hover { border-color: #007bff; }
        .chat-area { height: 300px; border: 1px solid #ddd; padding: 10px; overflow-y: auto; margin: 10px 0; }
        .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .message.user { background: #e3f2fd; text-align: right; }
        .message.assistant { background: #f5f5f5; }
        #chatInput { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 多智能体文档分析助手</h1>
            <p>基于 A2A 和 AG-UI 协议的智能文档分析系统</p>
            <div id="connectionStatus" class="status error">未连接到后端服务</div>
        </div>
        
        <div class="demo-section">
            <h3>📄 文档上传</h3>
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <p>点击此处上传文档</p>
                <p><small>支持 PDF, DOCX, TXT 格式</small></p>
            </div>
            <input type="file" id="fileInput" accept=".pdf,.docx,.doc,.txt" style="display: none;">
            <div id="uploadStatus"></div>
        </div>
        
        <div class="demo-section">
            <h3>💬 智能对话</h3>
            <div id="chatArea" class="chat-area">
                <div class="message assistant">
                    <strong>助手:</strong> 您好！我是智能文档分析助手。请上传文档或直接与我对话。
                </div>
            </div>
            <input type="text" id="chatInput" placeholder="输入您的问题...">
            <button class="btn" onclick="sendMessage()">发送</button>
        </div>
        
        <div class="demo-section">
            <h3>🤖 智能体状态</h3>
            <div id="agentStatus">加载中...</div>
            <button class="btn" onclick="refreshAgentStatus()">刷新状态</button>
        </div>
        
        <div class="demo-section">
            <h3>⚙️ 系统配置</h3>
            <div>
                <label>OpenAI API Key:</label>
                <input type="password" id="apiKey" placeholder="输入您的 API Key">
                <button class="btn" onclick="updateConfig()">更新配置</button>
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8000';
        let ws = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            connectWebSocket();
            refreshAgentStatus();
        });
        
        // 检查连接
        async function checkConnection() {
            try {
                const response = await fetch(API_BASE + '/api/agents/status');
                if (response.ok) {
                    document.getElementById('connectionStatus').innerHTML = '<span class="status success">✅ 已连接到后端服务</span>';
                }
            } catch (error) {
                document.getElementById('connectionStatus').innerHTML = '<span class="status error">❌ 无法连接到后端服务</span>';
            }
        }
        
        // WebSocket 连接
        function connectWebSocket() {
            try {
                ws = new WebSocket('ws://localhost:8000/ws/web_demo');
                ws.onopen = function() {
                    console.log('WebSocket 连接已建立');
                };
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };
            } catch (error) {
                console.error('WebSocket 连接失败:', error);
            }
        }
        
        // 处理 WebSocket 消息
        function handleWebSocketMessage(message) {
            if (message.type === 'chat_response') {
                addChatMessage('assistant', message.content);
            }
        }
        
        // 发送消息
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;
            
            addChatMessage('user', message);
            input.value = '';
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'chat',
                    content: message
                }));
            }
        }
        
        // 添加聊天消息
        function addChatMessage(role, content) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + role;
            messageDiv.innerHTML = '<strong>' + (role === 'user' ? '用户' : '助手') + ':</strong> ' + content;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 刷新智能体状态
        async function refreshAgentStatus() {
            try {
                const response = await fetch(API_BASE + '/api/agents/status');
                const data = await response.json();
                
                if (data.success) {
                    let html = '<ul>';
                    Object.entries(data.agents).forEach(([id, agent]) => {
                        html += '<li><strong>' + agent.type + '</strong>: ' + agent.status + '</li>';
                    });
                    html += '</ul>';
                    document.getElementById('agentStatus').innerHTML = html;
                } else {
                    document.getElementById('agentStatus').innerHTML = '获取状态失败';
                }
            } catch (error) {
                document.getElementById('agentStatus').innerHTML = '连接失败';
            }
        }
        
        // 文件上传
        document.getElementById('fileInput').addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                document.getElementById('uploadStatus').innerHTML = '<div class="status">上传中...</div>';
                
                const response = await fetch(API_BASE + '/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('uploadStatus').innerHTML = '<div class="status success">✅ 文件上传成功: ' + result.filename + '</div>';
                    
                    // 自动开始分析
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'command',
                            command: 'analyze_document',
                            parameters: { document_id: result.document_id }
                        }));
                    }
                } else {
                    document.getElementById('uploadStatus').innerHTML = '<div class="status error">❌ 上传失败</div>';
                }
            } catch (error) {
                document.getElementById('uploadStatus').innerHTML = '<div class="status error">❌ 上传出错</div>';
            }
        });
        
        // 更新配置
        async function updateConfig() {
            const apiKey = document.getElementById('apiKey').value;
            if (!apiKey) return;
            
            try {
                const response = await fetch(API_BASE + '/api/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ openai_api_key: apiKey })
                });
                
                if (response.ok) {
                    alert('配置更新成功');
                } else {
                    alert('配置更新失败');
                }
            } catch (error) {
                alert('配置更新出错');
            }
        }
        
        // 回车发送消息
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
    """
    
    web_file = web_dir / "index.html"
    web_file.write_text(html_content, encoding='utf-8')
    
    return web_file


def main():
    """主函数"""
    print("🤖 多智能体文档分析助手演示启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_python_version():
        return
    
    has_node = check_node_version()
    
    # 安装依赖
    print("\n📦 安装依赖...")
    if not install_python_dependencies():
        print("❌ Python 依赖安装失败，请手动安装")
        return
    
    if has_node:
        install_node_dependencies()
    
    # 启动后端服务
    print("\n🚀 启动服务...")
    backend_process = start_backend_server()
    
    if not backend_process:
        print("❌ 后端服务启动失败")
        return
    
    try:
        # 尝试启动 Electron 应用
        if has_node:
            print("\n🖥️ 尝试启动 Electron 应用...")
            electron_process = start_electron_app()
            
            if electron_process:
                print("✅ Electron 应用已启动")
                print("\n🎉 演示启动完成！")
                print("- Electron 应用已打开")
                print("- 后端服务运行在 http://localhost:8000")
                
                # 等待 Electron 应用结束
                electron_process.wait()
            else:
                print("⚠️ Electron 应用启动失败，启动 Web 演示")
                create_simple_web_interface()
                start_web_demo()
        else:
            print("⚠️ Node.js 未安装，启动 Web 演示")
            create_simple_web_interface()
            start_web_demo()
        
        if not has_node:
            print("\n🎉 Web 演示启动完成！")
            print("- 浏览器已打开演示页面")
            print("- 后端服务运行在 http://localhost:8000")
            print("\n按 Ctrl+C 停止服务")
            
            # 保持后端服务运行
            backend_process.wait()
    
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
    
    finally:
        # 清理进程
        if backend_process and backend_process.poll() is None:
            backend_process.terminate()
            print("✅ 后端服务已停止")


if __name__ == "__main__":
    main()