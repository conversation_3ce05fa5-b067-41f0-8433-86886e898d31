{"name": "multiagent-document-analyzer", "version": "1.0.0", "description": "多智能体文档分析助手 - Electron 前端", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "multiagent", "document", "analysis", "ai"], "author": "Agent Protocol Demo", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"ws": "^8.14.2"}, "build": {"appId": "com.example.multiagent-analyzer", "productName": "多智能体文档分析助手", "directories": {"output": "dist"}, "files": ["main.js", "renderer/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}