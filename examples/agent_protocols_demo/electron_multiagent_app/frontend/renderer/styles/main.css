/* 主样式文件 */

:root {
    /* 颜色变量 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;
    --bg-darker: #0f172a;
    
    /* 文本颜色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-light: #ffffff;
    
    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --border-focus: #2563eb;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* 过渡 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: var(--font-family);
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow: hidden;
}

/* 应用容器 */
#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    min-height: 48px;
    -webkit-app-region: drag;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    -webkit-app-region: no-drag;
}

.app-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--error-color);
    transition: background-color var(--transition-fast);
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.connecting {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 300px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.sidebar section {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.sidebar h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* 内容区域 */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 标签页导航 */
.tab-nav {
    display: flex;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: var(--bg-primary);
}

/* 标签页内容 */
.tab-content {
    flex: 1;
    display: none;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-secondary);
    min-height: 24px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    user-select: none;
}

.btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
}

.btn-icon {
    padding: var(--spacing-xs);
    width: 32px;
    height: 32px;
}

/* 表单元素 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.form-group small {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-muted);
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90%;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
}

.modal-body {
    padding: var(--spacing-md);
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* 加载指示器 */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

/* 通知 */
.notification-container {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    z-index: 1500;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    background-color: var(--success-color);
    color: var(--text-light);
}

.notification.error {
    background-color: var(--error-color);
    color: var(--text-light);
}

.notification.warning {
    background-color: var(--warning-color);
    color: var(--text-light);
}

.notification.info {
    background-color: var(--info-color);
    color: var(--text-light);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state p {
    margin-bottom: var(--spacing-sm);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-hover);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 250px;
    }
    
    .toolbar {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .app-title {
        font-size: 14px;
    }
}

/* 打印样式 */
@media print {
    .toolbar,
    .sidebar,
    .status-bar,
    .modal,
    .loading-overlay,
    .notification-container {
        display: none !important;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .content-area {
        width: 100%;
    }
}