/* 组件样式 */

/* 上传区域 */
.upload-section {
    flex-shrink: 0;
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    background-color: var(--bg-primary);
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.upload-placeholder {
    color: var(--text-secondary);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
}

.upload-hint {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 文档列表 */
.document-list {
    flex: 1;
    overflow-y: auto;
}

.document-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.document-item {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.document-item:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.document-item.active {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.document-name {
    font-weight: 500;
    font-size: 13px;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.document-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.document-status.uploaded {
    background-color: var(--info-color);
    color: white;
}

.document-status.processing {
    background-color: var(--warning-color);
    color: white;
}

.document-status.completed {
    background-color: var(--success-color);
    color: white;
}

.document-status.error {
    background-color: var(--error-color);
    color: white;
}

.document-details {
    font-size: 11px;
    color: var(--text-muted);
}

/* 智能体状态 */
.agent-status {
    flex-shrink: 0;
}

.agent-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.agent-item {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
}

.agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.agent-name {
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
}

.agent-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.agent-details small {
    font-size: 10px;
    color: var(--text-muted);
}

/* 聊天界面 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
}

.welcome-message {
    margin-bottom: var(--spacing-lg);
}

.message {
    margin-bottom: var(--spacing-md);
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message.system {
    align-items: center;
}

.message-content {
    max-width: 80%;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 14px;
    line-height: 1.4;
}

.message.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: var(--radius-sm);
}

.message.assistant .message-content {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: var(--radius-sm);
}

.message.system .message-content {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    text-align: center;
    max-width: 90%;
}

.message-content ul {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-md);
}

.message-content li {
    margin-bottom: var(--spacing-xs);
}

.message-timestamp {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 聊天输入区域 */
.chat-input-area {
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
}

.input-container {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.input-container textarea {
    flex: 1;
    resize: none;
    min-height: 40px;
    max-height: 120px;
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
}

.input-actions {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-sm);
}

/* 分析结果 */
.analysis-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-md);
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.analysis-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.analysis-content {
    flex: 1;
    overflow-y: auto;
}

.analysis-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--bg-secondary);
}

.analysis-section h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 16px;
}

.analysis-section h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 14px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.keyword-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
}

.sentiment-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
}

.sentiment-indicator.positive {
    background-color: var(--success-color);
    color: white;
}

.sentiment-indicator.negative {
    background-color: var(--error-color);
    color: white;
}

.sentiment-indicator.neutral {
    background-color: var(--secondary-color);
    color: white;
}

/* 工作流 */
.workflow-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-md);
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.workflow-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.workflow-content {
    flex: 1;
    overflow-y: auto;
}

.workflow-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.workflow-step {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--bg-secondary);
    position: relative;
}

.workflow-step.active {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.workflow-step.completed {
    border-color: var(--success-color);
    background-color: rgba(16, 185, 129, 0.05);
}

.workflow-step.error {
    border-color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.05);
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    font-size: 18px;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.workflow-step.active .step-icon {
    background-color: var(--primary-color);
    color: white;
}

.workflow-step.completed .step-icon {
    background-color: var(--success-color);
    color: white;
}

.workflow-step.error .step-icon {
    background-color: var(--error-color);
    color: white;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.step-description {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.step-status {
    font-size: 12px;
    color: var(--text-muted);
}

.step-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    transition: width var(--transition-normal);
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.loading::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-sm);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .message-content {
        max-width: 95%;
    }
    
    .input-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .workflow-step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-icon {
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }
}