/* 动画样式 */

/* 关键帧动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scaleOut {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

@keyframes modalSlideIn {
    0% {
        transform: translateY(-50px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
    100% { box-shadow: 0 0 5px var(--primary-color); }
}

@keyframes typing {
    0% { width: 0; }
    100% { width: 100%; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 动画类 */
.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
}

.animate-scale-out {
    animation: scaleOut 0.2s ease-out;
}

/* 过渡效果 */
.transition-all {
    transition: all var(--transition-normal);
}

.transition-colors {
    transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-opacity {
    transition: opacity var(--transition-fast);
}

.transition-transform {
    transition: transform var(--transition-fast);
}

/* 悬停效果 */
.hover-lift {
    transition: transform var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-fast);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* 加载动画 */
.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink 0.75s step-end infinite;
}

/* 进度条动画 */
.progress-bar {
    position: relative;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-md);
    transition: width var(--transition-slow);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 消息动画 */
.message-enter {
    animation: slideInUp 0.3s ease-out;
}

.message-exit {
    animation: slideInDown 0.3s ease-out reverse;
}

/* 按钮动画 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 状态指示器动画 */
.status-indicator.connecting {
    animation: pulse 1.5s infinite;
}

.status-indicator.online {
    animation: glow 2s ease-in-out infinite alternate;
}

/* 工作流步骤动画 */
.workflow-step.active .step-icon {
    animation: pulse 2s infinite;
}

.workflow-step.completed .step-icon {
    animation: bounce 0.6s ease-out;
}

.workflow-step.error .step-icon {
    animation: shake 0.5s ease-in-out;
}

/* 文档上传动画 */
.upload-area.drag-over {
    animation: glow 1s ease-in-out infinite alternate;
}

.document-item.uploading {
    animation: pulse 1s infinite;
}

/* 智能体状态动画 */
.agent-item.active {
    animation: glow 2s ease-in-out infinite alternate;
}

/* 通知动画 */
.notification {
    animation: slideInRight 0.3s ease-out;
}

.notification.removing {
    animation: slideInRight 0.3s ease-out reverse;
}

/* 模态框动画 */
.modal.show {
    animation: fadeIn 0.3s ease-out;
}

.modal.show .modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

/* 标签页切换动画 */
.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity var(--transition-normal), transform var(--transition-normal);
}

.tab-content.active {
    opacity: 1;
    transform: translateY(0);
}

/* 聊天消息动画 */
.message {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.3s ease-out forwards;
}

.message.user {
    animation: slideInRight 0.3s ease-out forwards;
}

.message.assistant {
    animation: slideInLeft 0.3s ease-out forwards;
}

/* 关键词标签动画 */
.keyword-tag {
    transition: transform var(--transition-fast);
}

.keyword-tag:hover {
    transform: scale(1.1);
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 深色模式动画调整 */
@media (prefers-color-scheme: dark) {
    .animate-glow {
        animation: glow 2s ease-in-out infinite alternate;
        --glow-color: rgba(59, 130, 246, 0.5);
    }
    
    .hover-glow:hover {
        box-shadow: 0 0 20px var(--glow-color, rgba(59, 130, 246, 0.3));
    }
}