
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试 - 多智能体文档分析助手</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <style>
        .test-section {
            margin: 20px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background-color: var(--bg-secondary);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: var(--radius-md);
        }
        .test-result.success {
            background-color: var(--success-color);
            color: white;
        }
        .test-result.error {
            background-color: var(--error-color);
            color: white;
        }
        .test-result.info {
            background-color: var(--info-color);
            color: white;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-left">
                <h1 class="app-title">🧪 前端测试 - 多智能体文档分析助手</h1>
            </div>
            <div class="toolbar-right">
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator offline"></span>
                    <span class="status-text">测试模式</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 测试结果区域 -->
            <div class="content-area">
                <div class="test-section">
                    <h2>📋 前端模块测试</h2>
                    <div id="testResults">
                        <div class="test-result info">正在加载测试...</div>
                    </div>
                    <button id="runTestsBtn" class="btn btn-primary">运行测试</button>
                </div>

                <div class="test-section">
                    <h2>🎨 UI 组件演示</h2>
                    
                    <h3>按钮组件</h3>
                    <div style="margin: 10px 0;">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-small">小按钮</button>
                        <button class="btn btn-icon">⚙️</button>
                    </div>

                    <h3>通知系统</h3>
                    <div style="margin: 10px 0;">
                        <button class="btn" onclick="showTestNotification('success')">成功通知</button>
                        <button class="btn" onclick="showTestNotification('error')">错误通知</button>
                        <button class="btn" onclick="showTestNotification('warning')">警告通知</button>
                        <button class="btn" onclick="showTestNotification('info')">信息通知</button>
                    </div>

                    <h3>模态框</h3>
                    <div style="margin: 10px 0;">
                        <button class="btn" onclick="showTestModal()">显示模态框</button>
                    </div>

                    <h3>进度条</h3>
                    <div style="margin: 10px 0;">
                        <div class="progress-bar" style="height: 20px;">
                            <div class="progress-fill" id="testProgress" style="width: 0%"></div>
                        </div>
                        <button class="btn" onclick="animateProgress()">动画进度</button>
                    </div>

                    <h3>文档项目</h3>
                    <div class="document-items">
                        <div class="document-item">
                            <div class="document-header">
                                <div class="document-name">📄 测试文档.pdf</div>
                                <div class="document-status completed">已完成</div>
                            </div>
                            <div class="document-details">
                                <span class="document-size">1.2 MB</span>
                                <span class="document-time">2分钟前</span>
                            </div>
                        </div>
                    </div>

                    <h3>智能体状态</h3>
                    <div class="agent-items">
                        <div class="agent-item">
                            <div class="agent-header">
                                <span class="agent-name">文档解析智能体</span>
                                <span class="status-indicator online"></span>
                            </div>
                            <div class="agent-details">
                                <small>状态: 空闲</small>
                                <small>消息: 5/3</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="test-section">
                    <h2>💬 聊天界面演示</h2>
                    <div class="chat-container" style="height: 300px;">
                        <div class="chat-messages" id="testChatMessages">
                            <div class="message system">
                                <div class="message-content">
                                    <p>欢迎使用多智能体文档分析助手！</p>
                                </div>
                            </div>
                            <div class="message user">
                                <div class="message-content">
                                    你好，请帮我分析这个文档。
                                </div>
                            </div>
                            <div class="message assistant">
                                <div class="message-content">
                                    好的，我来帮您分析文档。请稍等片刻...
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <div class="input-container">
                                <textarea placeholder="输入测试消息..." rows="2"></textarea>
                                <button class="btn btn-primary" onclick="addTestMessage()">发送</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-left">
                <span>前端测试模式</span>
            </div>
            <div class="status-right">
                <span>所有模块已加载</span>
            </div>
        </footer>
    </div>

    <!-- 测试模态框 -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>测试模态框</h3>
                <button class="modal-close" onclick="hideTestModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>这是一个测试模态框，用于验证模态框功能是否正常。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideTestModal()">关闭</button>
                <button class="btn btn-primary" onclick="hideTestModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- 脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/api.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/chat.js"></script>
    <script src="js/document.js"></script>
    <script src="js/analysis.js"></script>
    <script src="js/workflow.js"></script>
    <script src="js/config.js"></script>

    <script>
        // 测试应用
        class TestApp {
            constructor() {
                this.config = {
                    backendUrl: 'http://localhost:8000',
                    websocketUrl: 'ws://localhost:8000'
                };
                this.isConnected = false;
                
                this.init();
            }
            
            init() {
                console.log('🧪 初始化测试应用...');
                
                // 初始化 UI 管理器
                this.uiManager = new UIManager(this);
                
                // 运行测试
                this.runTests();
            }
            
            runTests() {
                const results = document.getElementById('testResults');
                results.innerHTML = '';
                
                const tests = [
                    { name: 'Utils 工具函数', test: () => this.testUtils() },
                    { name: 'UI 管理器', test: () => this.testUIManager() },
                    { name: 'WebSocket 管理器', test: () => this.testWebSocketManager() },
                    { name: 'API 客户端', test: () => this.testAPIClient() },
                    { name: 'CSS 样式', test: () => this.testCSS() }
                ];
                
                tests.forEach(({ name, test }) => {
                    try {
                        const result = test();
                        this.addTestResult(name, true, result);
                    } catch (error) {
                        this.addTestResult(name, false, error.message);
                    }
                });
            }
            
            testUtils() {
                if (!window.Utils) throw new Error('Utils 未加载');
                
                const time = Utils.formatTime(new Date());
                const size = Utils.formatFileSize(1024 * 1024);
                const id = Utils.generateId('test');
                
                return `时间格式化: ${time}, 文件大小: ${size}, ID生成: ${id}`;
            }
            
            testUIManager() {
                if (!this.uiManager) throw new Error('UIManager 未初始化');
                
                const stats = this.uiManager.getStats();
                return `通知: ${stats.notifications}, 模态框: ${stats.modals}, 主题: ${stats.theme}`;
            }
            
            testWebSocketManager() {
                if (!window.WebSocketManager) throw new Error('WebSocketManager 未加载');
                
                const ws = new WebSocketManager(this);
                const stats = ws.getStats();
                return `连接状态: ${stats.connected}, 队列消息: ${stats.queuedMessages}`;
            }
            
            testAPIClient() {
                if (!window.MultiAgentAPI) throw new Error('MultiAgentAPI 未加载');
                
                const api = new MultiAgentAPI('http://localhost:8000');
                return `API 客户端已创建，基础URL: ${api.baseURL}`;
            }
            
            testCSS() {
                const testEl = document.createElement('div');
                testEl.className = 'btn btn-primary';
                document.body.appendChild(testEl);
                
                const styles = getComputedStyle(testEl);
                const hasStyles = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
                
                document.body.removeChild(testEl);
                
                if (!hasStyles) throw new Error('CSS 样式未正确加载');
                return 'CSS 样式正常加载';
            }
            
            addTestResult(name, success, message) {
                const results = document.getElementById('testResults');
                const resultEl = document.createElement('div');
                resultEl.className = `test-result ${success ? 'success' : 'error'}`;
                resultEl.innerHTML = `<strong>${name}:</strong> ${message}`;
                results.appendChild(resultEl);
            }
            
            showNotification(message, type = 'info', duration = 3000) {
                if (this.uiManager) {
                    this.uiManager.showNotification(message, type, duration);
                }
            }
        }
        
        // 全局测试函数
        function showTestNotification(type) {
            const messages = {
                success: '这是一个成功通知',
                error: '这是一个错误通知',
                warning: '这是一个警告通知',
                info: '这是一个信息通知'
            };
            
            if (window.testApp) {
                window.testApp.showNotification(messages[type], type);
            }
        }
        
        function showTestModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.classList.add('show');
            }
        }
        
        function hideTestModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.classList.remove('show');
            }
        }
        
        function animateProgress() {
            const progress = document.getElementById('testProgress');
            if (progress) {
                let width = 0;
                const interval = setInterval(() => {
                    width += 2;
                    progress.style.width = width + '%';
                    if (width >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            progress.style.width = '0%';
                        }, 1000);
                    }
                }, 50);
            }
        }
        
        function addTestMessage() {
            const chatMessages = document.getElementById('testChatMessages');
            const messageEl = document.createElement('div');
            messageEl.className = 'message user animate-slide-in-up';
            messageEl.innerHTML = `
                <div class="message-content">
                    这是一条测试消息 - ${new Date().toLocaleTimeString()}
                </div>
            `;
            chatMessages.appendChild(messageEl);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 启动测试应用
        document.addEventListener('DOMContentLoaded', () => {
            window.testApp = new TestApp();
            
            // 绑定测试按钮
            document.getElementById('runTestsBtn').addEventListener('click', () => {
                window.testApp.runTests();
            });
        });
    </script>
</body>
</html>
    