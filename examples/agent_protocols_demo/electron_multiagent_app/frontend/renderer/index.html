<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多智能体文档分析助手</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <div id="app">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-left">
                <h1 class="app-title">🤖 多智能体文档分析助手</h1>
            </div>
            <div class="toolbar-right">
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator offline"></span>
                    <span class="status-text">未连接</span>
                </div>
                <button class="btn btn-icon" id="configBtn" title="设置">
                    <span>⚙️</span>
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <aside class="sidebar">
                <!-- 文档上传区域 -->
                <section class="upload-section">
                    <h3>📄 文档上传</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-placeholder">
                            <div class="upload-icon">📁</div>
                            <p>拖拽文件到此处或点击上传</p>
                            <p class="upload-hint">支持 PDF, DOCX, TXT 格式</p>
                        </div>
                        <input type="file" id="fileInput" accept=".pdf,.docx,.doc,.txt,.md" multiple hidden>
                    </div>
                </section>

                <!-- 文档列表 -->
                <section class="document-list">
                    <h3>📋 文档列表</h3>
                    <div class="document-items" id="documentList">
                        <div class="empty-state">
                            <p>暂无文档</p>
                        </div>
                    </div>
                </section>

                <!-- 智能体状态 -->
                <section class="agent-status">
                    <h3>🤖 智能体状态</h3>
                    <div class="agent-items" id="agentList">
                        <div class="loading">加载中...</div>
                    </div>
                </section>
            </aside>

            <!-- 中央内容区域 -->
            <div class="content-area">
                <!-- 标签页导航 -->
                <nav class="tab-nav">
                    <button class="tab-btn active" data-tab="chat">💬 对话</button>
                    <button class="tab-btn" data-tab="analysis">📊 分析结果</button>
                    <button class="tab-btn" data-tab="workflow">🔄 工作流</button>
                </nav>

                <!-- 对话标签页 -->
                <div class="tab-content active" id="chatTab">
                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="welcome-message">
                                <div class="message system">
                                    <div class="message-content">
                                        <p>👋 欢迎使用多智能体文档分析助手！</p>
                                        <p>您可以：</p>
                                        <ul>
                                            <li>上传文档进行智能分析</li>
                                            <li>与智能体进行自然语言对话</li>
                                            <li>查看详细的分析结果和报告</li>
                                        </ul>
                                        <p>请先上传文档或直接开始对话。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <div class="input-container">
                                <textarea id="chatInput" placeholder="输入您的问题或指令..." rows="2"></textarea>
                                <button id="sendBtn" class="btn btn-primary" disabled>
                                    <span>发送</span>
                                </button>
                            </div>
                            <div class="input-actions">
                                <button class="btn btn-small" id="clearChatBtn">清空对话</button>
                                <button class="btn btn-small" id="exportChatBtn">导出对话</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析结果标签页 -->
                <div class="tab-content" id="analysisTab">
                    <div class="analysis-container">
                        <div class="analysis-header">
                            <h2>📊 文档分析结果</h2>
                            <div class="analysis-actions">
                                <button class="btn btn-small" id="refreshAnalysisBtn">刷新</button>
                                <button class="btn btn-small" id="exportAnalysisBtn">导出</button>
                            </div>
                        </div>
                        <div class="analysis-content" id="analysisContent">
                            <div class="empty-state">
                                <p>暂无分析结果</p>
                                <p>请先上传文档并开始分析</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作流标签页 -->
                <div class="tab-content" id="workflowTab">
                    <div class="workflow-container">
                        <div class="workflow-header">
                            <h2>🔄 智能体工作流</h2>
                            <div class="workflow-actions">
                                <button class="btn btn-small" id="refreshWorkflowBtn">刷新</button>
                            </div>
                        </div>
                        <div class="workflow-content" id="workflowContent">
                            <div class="workflow-steps">
                                <div class="empty-state">
                                    <p>暂无活动工作流</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="statusText">就绪</span>
            </div>
            <div class="status-right">
                <span id="systemInfo">系统信息加载中...</span>
            </div>
        </footer>
    </div>

    <!-- 模态对话框 -->
    <div class="modal" id="configModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚙️ 系统配置</h3>
                <button class="modal-close" id="closeConfigModal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="form-group">
                        <label for="openaiApiKey">OpenAI API Key:</label>
                        <input type="password" id="openaiApiKey" placeholder="输入您的 OpenAI API Key">
                        <small>用于 AI 功能，如智能分析和对话</small>
                    </div>
                    <div class="form-group">
                        <label for="openaiModel">AI 模型:</label>
                        <select id="openaiModel">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo-preview">GPT-4 Turbo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="maxTokens">最大 Token 数:</label>
                        <input type="number" id="maxTokens" value="2000" min="100" max="4000">
                    </div>
                    <div class="form-group">
                        <label for="temperature">创造性 (Temperature):</label>
                        <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                        <span id="temperatureValue">0.7</span>
                    </div>
                    <div class="form-group">
                        <label for="backendUrl">后端服务地址:</label>
                        <input type="url" id="backendUrl" value="http://localhost:8000">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelConfigBtn">取消</button>
                <button class="btn btn-primary" id="saveConfigBtn">保存</button>
                <button class="btn btn-small" id="testConnectionBtn">测试连接</button>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p id="loadingText">处理中...</p>
        </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- 脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/api.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/chat.js"></script>
    <script src="js/document.js"></script>
    <script src="js/analysis.js"></script>
    <script src="js/workflow.js"></script>
    <script src="js/config.js"></script>
    <script src="js/main.js"></script>
</body>
</html>