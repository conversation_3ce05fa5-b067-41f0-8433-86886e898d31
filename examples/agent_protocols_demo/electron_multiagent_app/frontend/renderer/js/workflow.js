/**
 * 工作流管理模块
 */

class WorkflowModule {
    constructor(app) {
        this.app = app;
        this.workflows = new Map();
        this.activeWorkflows = [];
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initEventListeners();
        this.loadWorkflows();
    }
    
    initElements() {
        this.workflowContent = document.getElementById('workflowContent');
        this.refreshWorkflowBtn = document.getElementById('refreshWorkflowBtn');
    }
    
    initEventListeners() {
        // 刷新工作流
        this.refreshWorkflowBtn?.addEventListener('click', () => {
            this.refreshWorkflows();
        });
        
        // 监听工作流消息
        if (this.app.websocket) {
            this.app.websocket.on('workflow_started', (message) => {
                this.handleWorkflowStarted(message);
            });
            
            this.app.websocket.on('workflow_progress', (message) => {
                this.handleWorkflowProgress(message);
            });
            
            this.app.websocket.on('workflow_completed', (message) => {
                this.handleWorkflowCompleted(message);
            });
            
            this.app.websocket.on('workflow_error', (message) => {
                this.handleWorkflowError(message);
            });
        }
    }
    
    handleWorkflowStarted(message) {
        console.log('🔄 工作流已启动:', message);
        
        const workflow = {
            id: message.workflow_id || message.document_id,
            type: 'document_analysis',
            status: 'running',
            document_id: message.document_id,
            started_at: message.timestamp || new Date().toISOString(),
            steps: [
                { name: 'parsing', title: '文档解析', status: 'running', icon: '📄' },
                { name: 'analysis', title: '内容分析', status: 'pending', icon: '🔍' },
                { name: 'summary', title: '摘要生成', status: 'pending', icon: '📝' }
            ],
            current_step: 0,
            progress: 0
        };
        
        this.workflows.set(workflow.id, workflow);
        this.activeWorkflows.push(workflow);
        
        this.renderWorkflows();
        this.app.showNotification('文档分析工作流已启动', 'info');
    }
    
    handleWorkflowProgress(message) {
        console.log('📈 工作流进度更新:', message);
        
        const workflowId = message.workflow_id || message.document_id;
        const workflow = this.workflows.get(workflowId);
        
        if (!workflow) return;
        
        // 更新步骤状态
        if (message.step) {
            const stepIndex = workflow.steps.findIndex(s => s.name === message.step);
            if (stepIndex >= 0) {
                // 完成当前步骤
                if (workflow.current_step < workflow.steps.length) {
                    workflow.steps[workflow.current_step].status = 'completed';
                }
                
                // 开始新步骤
                workflow.current_step = stepIndex;
                workflow.steps[stepIndex].status = 'running';
                
                // 更新进度
                workflow.progress = Math.round(((stepIndex + 1) / workflow.steps.length) * 100);
            }
        }
        
        // 更新结果
        if (message.result) {
            const currentStep = workflow.steps[workflow.current_step];
            if (currentStep) {
                currentStep.result = message.result;
                currentStep.completed_at = message.timestamp || new Date().toISOString();
            }
        }
        
        this.renderWorkflows();
    }
    
    handleWorkflowCompleted(message) {
        console.log('✅ 工作流已完成:', message);
        
        const workflowId = message.workflow_id || message.document_id;
        const workflow = this.workflows.get(workflowId);
        
        if (!workflow) return;
        
        // 更新工作流状态
        workflow.status = 'completed';
        workflow.completed_at = message.timestamp || new Date().toISOString();
        workflow.progress = 100;
        workflow.results = message.results;
        
        // 完成所有步骤
        workflow.steps.forEach(step => {
            if (step.status !== 'completed') {
                step.status = 'completed';
            }
        });
        
        // 从活跃列表中移除
        this.activeWorkflows = this.activeWorkflows.filter(w => w.id !== workflowId);
        
        this.renderWorkflows();
        this.app.showNotification('文档分析工作流已完成', 'success');
    }
    
    handleWorkflowError(message) {
        console.error('❌ 工作流错误:', message);
        
        const workflowId = message.workflow_id || message.document_id;
        const workflow = this.workflows.get(workflowId);
        
        if (!workflow) return;
        
        // 更新工作流状态
        workflow.status = 'error';
        workflow.error = message.content || message.error;
        workflow.error_at = message.timestamp || new Date().toISOString();
        
        // 标记当前步骤为错误
        if (workflow.current_step < workflow.steps.length) {
            workflow.steps[workflow.current_step].status = 'error';
            workflow.steps[workflow.current_step].error = workflow.error;
        }
        
        // 从活跃列表中移除
        this.activeWorkflows = this.activeWorkflows.filter(w => w.id !== workflowId);
        
        this.renderWorkflows();
        this.app.showNotification('工作流执行出错: ' + workflow.error, 'error');
    }
    
    renderWorkflows() {
        if (!this.workflowContent) return;
        
        const workflowSteps = this.workflowContent.querySelector('.workflow-steps');
        if (!workflowSteps) return;
        
        // 获取所有工作流（活跃的和最近完成的）
        const allWorkflows = Array.from(this.workflows.values())
            .sort((a, b) => new Date(b.started_at) - new Date(a.started_at))
            .slice(0, 10); // 只显示最近10个
        
        if (allWorkflows.length === 0) {
            workflowSteps.innerHTML = `
                <div class="empty-state">
                    <p>暂无活动工作流</p>
                </div>
            `;
            return;
        }
        
        workflowSteps.innerHTML = '';
        
        allWorkflows.forEach(workflow => {
            const workflowEl = this.createWorkflowElement(workflow);
            workflowSteps.appendChild(workflowEl);
        });
    }
    
    createWorkflowElement(workflow) {
        const workflowEl = document.createElement('div');
        workflowEl.className = `workflow-item ${workflow.status}`;
        workflowEl.dataset.workflowId = workflow.id;
        
        // 工作流头部
        const headerEl = document.createElement('div');
        headerEl.className = 'workflow-header';
        headerEl.innerHTML = `
            <div class="workflow-title">
                <h4>${this.getWorkflowTitle(workflow)}</h4>
                <span class="workflow-status ${workflow.status}">${this.getStatusText(workflow.status)}</span>
            </div>
            <div class="workflow-meta">
                <span class="workflow-time">${Utils.formatTime(workflow.started_at)}</span>
                <span class="workflow-progress">${workflow.progress || 0}%</span>
            </div>
        `;
        
        // 进度条
        const progressEl = document.createElement('div');
        progressEl.className = 'workflow-progress-bar';
        progressEl.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${workflow.progress || 0}%"></div>
            </div>
        `;
        
        // 步骤列表
        const stepsEl = document.createElement('div');
        stepsEl.className = 'workflow-steps-list';
        
        workflow.steps.forEach((step, index) => {
            const stepEl = document.createElement('div');
            stepEl.className = `workflow-step ${step.status}`;
            
            stepEl.innerHTML = `
                <div class="step-icon">${step.icon}</div>
                <div class="step-content">
                    <div class="step-title">${step.title}</div>
                    <div class="step-status">${this.getStepStatusText(step.status)}</div>
                    ${step.error ? `<div class="step-error">${step.error}</div>` : ''}
                </div>
                <div class="step-time">
                    ${step.completed_at ? Utils.formatTime(step.completed_at) : ''}
                </div>
            `;
            
            stepsEl.appendChild(stepEl);
        });
        
        // 操作按钮
        const actionsEl = document.createElement('div');
        actionsEl.className = 'workflow-actions';
        
        if (workflow.status === 'error') {
            const retryBtn = document.createElement('button');
            retryBtn.className = 'btn btn-small';
            retryBtn.textContent = '重试';
            retryBtn.onclick = () => this.retryWorkflow(workflow);
            actionsEl.appendChild(retryBtn);
        }
        
        if (workflow.status === 'completed' && workflow.results) {
            const viewBtn = document.createElement('button');
            viewBtn.className = 'btn btn-small';
            viewBtn.textContent = '查看结果';
            viewBtn.onclick = () => this.viewWorkflowResults(workflow);
            actionsEl.appendChild(viewBtn);
        }
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-small btn-secondary';
        removeBtn.textContent = '移除';
        removeBtn.onclick = () => this.removeWorkflow(workflow.id);
        actionsEl.appendChild(removeBtn);
        
        // 组装元素
        workflowEl.appendChild(headerEl);
        workflowEl.appendChild(progressEl);
        workflowEl.appendChild(stepsEl);
        workflowEl.appendChild(actionsEl);
        
        return workflowEl;
    }
    
    getWorkflowTitle(workflow) {
        const titles = {
            'document_analysis': '文档分析工作流'
        };
        
        let title = titles[workflow.type] || '未知工作流';
        
        if (workflow.document_id) {
            const document = this.app.documentModule?.documents.find(d => d.id === workflow.document_id);
            if (document) {
                title += ` - ${document.name}`;
            }
        }
        
        return title;
    }
    
    getStatusText(status) {
        const statusMap = {
            'running': '运行中',
            'completed': '已完成',
            'error': '错误',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }
    
    getStepStatusText(status) {
        const statusMap = {
            'pending': '等待中',
            'running': '执行中',
            'completed': '已完成',
            'error': '错误'
        };
        return statusMap[status] || status;
    }
    
    async retryWorkflow(workflow) {
        try {
            // 重置工作流状态
            workflow.status = 'running';
            workflow.progress = 0;
            workflow.current_step = 0;
            workflow.error = null;
            workflow.error_at = null;
            
            // 重置步骤状态
            workflow.steps.forEach((step, index) => {
                step.status = index === 0 ? 'running' : 'pending';
                step.error = null;
                step.completed_at = null;
                step.result = null;
            });
            
            // 重新添加到活跃列表
            if (!this.activeWorkflows.find(w => w.id === workflow.id)) {
                this.activeWorkflows.push(workflow);
            }
            
            // 发送重试命令
            if (this.app.websocket && this.app.websocket.isConnected()) {
                this.app.websocket.sendCommand('analyze_document', {
                    document_id: workflow.document_id
                });
            }
            
            this.renderWorkflows();
            this.app.showNotification('工作流重试已启动', 'info');
            
        } catch (error) {
            console.error('❌ 重试工作流失败:', error);
            this.app.showNotification('重试失败: ' + error.message, 'error');
        }
    }
    
    viewWorkflowResults(workflow) {
        if (workflow.results && this.app.analysisModule) {
            // 切换到分析结果标签页
            const analysisTab = document.querySelector('[data-tab="analysis"]');
            if (analysisTab) {
                analysisTab.click();
            }
            
            // 显示结果
            this.app.analysisModule.renderAnalysisResults(workflow.results);
        }
    }
    
    removeWorkflow(workflowId) {
        this.workflows.delete(workflowId);
        this.activeWorkflows = this.activeWorkflows.filter(w => w.id !== workflowId);
        this.renderWorkflows();
        this.saveWorkflows();
    }
    
    async refreshWorkflows() {
        try {
            // 从服务器获取活跃工作流
            const response = await this.app.apiRequest('/api/workflow/active');
            
            if (response.success && response.workflows) {
                response.workflows.forEach(serverWorkflow => {
                    const localWorkflow = this.workflows.get(serverWorkflow.id);
                    if (localWorkflow) {
                        // 更新本地工作流状态
                        Object.assign(localWorkflow, serverWorkflow);
                    } else {
                        // 添加新的工作流
                        this.workflows.set(serverWorkflow.id, serverWorkflow);
                        if (serverWorkflow.status === 'running') {
                            this.activeWorkflows.push(serverWorkflow);
                        }
                    }
                });
                
                this.renderWorkflows();
            }
            
        } catch (error) {
            console.error('❌ 刷新工作流失败:', error);
            this.app.showNotification('刷新工作流失败: ' + error.message, 'error');
        }
    }
    
    loadWorkflows() {
        try {
            const saved = Utils.StorageUtils.get('workflows', []);
            saved.forEach(workflow => {
                this.workflows.set(workflow.id, workflow);
                if (workflow.status === 'running') {
                    this.activeWorkflows.push(workflow);
                }
            });
            
            this.renderWorkflows();
            
        } catch (error) {
            console.error('❌ 加载工作流失败:', error);
        }
    }
    
    saveWorkflows() {
        try {
            const workflowsArray = Array.from(this.workflows.values());
            Utils.StorageUtils.set('workflows', workflowsArray);
        } catch (error) {
            console.error('❌ 保存工作流失败:', error);
        }
    }
    
    onTabActivated() {
        // 标签页激活时刷新工作流
        this.renderWorkflows();
        this.refreshWorkflows();
    }
    
    getStats() {
        return {
            totalWorkflows: this.workflows.size,
            activeWorkflows: this.activeWorkflows.length,
            completedWorkflows: Array.from(this.workflows.values()).filter(w => w.status === 'completed').length,
            errorWorkflows: Array.from(this.workflows.values()).filter(w => w.status === 'error').length
        };
    }
    
    // 清理旧的工作流
    cleanup() {
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        
        for (const [id, workflow] of this.workflows) {
            const workflowDate = new Date(workflow.started_at);
            if (workflowDate < oneWeekAgo && workflow.status !== 'running') {
                this.workflows.delete(id);
            }
        }
        
        this.activeWorkflows = this.activeWorkflows.filter(w => this.workflows.has(w.id));
        this.saveWorkflows();
    }
}

// 导出到全局
window.WorkflowModule = WorkflowModule;