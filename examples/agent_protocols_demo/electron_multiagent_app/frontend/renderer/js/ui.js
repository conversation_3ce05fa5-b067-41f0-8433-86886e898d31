/**
 * UI 管理模块
 */

class UIManager {
    constructor(app) {
        this.app = app;
        this.notifications = [];
        this.modals = new Map();
        this.tooltips = new Map();
        
        this.init();
    }
    
    init() {
        this.initNotifications();
        this.initModals();
        this.initTooltips();
        this.initTheme();
    }
    
    // 通知系统
    initNotifications() {
        this.notificationContainer = document.getElementById('notificationContainer');
        if (!this.notificationContainer) {
            this.notificationContainer = document.createElement('div');
            this.notificationContainer.id = 'notificationContainer';
            this.notificationContainer.className = 'notification-container';
            document.body.appendChild(this.notificationContainer);
        }
    }
    
    showNotification(message, type = 'info', duration = 5000, actions = []) {
        const notification = document.createElement('div');
        notification.className = `notification ${type} animate-slide-in-right`;
        
        const content = document.createElement('div');
        content.className = 'notification-content';
        
        const messageEl = document.createElement('div');
        messageEl.className = 'notification-message';
        messageEl.textContent = message;
        content.appendChild(messageEl);
        
        // 添加操作按钮
        if (actions.length > 0) {
            const actionsEl = document.createElement('div');
            actionsEl.className = 'notification-actions';
            
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = 'btn btn-small';
                button.textContent = action.text;
                button.onclick = () => {
                    action.handler();
                    this.removeNotification(notification);
                };
                actionsEl.appendChild(button);
            });
            
            content.appendChild(actionsEl);
        }
        
        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.onclick = () => this.removeNotification(notification);
        
        notification.appendChild(content);
        notification.appendChild(closeBtn);
        
        this.notificationContainer.appendChild(notification);
        this.notifications.push(notification);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }
        
        return notification;
    }
    
    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.classList.add('animate-slide-out-right');
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                
                const index = this.notifications.indexOf(notification);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                }
            }, 300);
        }
    }
    
    clearAllNotifications() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }
    
    // 模态框系统
    initModals() {
        // 为所有模态框添加事件监听
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target.id);
            }
        });
        
        // ESC 键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }
    
    showModal(modalId, data = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) return false;
        
        // 填充数据
        if (data && typeof data === 'object') {
            Object.entries(data).forEach(([key, value]) => {
                const element = modal.querySelector(`[data-field="${key}"]`);
                if (element) {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.value = value;
                    } else {
                        element.textContent = value;
                    }
                }
            });
        }
        
        modal.classList.add('show');
        this.modals.set(modalId, modal);
        
        // 聚焦到第一个输入框
        const firstInput = modal.querySelector('input, textarea, select');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
        
        return true;
    }
    
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return false;
        
        modal.classList.remove('show');
        this.modals.delete(modalId);
        
        return true;
    }
    
    hideAllModals() {
        this.modals.forEach((modal, modalId) => {
            this.hideModal(modalId);
        });
    }
    
    // 工具提示系统
    initTooltips() {
        document.addEventListener('mouseover', (e) => {
            const element = e.target.closest('[title], [data-tooltip]');
            if (element) {
                this.showTooltip(element);
            }
        });
        
        document.addEventListener('mouseout', (e) => {
            const element = e.target.closest('[title], [data-tooltip]');
            if (element) {
                this.hideTooltip(element);
            }
        });
    }
    
    showTooltip(element) {
        const text = element.getAttribute('data-tooltip') || element.getAttribute('title');
        if (!text) return;
        
        // 移除原生 title 属性以避免冲突
        if (element.hasAttribute('title')) {
            element.setAttribute('data-original-title', element.getAttribute('title'));
            element.removeAttribute('title');
        }
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        // 定位工具提示
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
        let top = rect.top - tooltipRect.height - 8;
        
        // 边界检查
        if (left < 8) left = 8;
        if (left + tooltipRect.width > window.innerWidth - 8) {
            left = window.innerWidth - tooltipRect.width - 8;
        }
        if (top < 8) {
            top = rect.bottom + 8;
            tooltip.classList.add('tooltip-bottom');
        }
        
        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
        
        this.tooltips.set(element, tooltip);
        
        // 添加动画
        setTimeout(() => tooltip.classList.add('show'), 10);
    }
    
    hideTooltip(element) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.classList.remove('show');
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200);
            this.tooltips.delete(element);
        }
        
        // 恢复原生 title 属性
        const originalTitle = element.getAttribute('data-original-title');
        if (originalTitle) {
            element.setAttribute('title', originalTitle);
            element.removeAttribute('data-original-title');
        }
    }
    
    // 主题系统
    initTheme() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.applyTheme(this.currentTheme);
    }
    
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
    }
    
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        return newTheme;
    }
    
    // 加载状态管理
    showLoading(message = '加载中...', target = null) {
        const overlay = target || document.getElementById('loadingOverlay');
        if (!overlay) return;
        
        const text = overlay.querySelector('#loadingText');
        if (text) {
            text.textContent = message;
        }
        
        overlay.classList.add('show');
    }
    
    hideLoading(target = null) {
        const overlay = target || document.getElementById('loadingOverlay');
        if (!overlay) return;
        
        overlay.classList.remove('show');
    }
    
    // 进度条
    updateProgress(percentage, message = '', target = null) {
        const progressBar = target || document.querySelector('.progress-bar');
        if (!progressBar) return;
        
        const fill = progressBar.querySelector('.progress-fill');
        const text = progressBar.querySelector('.progress-text');
        
        if (fill) {
            fill.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
        }
        
        if (text && message) {
            text.textContent = message;
        }
    }
    
    // 表单验证
    validateForm(formElement) {
        const errors = [];
        const inputs = formElement.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            const error = this.validateInput(input);
            if (error) {
                errors.push({ element: input, message: error });
            }
        });
        
        // 显示错误
        errors.forEach(({ element, message }) => {
            this.showInputError(element, message);
        });
        
        return errors.length === 0;
    }
    
    validateInput(input) {
        const value = input.value.trim();
        const type = input.type;
        const required = input.hasAttribute('required');
        
        // 必填验证
        if (required && !value) {
            return '此字段为必填项';
        }
        
        if (!value) return null;
        
        // 类型验证
        switch (type) {
            case 'email':
                if (!Utils.isValidEmail(value)) {
                    return '请输入有效的邮箱地址';
                }
                break;
            case 'url':
                if (!Utils.isValidUrl(value)) {
                    return '请输入有效的URL地址';
                }
                break;
            case 'number':
                const min = input.getAttribute('min');
                const max = input.getAttribute('max');
                const num = parseFloat(value);
                
                if (isNaN(num)) {
                    return '请输入有效的数字';
                }
                if (min !== null && num < parseFloat(min)) {
                    return `数值不能小于 ${min}`;
                }
                if (max !== null && num > parseFloat(max)) {
                    return `数值不能大于 ${max}`;
                }
                break;
        }
        
        // 长度验证
        const minLength = input.getAttribute('minlength');
        const maxLength = input.getAttribute('maxlength');
        
        if (minLength && value.length < parseInt(minLength)) {
            return `长度不能少于 ${minLength} 个字符`;
        }
        if (maxLength && value.length > parseInt(maxLength)) {
            return `长度不能超过 ${maxLength} 个字符`;
        }
        
        return null;
    }
    
    showInputError(input, message) {
        this.clearInputError(input);
        
        input.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'input-error';
        errorElement.textContent = message;
        
        input.parentNode.appendChild(errorElement);
    }
    
    clearInputError(input) {
        input.classList.remove('error');
        
        const errorElement = input.parentNode.querySelector('.input-error');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    clearFormErrors(formElement) {
        const inputs = formElement.querySelectorAll('input, textarea, select');
        inputs.forEach(input => this.clearInputError(input));
    }
    
    // 确认对话框
    showConfirm(message, title = '确认', options = {}) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal confirm-modal show';
            
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary cancel-btn">${options.cancelText || '取消'}</button>
                        <button class="btn btn-primary confirm-btn">${options.confirmText || '确认'}</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            const confirmBtn = modal.querySelector('.confirm-btn');
            const cancelBtn = modal.querySelector('.cancel-btn');
            
            const cleanup = () => {
                modal.remove();
            };
            
            confirmBtn.onclick = () => {
                cleanup();
                resolve(true);
            };
            
            cancelBtn.onclick = () => {
                cleanup();
                resolve(false);
            };
            
            modal.onclick = (e) => {
                if (e.target === modal) {
                    cleanup();
                    resolve(false);
                }
            };
        });
    }
    
    // 获取 UI 统计信息
    getStats() {
        return {
            notifications: this.notifications.length,
            modals: this.modals.size,
            tooltips: this.tooltips.size,
            theme: this.currentTheme
        };
    }
}

// 导出到全局
window.UIManager = UIManager;