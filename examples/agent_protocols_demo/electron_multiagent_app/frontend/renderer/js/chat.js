/**
 * 聊天模块
 */

class ChatModule {
    constructor(app) {
        this.app = app;
        this.messages = [];
        this.isTyping = false;
        this.currentDocument = null;
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initEventListeners();
        this.loadChatHistory();
    }
    
    initElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.clearChatBtn = document.getElementById('clearChatBtn');
        this.exportChatBtn = document.getElementById('exportChatBtn');
    }
    
    initEventListeners() {
        // 发送按钮
        this.sendBtn?.addEventListener('click', () => {
            this.sendMessage();
        });
        
        // 输入框事件
        this.chatInput?.addEventListener('input', () => {
            this.updateSendButton();
        });
        
        this.chatInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 清空聊天
        this.clearChatBtn?.addEventListener('click', () => {
            this.clearChat();
        });
        
        // 导出聊天
        this.exportChatBtn?.addEventListener('click', () => {
            this.exportChat();
        });
        
        // 监听 WebSocket 消息
        if (this.app.websocket) {
            this.app.websocket.on('chat_response', (message) => {
                this.handleChatResponse(message);
            });
        }
    }
    
    updateSendButton() {
        const hasText = this.chatInput?.value.trim().length > 0;
        const isConnected = this.app.isConnected;
        
        if (this.sendBtn) {
            this.sendBtn.disabled = !hasText || !isConnected || this.isTyping;
        }
    }
    
    async sendMessage() {
        const message = this.chatInput?.value.trim();
        if (!message || this.isTyping) return;
        
        try {
            // 添加用户消息到界面
            this.addMessage('user', message);
            
            // 清空输入框
            this.chatInput.value = '';
            this.updateSendButton();
            
            // 显示正在输入状态
            this.showTypingIndicator();
            
            // 发送消息到后端
            if (this.app.websocket && this.app.websocket.isConnected()) {
                this.app.websocket.sendChatMessage(message, 'electron_client');
            } else {
                // 使用 HTTP API 作为备选
                const response = await this.app.apiRequest('/api/chat', {
                    method: 'POST',
                    body: JSON.stringify({
                        message: message,
                        document_id: this.currentDocument?.id
                    })
                });
                
                this.handleChatResponse({
                    type: 'chat_response',
                    content: response.response || '抱歉，我无法处理您的请求。',
                    timestamp: new Date().toISOString()
                });
            }
            
        } catch (error) {
            console.error('❌ 发送消息失败:', error);
            this.hideTypingIndicator();
            this.addMessage('system', '发送消息失败，请检查网络连接。');
        }
    }
    
    handleChatResponse(message) {
        this.hideTypingIndicator();
        
        if (message.content) {
            this.addMessage('assistant', message.content);
        } else if (message.error) {
            this.addMessage('system', `错误: ${message.error}`);
        }
    }
    
    addMessage(role, content, timestamp = null) {
        const message = {
            id: Utils.generateId('msg'),
            role: role,
            content: content,
            timestamp: timestamp || new Date().toISOString()
        };
        
        this.messages.push(message);
        this.renderMessage(message);
        this.scrollToBottom();
        this.saveChatHistory();
    }
    
    renderMessage(message) {
        if (!this.chatMessages) return;
        
        const messageEl = document.createElement('div');
        messageEl.className = `message ${message.role} animate-slide-in-up`;
        messageEl.dataset.messageId = message.id;
        
        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';
        
        // 处理消息内容
        if (message.role === 'system') {
            contentEl.innerHTML = this.formatSystemMessage(message.content);
        } else {
            contentEl.innerHTML = this.formatMessage(message.content);
        }
        
        const timestampEl = document.createElement('div');
        timestampEl.className = 'message-timestamp';
        timestampEl.textContent = Utils.formatTime(message.timestamp);
        
        messageEl.appendChild(contentEl);
        messageEl.appendChild(timestampEl);
        
        // 添加操作按钮
        if (message.role === 'assistant') {
            const actionsEl = this.createMessageActions(message);
            messageEl.appendChild(actionsEl);
        }
        
        this.chatMessages.appendChild(messageEl);
    }
    
    formatMessage(content) {
        // 基本的 Markdown 支持
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    }
    
    formatSystemMessage(content) {
        // 系统消息的特殊格式化
        if (content.includes('•') || content.includes('-')) {
            return content.replace(/\n/g, '<br>');
        }
        return content;
    }
    
    createMessageActions(message) {
        const actionsEl = document.createElement('div');
        actionsEl.className = 'message-actions';
        
        // 复制按钮
        const copyBtn = document.createElement('button');
        copyBtn.className = 'btn btn-small';
        copyBtn.innerHTML = '📋';
        copyBtn.title = '复制消息';
        copyBtn.onclick = () => this.copyMessage(message);
        
        // 重新生成按钮
        const regenerateBtn = document.createElement('button');
        regenerateBtn.className = 'btn btn-small';
        regenerateBtn.innerHTML = '🔄';
        regenerateBtn.title = '重新生成';
        regenerateBtn.onclick = () => this.regenerateMessage(message);
        
        actionsEl.appendChild(copyBtn);
        actionsEl.appendChild(regenerateBtn);
        
        return actionsEl;
    }
    
    copyMessage(message) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(message.content).then(() => {
                this.app.showNotification('消息已复制到剪贴板', 'success', 2000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }
    }
    
    regenerateMessage(message) {
        // 找到用户的上一条消息
        const messageIndex = this.messages.findIndex(m => m.id === message.id);
        if (messageIndex > 0) {
            const userMessage = this.messages[messageIndex - 1];
            if (userMessage.role === 'user') {
                // 移除当前助手消息
                this.removeMessage(message.id);
                
                // 重新发送用户消息
                this.showTypingIndicator();
                
                if (this.app.websocket && this.app.websocket.isConnected()) {
                    this.app.websocket.sendChatMessage(userMessage.content, 'electron_client');
                }
            }
        }
    }
    
    removeMessage(messageId) {
        // 从数组中移除
        this.messages = this.messages.filter(m => m.id !== messageId);
        
        // 从 DOM 中移除
        const messageEl = this.chatMessages?.querySelector(`[data-message-id="${messageId}"]`);
        if (messageEl) {
            messageEl.remove();
        }
        
        this.saveChatHistory();
    }
    
    showTypingIndicator() {
        this.hideTypingIndicator(); // 确保只有一个指示器
        
        this.isTyping = true;
        this.updateSendButton();
        
        const typingEl = document.createElement('div');
        typingEl.className = 'message assistant typing-indicator';
        typingEl.id = 'typingIndicator';
        
        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';
        contentEl.innerHTML = '<div class="typing-dots"><span></span><span></span><span></span></div>';
        
        typingEl.appendChild(contentEl);
        this.chatMessages?.appendChild(typingEl);
        
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        const typingEl = document.getElementById('typingIndicator');
        if (typingEl) {
            typingEl.remove();
        }
        
        this.isTyping = false;
        this.updateSendButton();
    }
    
    scrollToBottom() {
        if (this.chatMessages) {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }
    }
    
    async clearChat() {
        const confirmed = await this.app.uiManager?.showConfirm(
            '确定要清空所有聊天记录吗？此操作无法撤销。',
            '清空聊天记录'
        );
        
        if (confirmed) {
            this.messages = [];
            
            if (this.chatMessages) {
                // 保留欢迎消息
                const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
                this.chatMessages.innerHTML = '';
                if (welcomeMessage) {
                    this.chatMessages.appendChild(welcomeMessage);
                }
            }
            
            this.saveChatHistory();
            this.app.showNotification('聊天记录已清空', 'success');
        }
    }
    
    async exportChat() {
        if (this.messages.length === 0) {
            this.app.showNotification('没有聊天记录可导出', 'warning');
            return;
        }
        
        try {
            const chatData = {
                exported_at: new Date().toISOString(),
                document: this.currentDocument,
                messages: this.messages
            };
            
            const content = JSON.stringify(chatData, null, 2);
            const filename = `chat_export_${new Date().toISOString().slice(0, 10)}.json`;
            
            // 使用 Electron API 保存文件
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                const result = await ipcRenderer.invoke('show-save-dialog', {
                    defaultPath: filename,
                    filters: [
                        { name: 'JSON Files', extensions: ['json'] },
                        { name: 'All Files', extensions: ['*'] }
                    ]
                });
                
                if (!result.canceled) {
                    await ipcRenderer.invoke('write-file', result.filePath, content);
                    this.app.showNotification('聊天记录导出成功', 'success');
                }
            } else {
                // Web 环境下载
                const blob = new Blob([content], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
                
                this.app.showNotification('聊天记录导出成功', 'success');
            }
            
        } catch (error) {
            console.error('❌ 导出聊天记录失败:', error);
            this.app.showNotification('导出失败: ' + error.message, 'error');
        }
    }
    
    setCurrentDocument(document) {
        this.currentDocument = document;
        
        if (document) {
            this.addMessage('system', `已切换到文档: ${document.name}`);
        }
    }
    
    loadChatHistory() {
        try {
            const saved = Utils.StorageUtils.get('chat_history', []);
            this.messages = saved;
            
            // 渲染历史消息
            this.messages.forEach(message => {
                this.renderMessage(message);
            });
            
            this.scrollToBottom();
            
        } catch (error) {
            console.error('❌ 加载聊天历史失败:', error);
        }
    }
    
    saveChatHistory() {
        try {
            // 只保存最近的100条消息
            const messagesToSave = this.messages.slice(-100);
            Utils.StorageUtils.set('chat_history', messagesToSave);
        } catch (error) {
            console.error('❌ 保存聊天历史失败:', error);
        }
    }
    
    onTabActivated() {
        // 标签页激活时的处理
        this.scrollToBottom();
        this.updateSendButton();
        
        // 聚焦输入框
        if (this.chatInput) {
            this.chatInput.focus();
        }
    }
    
    getStats() {
        return {
            totalMessages: this.messages.length,
            userMessages: this.messages.filter(m => m.role === 'user').length,
            assistantMessages: this.messages.filter(m => m.role === 'assistant').length,
            systemMessages: this.messages.filter(m => m.role === 'system').length,
            isTyping: this.isTyping,
            currentDocument: this.currentDocument?.id || null
        };
    }
}

// 导出到全局
window.ChatModule = ChatModule;