/**
 * WebSocket 通信模块
 */

class WebSocketManager {
    constructor(app) {
        this.app = app;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isConnecting = false;
        this.messageQueue = [];
        this.eventHandlers = new Map();
        
        // 绑定方法
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }
    
    async connect(url) {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        
        this.isConnecting = true;
        
        try {
            console.log('🔌 连接 WebSocket:', url);
            
            this.ws = new WebSocket(url);
            
            this.ws.onopen = this.onOpen;
            this.ws.onmessage = this.onMessage;
            this.ws.onclose = this.onClose;
            this.ws.onerror = this.onError;
            
            // 连接超时处理
            setTimeout(() => {
                if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
                    console.warn('⚠️ WebSocket 连接超时');
                    this.ws.close();
                }
            }, 10000);
            
        } catch (error) {
            console.error('❌ WebSocket 连接失败:', error);
            this.isConnecting = false;
            this.scheduleReconnect();
        }
    }
    
    disconnect() {
        console.log('🔌 断开 WebSocket 连接');
        
        this.reconnectAttempts = this.maxReconnectAttempts; // 阻止重连
        
        if (this.ws) {
            this.ws.onopen = null;
            this.ws.onmessage = null;
            this.ws.onclose = null;
            this.ws.onerror = null;
            
            if (this.ws.readyState === WebSocket.OPEN) {
                this.ws.close();
            }
            
            this.ws = null;
        }
        
        this.isConnecting = false;
        this.messageQueue = [];
    }
    
    send(message) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.warn('⚠️ WebSocket 未连接，消息已加入队列');
            this.messageQueue.push(message);
            return false;
        }
        
        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            this.ws.send(messageStr);
            console.log('📤 发送 WebSocket 消息:', message);
            return true;
        } catch (error) {
            console.error('❌ 发送 WebSocket 消息失败:', error);
            return false;
        }
    }
    
    onOpen(event) {
        console.log('✅ WebSocket 连接已建立');
        
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // 更新连接状态
        if (this.app) {
            this.app.updateConnectionStatus(true, '已连接');
        }
        
        // 发送队列中的消息
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
        
        // 触发连接事件
        this.emit('connected', event);
    }
    
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('📥 收到 WebSocket 消息:', message);
            
            // 触发消息事件
            this.emit('message', message);
            
            // 根据消息类型分发
            if (message.type) {
                this.emit(message.type, message);
            }
            
        } catch (error) {
            console.error('❌ 解析 WebSocket 消息失败:', error);
        }
    }
    
    onClose(event) {
        console.log('🔌 WebSocket 连接已关闭:', event.code, event.reason);
        
        this.isConnecting = false;
        
        // 更新连接状态
        if (this.app) {
            this.app.updateConnectionStatus(false, '连接断开');
        }
        
        // 触发断开事件
        this.emit('disconnected', event);
        
        // 如果不是主动断开，尝试重连
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    onError(event) {
        console.error('❌ WebSocket 错误:', event);
        
        this.isConnecting = false;
        
        // 更新连接状态
        if (this.app) {
            this.app.updateConnectionStatus(false, '连接错误');
        }
        
        // 触发错误事件
        this.emit('error', event);
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ WebSocket 重连次数已达上限');
            if (this.app) {
                this.app.showNotification('WebSocket 连接失败，请检查网络连接', 'error');
            }
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
        
        console.log(`🔄 ${delay}ms 后尝试第 ${this.reconnectAttempts} 次重连...`);
        
        if (this.app) {
            this.app.updateConnectionStatus(false, `重连中 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        }
        
        setTimeout(() => {
            if (this.app && this.app.config) {
                const clientId = 'electron_client_' + Date.now();
                const wsUrl = `${this.app.config.websocketUrl}/ws/${clientId}`;
                this.connect(wsUrl);
            }
        }, delay);
    }
    
    // 事件系统
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`❌ 事件处理器错误 (${event}):`, error);
                }
            });
        }
    }
    
    // 获取连接状态
    getReadyState() {
        if (!this.ws) return WebSocket.CLOSED;
        return this.ws.readyState;
    }
    
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN;
    }
    
    // 发送特定类型的消息
    sendChatMessage(content, clientId = null) {
        return this.send({
            type: 'chat',
            content: content,
            client_id: clientId,
            timestamp: new Date().toISOString()
        });
    }
    
    sendCommand(command, parameters = {}, agentId = null) {
        return this.send({
            type: 'command',
            command: command,
            parameters: parameters,
            agent_id: agentId,
            timestamp: new Date().toISOString()
        });
    }
    
    sendConfig(config) {
        return this.send({
            type: 'config',
            config: config,
            timestamp: new Date().toISOString()
        });
    }
    
    // 心跳检测
    startHeartbeat(interval = 30000) {
        this.stopHeartbeat();
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected()) {
                this.send({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            }
        }, interval);
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    // 消息统计
    getStats() {
        return {
            connected: this.isConnected(),
            reconnectAttempts: this.reconnectAttempts,
            queuedMessages: this.messageQueue.length,
            eventHandlers: this.eventHandlers.size
        };
    }
}

// 导出到全局
window.WebSocketManager = WebSocketManager;