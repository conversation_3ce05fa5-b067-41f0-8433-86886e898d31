/**
 * 分析结果模块
 */

class AnalysisModule {
    constructor(app) {
        this.app = app;
        this.currentDocument = null;
        this.analysisResults = {};
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initEventListeners();
    }
    
    initElements() {
        this.analysisContent = document.getElementById('analysisContent');
        this.refreshAnalysisBtn = document.getElementById('refreshAnalysisBtn');
        this.exportAnalysisBtn = document.getElementById('exportAnalysisBtn');
    }
    
    initEventListeners() {
        // 刷新分析结果
        this.refreshAnalysisBtn?.addEventListener('click', () => {
            this.refreshAnalysis();
        });
        
        // 导出分析结果
        this.exportAnalysisBtn?.addEventListener('click', () => {
            this.exportAnalysis();
        });
        
        // 监听工作流消息
        if (this.app.websocket) {
            this.app.websocket.on('workflow_completed', (message) => {
                this.handleWorkflowCompleted(message);
            });
            
            this.app.websocket.on('workflow_progress', (message) => {
                this.handleWorkflowProgress(message);
            });
        }
    }
    
    setCurrentDocument(document) {
        this.currentDocument = document;
        
        if (document) {
            this.loadAnalysisResults(document.id);
        } else {
            this.showEmptyState();
        }
    }
    
    async loadAnalysisResults(documentId) {
        try {
            this.showLoading('加载分析结果...');
            
            const response = await this.app.apiRequest(`/api/analysis/${documentId}`);
            
            if (response.success) {
                this.analysisResults[documentId] = response.results;
                this.renderAnalysisResults(response.results);
            } else {
                this.showEmptyState('暂无分析结果');
            }
            
        } catch (error) {
            console.error('❌ 加载分析结果失败:', error);
            this.showError('加载分析结果失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    renderAnalysisResults(results) {
        if (!this.analysisContent) return;
        
        this.analysisContent.innerHTML = '';
        
        // 基本统计
        if (results.basic_stats) {
            this.renderBasicStats(results.basic_stats);
        }
        
        // 关键词
        if (results.keywords) {
            this.renderKeywords(results.keywords);
        }
        
        // 情感分析
        if (results.sentiment) {
            this.renderSentiment(results.sentiment);
        }
        
        // 主题分析
        if (results.topics) {
            this.renderTopics(results.topics);
        }
        
        // 实体识别
        if (results.entities) {
            this.renderEntities(results.entities);
        }
        
        // AI 洞察
        if (results.ai_insights) {
            this.renderAIInsights(results.ai_insights);
        }
        
        // 摘要
        if (results.summary) {
            this.renderSummary(results.summary);
        }
    }
    
    renderBasicStats(stats) {
        const section = this.createSection('📊 基本统计', 'basic-stats');
        
        const statsGrid = document.createElement('div');
        statsGrid.className = 'stats-grid';
        
        const statItems = [
            { label: '字符数', value: stats.character_count?.toLocaleString() || 0 },
            { label: '词数', value: stats.word_count?.toLocaleString() || 0 },
            { label: '句子数', value: stats.sentence_count?.toLocaleString() || 0 },
            { label: '段落数', value: stats.paragraph_count?.toLocaleString() || 0 },
            { label: '平均句长', value: Math.round(stats.average_sentence_length || 0) },
            { label: '平均词/句', value: Math.round(stats.average_words_per_sentence || 0) }
        ];
        
        statItems.forEach(item => {
            const statEl = document.createElement('div');
            statEl.className = 'stat-item';
            statEl.innerHTML = `
                <span class="stat-value">${item.value}</span>
                <span class="stat-label">${item.label}</span>
            `;
            statsGrid.appendChild(statEl);
        });
        
        section.appendChild(statsGrid);
        this.analysisContent.appendChild(section);
    }
    
    renderKeywords(keywords) {
        const section = this.createSection('🔑 关键词', 'keywords');
        
        if (!keywords || keywords.length === 0) {
            section.appendChild(this.createEmptyMessage('未找到关键词'));
            this.analysisContent.appendChild(section);
            return;
        }
        
        const keywordsList = document.createElement('div');
        keywordsList.className = 'keywords-list';
        
        keywords.slice(0, 20).forEach(keyword => {
            const keywordEl = document.createElement('span');
            keywordEl.className = 'keyword-tag';
            keywordEl.textContent = keyword.keyword || keyword;
            keywordEl.title = `权重: ${keyword.score || 'N/A'}`;
            keywordsList.appendChild(keywordEl);
        });
        
        section.appendChild(keywordsList);
        this.analysisContent.appendChild(section);
    }
    
    renderSentiment(sentiment) {
        const section = this.createSection('😊 情感分析', 'sentiment');
        
        const sentimentEl = document.createElement('div');
        sentimentEl.className = 'sentiment-analysis';
        
        const indicator = document.createElement('div');
        indicator.className = `sentiment-indicator ${sentiment.sentiment || 'neutral'}`;
        
        const emoji = {
            'positive': '😊',
            'negative': '😞',
            'neutral': '😐'
        };
        
        indicator.innerHTML = `
            <span class="sentiment-emoji">${emoji[sentiment.sentiment] || '😐'}</span>
            <span class="sentiment-text">${this.getSentimentText(sentiment.sentiment)}</span>
            <span class="sentiment-confidence">置信度: ${Math.round((sentiment.confidence || 0) * 100)}%</span>
        `;
        
        sentimentEl.appendChild(indicator);
        
        if (sentiment.explanation) {
            const explanation = document.createElement('p');
            explanation.className = 'sentiment-explanation';
            explanation.textContent = sentiment.explanation;
            sentimentEl.appendChild(explanation);
        }
        
        section.appendChild(sentimentEl);
        this.analysisContent.appendChild(section);
    }
    
    renderTopics(topics) {
        const section = this.createSection('📝 主题分析', 'topics');
        
        if (!topics || topics.length === 0) {
            section.appendChild(this.createEmptyMessage('未找到主题'));
            this.analysisContent.appendChild(section);
            return;
        }
        
        const topicsContainer = document.createElement('div');
        topicsContainer.className = 'topics-container';
        
        topics.forEach((topic, index) => {
            const topicEl = document.createElement('div');
            topicEl.className = 'topic-item';
            
            const topicHeader = document.createElement('div');
            topicHeader.className = 'topic-header';
            topicHeader.innerHTML = `
                <h4>${topic.topic || `主题 ${index + 1}`}</h4>
                <span class="topic-count">${topic.sentence_count || 0} 个句子</span>
            `;
            
            const topicKeywords = document.createElement('div');
            topicKeywords.className = 'topic-keywords';
            
            if (topic.keywords && topic.keywords.length > 0) {
                topic.keywords.forEach(keyword => {
                    const keywordEl = document.createElement('span');
                    keywordEl.className = 'keyword-tag small';
                    keywordEl.textContent = keyword;
                    topicKeywords.appendChild(keywordEl);
                });
            }
            
            const topicSentences = document.createElement('div');
            topicSentences.className = 'topic-sentences';
            
            if (topic.sentences && topic.sentences.length > 0) {
                topic.sentences.slice(0, 2).forEach(sentence => {
                    const sentenceEl = document.createElement('p');
                    sentenceEl.className = 'topic-sentence';
                    sentenceEl.textContent = sentence;
                    topicSentences.appendChild(sentenceEl);
                });
            }
            
            topicEl.appendChild(topicHeader);
            topicEl.appendChild(topicKeywords);
            topicEl.appendChild(topicSentences);
            
            topicsContainer.appendChild(topicEl);
        });
        
        section.appendChild(topicsContainer);
        this.analysisContent.appendChild(section);
    }
    
    renderEntities(entities) {
        const section = this.createSection('🏷️ 实体识别', 'entities');
        
        if (!entities || entities.length === 0) {
            section.appendChild(this.createEmptyMessage('未找到实体'));
            this.analysisContent.appendChild(section);
            return;
        }
        
        const entitiesContainer = document.createElement('div');
        entitiesContainer.className = 'entities-container';
        
        // 按类型分组
        const entityGroups = {};
        entities.forEach(entity => {
            const type = entity.type || 'OTHER';
            if (!entityGroups[type]) {
                entityGroups[type] = [];
            }
            entityGroups[type].push(entity);
        });
        
        Object.entries(entityGroups).forEach(([type, typeEntities]) => {
            const groupEl = document.createElement('div');
            groupEl.className = 'entity-group';
            
            const groupHeader = document.createElement('h5');
            groupHeader.textContent = this.getEntityTypeText(type);
            groupEl.appendChild(groupHeader);
            
            const entitiesList = document.createElement('div');
            entitiesList.className = 'entities-list';
            
            typeEntities.forEach(entity => {
                const entityEl = document.createElement('span');
                entityEl.className = 'entity-tag';
                entityEl.textContent = entity.text;
                entityEl.title = `置信度: ${Math.round((entity.confidence || 0) * 100)}%`;
                entitiesList.appendChild(entityEl);
            });
            
            groupEl.appendChild(entitiesList);
            entitiesContainer.appendChild(groupEl);
        });
        
        section.appendChild(entitiesContainer);
        this.analysisContent.appendChild(section);
    }
    
    renderAIInsights(insights) {
        const section = this.createSection('🧠 AI 洞察', 'ai-insights');
        
        if (insights.summary) {
            const summaryEl = document.createElement('div');
            summaryEl.className = 'insight-summary';
            summaryEl.innerHTML = `<h4>摘要</h4><p>${insights.summary}</p>`;
            section.appendChild(summaryEl);
        }
        
        if (insights.key_points && insights.key_points.length > 0) {
            const pointsEl = document.createElement('div');
            pointsEl.className = 'insight-points';
            pointsEl.innerHTML = '<h4>关键点</h4>';
            
            const pointsList = document.createElement('ul');
            insights.key_points.forEach(point => {
                const pointEl = document.createElement('li');
                pointEl.textContent = point;
                pointsList.appendChild(pointEl);
            });
            
            pointsEl.appendChild(pointsList);
            section.appendChild(pointsEl);
        }
        
        if (insights.recommendations && insights.recommendations.length > 0) {
            const recsEl = document.createElement('div');
            recsEl.className = 'insight-recommendations';
            recsEl.innerHTML = '<h4>建议</h4>';
            
            const recsList = document.createElement('ul');
            insights.recommendations.forEach(rec => {
                const recEl = document.createElement('li');
                recEl.textContent = rec;
                recsList.appendChild(recEl);
            });
            
            recsEl.appendChild(recsList);
            section.appendChild(recsEl);
        }
        
        this.analysisContent.appendChild(section);
    }
    
    renderSummary(summary) {
        const section = this.createSection('📋 文档摘要', 'summary');
        
        if (typeof summary === 'string') {
            const summaryEl = document.createElement('p');
            summaryEl.className = 'summary-text';
            summaryEl.textContent = summary;
            section.appendChild(summaryEl);
        } else if (summary && typeof summary === 'object') {
            if (summary.brief_summary) {
                const briefEl = document.createElement('div');
                briefEl.className = 'summary-brief';
                briefEl.innerHTML = `<h4>简要摘要</h4><p>${summary.brief_summary}</p>`;
                section.appendChild(briefEl);
            }
            
            if (summary.key_points && summary.key_points.length > 0) {
                const pointsEl = document.createElement('div');
                pointsEl.className = 'summary-points';
                pointsEl.innerHTML = '<h4>要点</h4>';
                
                const pointsList = document.createElement('ul');
                summary.key_points.forEach(point => {
                    const pointEl = document.createElement('li');
                    pointEl.textContent = point;
                    pointsList.appendChild(pointEl);
                });
                
                pointsEl.appendChild(pointsList);
                section.appendChild(pointsEl);
            }
        }
        
        this.analysisContent.appendChild(section);
    }
    
    createSection(title, className) {
        const section = document.createElement('div');
        section.className = `analysis-section ${className}`;
        
        const header = document.createElement('h3');
        header.textContent = title;
        section.appendChild(header);
        
        return section;
    }
    
    createEmptyMessage(message) {
        const emptyEl = document.createElement('p');
        emptyEl.className = 'empty-message';
        emptyEl.textContent = message;
        return emptyEl;
    }
    
    getSentimentText(sentiment) {
        const sentimentMap = {
            'positive': '积极',
            'negative': '消极',
            'neutral': '中性'
        };
        return sentimentMap[sentiment] || '未知';
    }
    
    getEntityTypeText(type) {
        const typeMap = {
            'PERSON': '人名',
            'ORG': '组织',
            'GPE': '地名',
            'DATE': '日期',
            'TIME': '时间',
            'MONEY': '金额',
            'EMAIL': '邮箱',
            'PHONE': '电话',
            'URL': '网址',
            'OTHER': '其他'
        };
        return typeMap[type] || type;
    }
    
    handleWorkflowCompleted(message) {
        if (message.document_id && message.results) {
            this.analysisResults[message.document_id] = message.results;
            
            if (this.currentDocument && this.currentDocument.id === message.document_id) {
                this.renderAnalysisResults(message.results);
            }
            
            this.app.showNotification('文档分析完成', 'success');
        }
    }
    
    handleWorkflowProgress(message) {
        if (message.step && message.document_id) {
            const stepText = {
                'parsing': '文档解析中...',
                'analysis': '内容分析中...',
                'summary': '生成摘要中...'
            };
            
            if (this.currentDocument && this.currentDocument.id === message.document_id) {
                this.showLoading(stepText[message.step] || '处理中...');
            }
        }
    }
    
    async refreshAnalysis() {
        if (!this.currentDocument) {
            this.app.showNotification('请先选择一个文档', 'warning');
            return;
        }
        
        await this.loadAnalysisResults(this.currentDocument.id);
    }
    
    async exportAnalysis() {
        if (!this.currentDocument || !this.analysisResults[this.currentDocument.id]) {
            this.app.showNotification('没有分析结果可导出', 'warning');
            return;
        }
        
        try {
            const analysisData = {
                document: this.currentDocument,
                results: this.analysisResults[this.currentDocument.id],
                exported_at: new Date().toISOString()
            };
            
            const content = JSON.stringify(analysisData, null, 2);
            const filename = `analysis_${this.currentDocument.name}_${new Date().toISOString().slice(0, 10)}.json`;
            
            // 使用 Electron API 保存文件
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                const result = await ipcRenderer.invoke('show-save-dialog', {
                    defaultPath: filename,
                    filters: [
                        { name: 'JSON Files', extensions: ['json'] },
                        { name: 'All Files', extensions: ['*'] }
                    ]
                });
                
                if (!result.canceled) {
                    await ipcRenderer.invoke('write-file', result.filePath, content);
                    this.app.showNotification('分析结果导出成功', 'success');
                }
            } else {
                // Web 环境下载
                const blob = new Blob([content], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
                
                this.app.showNotification('分析结果导出成功', 'success');
            }
            
        } catch (error) {
            console.error('❌ 导出分析结果失败:', error);
            this.app.showNotification('导出失败: ' + error.message, 'error');
        }
    }
    
    showEmptyState(message = '请选择文档查看分析结果') {
        if (!this.analysisContent) return;
        
        this.analysisContent.innerHTML = `
            <div class="empty-state">
                <p>${message}</p>
            </div>
        `;
    }
    
    showLoading(message = '加载中...') {
        if (!this.analysisContent) return;
        
        this.analysisContent.innerHTML = `
            <div class="loading">
                <p>${message}</p>
            </div>
        `;
    }
    
    showError(message) {
        if (!this.analysisContent) return;
        
        this.analysisContent.innerHTML = `
            <div class="error-state">
                <p>❌ ${message}</p>
                <button class="btn btn-small" onclick="this.parentElement.parentElement.innerHTML = ''">关闭</button>
            </div>
        `;
    }
    
    hideLoading() {
        // 加载状态会被新内容替换，无需特殊处理
    }
    
    onTabActivated() {
        // 标签页激活时刷新当前文档的分析结果
        if (this.currentDocument) {
            this.loadAnalysisResults(this.currentDocument.id);
        }
    }
    
    getStats() {
        return {
            currentDocument: this.currentDocument?.id || null,
            analysisResultsCount: Object.keys(this.analysisResults).length,
            hasResults: this.currentDocument && !!this.analysisResults[this.currentDocument.id]
        };
    }
}

// 导出到全局
window.AnalysisModule = AnalysisModule;