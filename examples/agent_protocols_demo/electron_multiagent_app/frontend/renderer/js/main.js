/**
 * 主应用脚本
 */

class MultiAgentApp {
    constructor() {
        this.config = {
            backendUrl: 'http://localhost:8000',
            websocketUrl: 'ws://localhost:8000',
            openaiApiKey: '',
            openaiModel: 'gpt-3.5-turbo',
            maxTokens: 2000,
            temperature: 0.7
        };
        
        this.websocket = null;
        this.isConnected = false;
        this.currentDocument = null;
        this.agents = {};
        this.workflows = {};
        
        this.init();
    }
    
    async init() {
        console.log('🚀 初始化多智能体应用...');
        
        try {
            // 加载配置
            await this.loadConfig();
            
            // 初始化 UI
            this.initUI();
            
            // 初始化各个模块
            this.initModules();
            
            // 连接后端服务
            await this.connectToBackend();
            
            // 加载系统信息
            await this.loadSystemInfo();
            
            console.log('✅ 应用初始化完成');
            this.showNotification('应用启动成功', 'success');
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showNotification('应用初始化失败: ' + error.message, 'error');
        }
    }
    
    async loadConfig() {
        try {
            // 从 Electron 主进程获取配置
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                const appConfig = await ipcRenderer.invoke('get-app-config');
                Object.assign(this.config, appConfig);
            }
            
            // 从本地存储加载用户配置
            const savedConfig = localStorage.getItem('app-config');
            if (savedConfig) {
                const userConfig = JSON.parse(savedConfig);
                Object.assign(this.config, userConfig);
            }
            
            console.log('📋 配置加载完成:', this.config);
            
        } catch (error) {
            console.warn('⚠️ 配置加载失败，使用默认配置:', error);
        }
    }
    
    async saveConfig() {
        try {
            // 保存到本地存储
            localStorage.setItem('app-config', JSON.stringify(this.config));
            
            // 通知 Electron 主进程
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                await ipcRenderer.invoke('update-app-config', this.config);
            }
            
            console.log('💾 配置保存成功');
            
        } catch (error) {
            console.error('❌ 配置保存失败:', error);
            throw error;
        }
    }
    
    initUI() {
        // 初始化标签页切换
        this.initTabs();
        
        // 初始化模态对话框
        this.initModals();
        
        // 初始化工具栏按钮
        this.initToolbar();
        
        // 初始化键盘快捷键
        this.initKeyboardShortcuts();
        
        // 初始化拖拽上传
        this.initDragAndDrop();
        
        console.log('🎨 UI 初始化完成');
    }
    
    initTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === tabId + 'Tab') {
                        content.classList.add('active');
                    }
                });
                
                // 触发标签页切换事件
                this.onTabChange(tabId);
            });
        });
    }
    
    initModals() {
        // 配置模态框
        const configBtn = document.getElementById('configBtn');
        const configModal = document.getElementById('configModal');
        const closeConfigModal = document.getElementById('closeConfigModal');
        const cancelConfigBtn = document.getElementById('cancelConfigBtn');
        
        configBtn?.addEventListener('click', () => {
            this.showConfigModal();
        });
        
        closeConfigModal?.addEventListener('click', () => {
            this.hideConfigModal();
        });
        
        cancelConfigBtn?.addEventListener('click', () => {
            this.hideConfigModal();
        });
        
        // 点击模态框外部关闭
        configModal?.addEventListener('click', (e) => {
            if (e.target === configModal) {
                this.hideConfigModal();
            }
        });
    }
    
    initToolbar() {
        // 连接状态显示
        this.updateConnectionStatus(false);
    }
    
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 发送消息
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const chatInput = document.getElementById('chatInput');
                if (chatInput === document.activeElement) {
                    this.sendChatMessage();
                }
            }
            
            // Esc 关闭模态框
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }
    
    initDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                
                const files = Array.from(e.dataTransfer.files);
                this.handleFileUpload(files);
            });
            
            uploadArea.addEventListener('click', () => {
                document.getElementById('fileInput')?.click();
            });
        }
        
        const fileInput = document.getElementById('fileInput');
        fileInput?.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFileUpload(files);
        });
    }
    
    initModules() {
        // 初始化各个功能模块
        if (window.ChatModule) {
            this.chatModule = new ChatModule(this);
        }
        
        if (window.DocumentModule) {
            this.documentModule = new DocumentModule(this);
        }
        
        if (window.AnalysisModule) {
            this.analysisModule = new AnalysisModule(this);
        }
        
        if (window.WorkflowModule) {
            this.workflowModule = new WorkflowModule(this);
        }
        
        if (window.ConfigModule) {
            this.configModule = new ConfigModule(this);
        }
        
        console.log('🔧 模块初始化完成');
    }
    
    async connectToBackend() {
        try {
            this.updateConnectionStatus(false, '连接中...');
            
            // 测试 HTTP 连接
            const response = await fetch(`${this.config.backendUrl}/agents/status`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 建立 WebSocket 连接
            await this.connectWebSocket();
            
            // 加载智能体状态
            await this.loadAgentStatus();
            
            this.updateConnectionStatus(true, '已连接');
            console.log('🔗 后端连接成功');
            
        } catch (error) {
            console.error('❌ 后端连接失败:', error);
            this.updateConnectionStatus(false, '连接失败');
            this.showNotification('无法连接到后端服务，请检查服务是否启动', 'error');
        }
    }
    
    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            const clientId = 'electron_client_' + Date.now();
            const wsUrl = `${this.config.websocketUrl}/ws/${clientId}`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('🔌 WebSocket 连接已建立');
                this.isConnected = true;
                resolve();
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    console.error('❌ WebSocket 消息解析失败:', error);
                }
            };
            
            this.websocket.onclose = () => {
                console.log('🔌 WebSocket 连接已关闭');
                this.isConnected = false;
                this.updateConnectionStatus(false, '连接断开');
                
                // 尝试重连
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connectWebSocket().catch(console.error);
                    }
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket 连接错误:', error);
                reject(error);
            };
            
            // 连接超时
            setTimeout(() => {
                if (this.websocket.readyState !== WebSocket.OPEN) {
                    reject(new Error('WebSocket 连接超时'));
                }
            }, 10000);
        });
    }
    
    handleWebSocketMessage(message) {
        console.log('📨 收到 WebSocket 消息:', message);
        
        switch (message.type) {
            case 'system':
                this.handleSystemMessage(message);
                break;
            case 'chat_response':
                this.chatModule?.handleChatResponse(message);
                break;
            case 'workflow_started':
            case 'workflow_progress':
            case 'workflow_completed':
            case 'workflow_error':
                this.workflowModule?.handleWorkflowMessage(message);
                break;
            case 'agent_status_update':
                this.handleAgentStatusUpdate(message);
                break;
            default:
                console.log('🤷 未知消息类型:', message.type);
        }
    }
    
    handleSystemMessage(message) {
        console.log('🖥️ 系统消息:', message.content);
        
        if (message.agents_status) {
            this.updateAgentStatus(message.agents_status);
        }
        
        this.updateStatus(message.content);
    }
    
    handleAgentStatusUpdate(message) {
        if (message.agents) {
            this.updateAgentStatus(message.agents);
        }
    }
    
    async loadAgentStatus() {
        try {
            const response = await fetch(`${this.config.backendUrl}/api/agents/status`);
            const data = await response.json();
            
            if (data.success) {
                this.updateAgentStatus(data.agents);
            }
            
        } catch (error) {
            console.error('❌ 加载智能体状态失败:', error);
        }
    }
    
    updateAgentStatus(agents) {
        this.agents = agents;
        
        const agentList = document.getElementById('agentList');
        if (!agentList) return;
        
        agentList.innerHTML = '';
        
        Object.entries(agents).forEach(([agentId, agent]) => {
            const agentItem = document.createElement('div');
            agentItem.className = 'agent-item';
            
            const statusClass = agent.status === 'idle' ? 'online' : 
                               agent.status === 'busy' ? 'connecting' : 'offline';
            
            agentItem.innerHTML = `
                <div class="agent-header">
                    <span class="agent-name">${agent.type}</span>
                    <span class="status-indicator ${statusClass}"></span>
                </div>
                <div class="agent-details">
                    <small>状态: ${agent.status}</small>
                    <small>消息: ${agent.sent_messages}/${agent.received_messages}</small>
                </div>
            `;
            
            agentList.appendChild(agentItem);
        });
    }
    
    async loadSystemInfo() {
        try {
            let systemInfo = '系统信息加载中...';
            
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                const info = await ipcRenderer.invoke('get-system-info');
                systemInfo = `${info.platform} | Node ${info.nodeVersion} | Electron ${info.electronVersion}`;
            }
            
            document.getElementById('systemInfo').textContent = systemInfo;
            
        } catch (error) {
            console.error('❌ 加载系统信息失败:', error);
        }
    }
    
    updateConnectionStatus(connected, message = '') {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${connected ? 'online' : 'offline'}`;
        }
        
        if (statusText) {
            statusText.textContent = message || (connected ? '已连接' : '未连接');
        }
        
        this.isConnected = connected;
    }
    
    updateStatus(message) {
        const statusText = document.getElementById('statusText');
        if (statusText) {
            statusText.textContent = message;
        }
    }
    
    onTabChange(tabId) {
        console.log('📑 切换到标签页:', tabId);
        
        switch (tabId) {
            case 'chat':
                this.chatModule?.onTabActivated();
                break;
            case 'analysis':
                this.analysisModule?.onTabActivated();
                break;
            case 'workflow':
                this.workflowModule?.onTabActivated();
                break;
        }
    }
    
    async handleFileUpload(files) {
        if (!files || files.length === 0) return;
        
        console.log('📁 处理文件上传:', files);
        
        if (this.documentModule) {
            await this.documentModule.uploadFiles(files);
        } else {
            this.showNotification('文档模块未初始化', 'error');
        }
    }
    
    showConfigModal() {
        const modal = document.getElementById('configModal');
        if (modal && this.configModule) {
            this.configModule.showModal();
        }
    }
    
    hideConfigModal() {
        const modal = document.getElementById('configModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }
    
    hideAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('show');
        });
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        container.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }
    
    showLoading(message = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        
        if (overlay) {
            overlay.classList.add('show');
        }
        
        if (text) {
            text.textContent = message;
        }
    }
    
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }
    
    async sendWebSocketMessage(message) {
        if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket 连接未建立');
        }
        
        this.websocket.send(JSON.stringify(message));
    }
    
    async apiRequest(endpoint, options = {}) {
        const url = `${this.config.backendUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        
        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
        }
        
        return response.json();
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MultiAgentApp();
});

// 导出到全局
window.MultiAgentApp = MultiAgentApp;