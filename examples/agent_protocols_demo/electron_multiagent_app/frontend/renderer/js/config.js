/**
 * 配置管理模块
 */

class ConfigModule {
    constructor(app) {
        this.app = app;
        this.config = {};
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initEventListeners();
        this.loadConfig();
    }
    
    initElements() {
        this.configModal = document.getElementById('configModal');
        this.configForm = document.getElementById('configForm');
        this.saveConfigBtn = document.getElementById('saveConfigBtn');
        this.cancelConfigBtn = document.getElementById('cancelConfigBtn');
        this.testConnectionBtn = document.getElementById('testConnectionBtn');
        
        // 表单字段
        this.openaiApiKeyInput = document.getElementById('openaiApiKey');
        this.openaiModelSelect = document.getElementById('openaiModel');
        this.maxTokensInput = document.getElementById('maxTokens');
        this.temperatureInput = document.getElementById('temperature');
        this.temperatureValue = document.getElementById('temperatureValue');
        this.backendUrlInput = document.getElementById('backendUrl');
    }
    
    initEventListeners() {
        // 保存配置
        this.saveConfigBtn?.addEventListener('click', () => {
            this.saveConfig();
        });
        
        // 取消配置
        this.cancelConfigBtn?.addEventListener('click', () => {
            this.hideModal();
        });
        
        // 测试连接
        this.testConnectionBtn?.addEventListener('click', () => {
            this.testConnection();
        });
        
        // Temperature 滑块
        this.temperatureInput?.addEventListener('input', (e) => {
            if (this.temperatureValue) {
                this.temperatureValue.textContent = e.target.value;
            }
        });
        
        // 表单验证
        this.configForm?.addEventListener('input', () => {
            this.validateForm();
        });
        
        // API Key 输入框特殊处理
        this.openaiApiKeyInput?.addEventListener('input', (e) => {
            const value = e.target.value;
            if (value && !value.startsWith('sk-')) {
                this.showFieldError(e.target, 'OpenAI API Key 应该以 "sk-" 开头');
            } else {
                this.clearFieldError(e.target);
            }
        });
        
        // 后端 URL 验证
        this.backendUrlInput?.addEventListener('input', (e) => {
            const value = e.target.value;
            if (value && !Utils.isValidUrl(value)) {
                this.showFieldError(e.target, '请输入有效的 URL 地址');
            } else {
                this.clearFieldError(e.target);
            }
        });
    }
    
    showModal() {
        if (!this.configModal) return;
        
        // 填充当前配置
        this.populateForm();
        
        // 显示模态框
        this.configModal.classList.add('show');
        
        // 聚焦到第一个输入框
        const firstInput = this.configModal.querySelector('input:not([type="range"])');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
    
    hideModal() {
        if (!this.configModal) return;
        
        this.configModal.classList.remove('show');
        this.clearFormErrors();
    }
    
    populateForm() {
        if (this.openaiApiKeyInput) {
            this.openaiApiKeyInput.value = this.config.openaiApiKey || '';
        }
        
        if (this.openaiModelSelect) {
            this.openaiModelSelect.value = this.config.openaiModel || 'gpt-3.5-turbo';
        }
        
        if (this.maxTokensInput) {
            this.maxTokensInput.value = this.config.maxTokens || 2000;
        }
        
        if (this.temperatureInput) {
            this.temperatureInput.value = this.config.temperature || 0.7;
            if (this.temperatureValue) {
                this.temperatureValue.textContent = this.config.temperature || 0.7;
            }
        }
        
        if (this.backendUrlInput) {
            this.backendUrlInput.value = this.config.backendUrl || 'http://localhost:8000';
        }
    }
    
    async saveConfig() {
        try {
            // 验证表单
            if (!this.validateForm()) {
                return;
            }
            
            // 收集配置数据
            const newConfig = {
                openaiApiKey: this.openaiApiKeyInput?.value.trim() || '',
                openaiModel: this.openaiModelSelect?.value || 'gpt-3.5-turbo',
                maxTokens: parseInt(this.maxTokensInput?.value) || 2000,
                temperature: parseFloat(this.temperatureInput?.value) || 0.7,
                backendUrl: this.backendUrlInput?.value.trim() || 'http://localhost:8000'
            };
            
            // 显示保存状态
            this.setSaveButtonState('saving');
            
            // 更新应用配置
            Object.assign(this.app.config, newConfig);
            Object.assign(this.config, newConfig);
            
            // 保存到本地存储
            await this.app.saveConfig();
            
            // 发送配置到后端
            if (this.app.websocket && this.app.websocket.isConnected()) {
                this.app.websocket.sendConfig(newConfig);
            } else {
                // 使用 HTTP API
                await this.app.apiRequest('/api/config', {
                    method: 'POST',
                    body: JSON.stringify(newConfig)
                });
            }
            
            this.setSaveButtonState('success');
            
            // 延迟关闭模态框
            setTimeout(() => {
                this.hideModal();
                this.setSaveButtonState('normal');
            }, 1000);
            
            this.app.showNotification('配置保存成功', 'success');
            
        } catch (error) {
            console.error('❌ 保存配置失败:', error);
            this.setSaveButtonState('error');
            this.app.showNotification('保存配置失败: ' + error.message, 'error');
            
            setTimeout(() => {
                this.setSaveButtonState('normal');
            }, 2000);
        }
    }
    
    async testConnection() {
        try {
            this.setTestButtonState('testing');
            
            // 测试后端连接
            const backendUrl = this.backendUrlInput?.value.trim() || this.config.backendUrl;
            
            const response = await fetch(`${backendUrl}/api/agents/status`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            this.setTestButtonState('success');
            this.app.showNotification('后端连接测试成功', 'success');
            
            // 如果配置了 OpenAI API Key，也测试一下
            const apiKey = this.openaiApiKeyInput?.value.trim();
            if (apiKey) {
                await this.testOpenAIConnection(apiKey);
            }
            
        } catch (error) {
            console.error('❌ 连接测试失败:', error);
            this.setTestButtonState('error');
            this.app.showNotification('连接测试失败: ' + error.message, 'error');
        } finally {
            setTimeout(() => {
                this.setTestButtonState('normal');
            }, 2000);
        }
    }
    
    async testOpenAIConnection(apiKey) {
        try {
            const response = await fetch(`${this.config.backendUrl}/api/config/test-openai`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    openai_api_key: apiKey,
                    openai_model: this.openaiModelSelect?.value || 'gpt-3.5-turbo'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.app.showNotification('OpenAI API 连接测试成功', 'success');
            } else {
                this.app.showNotification('OpenAI API 测试失败: ' + result.error, 'warning');
            }
            
        } catch (error) {
            console.error('❌ OpenAI 连接测试失败:', error);
            this.app.showNotification('OpenAI API 测试失败', 'warning');
        }
    }
    
    validateForm() {
        if (!this.configForm) return true;
        
        let isValid = true;
        
        // 验证后端 URL
        const backendUrl = this.backendUrlInput?.value.trim();
        if (backendUrl && !Utils.isValidUrl(backendUrl)) {
            this.showFieldError(this.backendUrlInput, '请输入有效的 URL 地址');
            isValid = false;
        } else {
            this.clearFieldError(this.backendUrlInput);
        }
        
        // 验证 Max Tokens
        const maxTokens = parseInt(this.maxTokensInput?.value);
        if (maxTokens && (maxTokens < 1 || maxTokens > 4000)) {
            this.showFieldError(this.maxTokensInput, 'Token 数量应在 1-4000 之间');
            isValid = false;
        } else {
            this.clearFieldError(this.maxTokensInput);
        }
        
        // 验证 Temperature
        const temperature = parseFloat(this.temperatureInput?.value);
        if (temperature && (temperature < 0 || temperature > 1)) {
            this.showFieldError(this.temperatureInput, 'Temperature 应在 0-1 之间');
            isValid = false;
        } else {
            this.clearFieldError(this.temperatureInput);
        }
        
        // 更新保存按钮状态
        if (this.saveConfigBtn) {
            this.saveConfigBtn.disabled = !isValid;
        }
        
        return isValid;
    }
    
    showFieldError(field, message) {
        if (!field) return;
        
        this.clearFieldError(field);
        
        field.classList.add('error');
        
        const errorEl = document.createElement('div');
        errorEl.className = 'field-error';
        errorEl.textContent = message;
        
        field.parentNode.appendChild(errorEl);
    }
    
    clearFieldError(field) {
        if (!field) return;
        
        field.classList.remove('error');
        
        const errorEl = field.parentNode.querySelector('.field-error');
        if (errorEl) {
            errorEl.remove();
        }
    }
    
    clearFormErrors() {
        if (!this.configForm) return;
        
        const errorFields = this.configForm.querySelectorAll('.error');
        errorFields.forEach(field => {
            this.clearFieldError(field);
        });
    }
    
    setSaveButtonState(state) {
        if (!this.saveConfigBtn) return;
        
        const states = {
            normal: { text: '保存', disabled: false, className: '' },
            saving: { text: '保存中...', disabled: true, className: 'btn-loading' },
            success: { text: '保存成功', disabled: true, className: 'btn-success' },
            error: { text: '保存失败', disabled: false, className: 'btn-error' }
        };
        
        const config = states[state] || states.normal;
        
        this.saveConfigBtn.textContent = config.text;
        this.saveConfigBtn.disabled = config.disabled;
        this.saveConfigBtn.className = `btn btn-primary ${config.className}`;
    }
    
    setTestButtonState(state) {
        if (!this.testConnectionBtn) return;
        
        const states = {
            normal: { text: '测试连接', disabled: false, className: '' },
            testing: { text: '测试中...', disabled: true, className: 'btn-loading' },
            success: { text: '连接成功', disabled: true, className: 'btn-success' },
            error: { text: '连接失败', disabled: false, className: 'btn-error' }
        };
        
        const config = states[state] || states.normal;
        
        this.testConnectionBtn.textContent = config.text;
        this.testConnectionBtn.disabled = config.disabled;
        this.testConnectionBtn.className = `btn btn-small ${config.className}`;
    }
    
    loadConfig() {
        try {
            // 从应用配置加载
            this.config = { ...this.app.config };
            
            // 从本地存储加载用户配置
            const savedConfig = Utils.StorageUtils.get('user-config', {});
            Object.assign(this.config, savedConfig);
            
        } catch (error) {
            console.error('❌ 加载配置失败:', error);
        }
    }
    
    exportConfig() {
        try {
            const configData = {
                exported_at: new Date().toISOString(),
                version: '1.0.0',
                config: {
                    ...this.config,
                    openaiApiKey: this.config.openaiApiKey ? '***' : '' // 隐藏敏感信息
                }
            };
            
            const content = JSON.stringify(configData, null, 2);
            const filename = `config_export_${new Date().toISOString().slice(0, 10)}.json`;
            
            // 下载配置文件
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
            
            this.app.showNotification('配置导出成功', 'success');
            
        } catch (error) {
            console.error('❌ 导出配置失败:', error);
            this.app.showNotification('导出配置失败: ' + error.message, 'error');
        }
    }
    
    async importConfig(file) {
        try {
            const content = await file.text();
            const configData = JSON.parse(content);
            
            if (configData.config) {
                // 验证配置格式
                const importedConfig = configData.config;
                
                // 合并配置（排除敏感信息）
                const safeConfig = {
                    openaiModel: importedConfig.openaiModel,
                    maxTokens: importedConfig.maxTokens,
                    temperature: importedConfig.temperature,
                    backendUrl: importedConfig.backendUrl
                };
                
                Object.assign(this.config, safeConfig);
                Object.assign(this.app.config, safeConfig);
                
                // 保存配置
                await this.app.saveConfig();
                
                // 更新表单
                this.populateForm();
                
                this.app.showNotification('配置导入成功', 'success');
            } else {
                throw new Error('无效的配置文件格式');
            }
            
        } catch (error) {
            console.error('❌ 导入配置失败:', error);
            this.app.showNotification('导入配置失败: ' + error.message, 'error');
        }
    }
    
    resetConfig() {
        const defaultConfig = {
            openaiApiKey: '',
            openaiModel: 'gpt-3.5-turbo',
            maxTokens: 2000,
            temperature: 0.7,
            backendUrl: 'http://localhost:8000'
        };
        
        Object.assign(this.config, defaultConfig);
        Object.assign(this.app.config, defaultConfig);
        
        this.populateForm();
        this.app.showNotification('配置已重置为默认值', 'info');
    }
    
    getConfigSummary() {
        return {
            hasOpenAIKey: !!this.config.openaiApiKey,
            model: this.config.openaiModel,
            maxTokens: this.config.maxTokens,
            temperature: this.config.temperature,
            backendUrl: this.config.backendUrl
        };
    }
    
    getStats() {
        return {
            configLoaded: Object.keys(this.config).length > 0,
            hasOpenAIKey: !!this.config.openaiApiKey,
            backendConfigured: !!this.config.backendUrl
        };
    }
}

// 导出到全局
window.ConfigModule = ConfigModule;