/**
 * API 通信模块
 */

class APIClient {
    constructor(baseURL) {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
        this.timeout = 30000; // 30秒超时
    }
    
    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };
        
        // 添加超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        config.signal = controller.signal;
        
        try {
            console.log(`🌐 API 请求: ${config.method} ${url}`);
            
            const response = await fetch(url, config);
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const contentType = response.headers.get('content-type');
            let data;
            
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }
            
            console.log(`✅ API 响应:`, data);
            return data;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            
            console.error(`❌ API 请求失败: ${config.method} ${url}`, error);
            throw error;
        }
    }
    
    // GET 请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, { method: 'GET' });
    }
    
    // POST 请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    // PUT 请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    // DELETE 请求
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }
    
    // 文件上传
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // 添加额外数据
        Object.entries(additionalData).forEach(([key, value]) => {
            formData.append(key, value);
        });
        
        return this.request(endpoint, {
            method: 'POST',
            body: formData,
            headers: {} // 让浏览器自动设置 Content-Type
        });
    }
    
    // 下载文件
    async downloadFile(endpoint, filename) {
        try {
            const response = await fetch(`${this.baseURL}${endpoint}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'download';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            return true;
            
        } catch (error) {
            console.error('❌ 文件下载失败:', error);
            throw error;
        }
    }
}

// 多智能体 API 客户端
class MultiAgentAPI extends APIClient {
    constructor(baseURL) {
        super(baseURL);
    }
    
    // 智能体相关 API
    async getAgentsStatus() {
        return this.get('/api/agents/status');
    }
    
    async getAgentCapabilities(agentId = null) {
        const params = agentId ? { agent_id: agentId } : {};
        return this.get('/api/agents/capabilities', params);
    }
    
    // 文档相关 API
    async uploadDocument(file) {
        return this.uploadFile('/api/upload', file);
    }
    
    async getDocuments() {
        return this.get('/api/documents');
    }
    
    async getDocument(documentId) {
        return this.get(`/api/documents/${documentId}`);
    }
    
    async deleteDocument(documentId) {
        return this.delete(`/api/documents/${documentId}`);
    }
    
    async analyzeDocument(documentId, analysisType = 'comprehensive') {
        return this.post('/api/analyze', {
            document_id: documentId,
            analysis_type: analysisType
        });
    }
    
    // 配置相关 API
    async updateConfig(config) {
        return this.post('/api/config', config);
    }
    
    async getConfig() {
        return this.get('/api/config');
    }
    
    async testConnection() {
        return this.get('/api/test');
    }
    
    // 对话相关 API
    async sendChatMessage(message, documentId = null) {
        return this.post('/api/chat', {
            message: message,
            document_id: documentId
        });
    }
    
    async getChatHistory(limit = 50) {
        return this.get('/api/chat/history', { limit });
    }
    
    async clearChatHistory() {
        return this.delete('/api/chat/history');
    }
    
    // 工作流相关 API
    async startWorkflow(workflowType, data = {}) {
        return this.post('/api/workflow/start', {
            workflow_type: workflowType,
            data: data
        });
    }
    
    async getWorkflowStatus(workflowId) {
        return this.get(`/api/workflow/${workflowId}/status`);
    }
    
    async getActiveWorkflows() {
        return this.get('/api/workflow/active');
    }
    
    // 分析结果相关 API
    async getAnalysisResults(documentId) {
        return this.get(`/api/analysis/${documentId}`);
    }
    
    async exportAnalysisResults(documentId, format = 'json') {
        return this.get(`/api/analysis/${documentId}/export`, { format });
    }
    
    // 系统相关 API
    async getSystemStats() {
        return this.get('/api/system/stats');
    }
    
    async getSystemHealth() {
        return this.get('/api/system/health');
    }
    
    async getSystemLogs(level = 'info', limit = 100) {
        return this.get('/api/system/logs', { level, limit });
    }
}

// API 错误处理
class APIError extends Error {
    constructor(message, status, response) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.response = response;
    }
}

// API 响应拦截器
class APIInterceptor {
    constructor(apiClient) {
        this.apiClient = apiClient;
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }
    
    // 添加请求拦截器
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    // 添加响应拦截器
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }
    
    // 执行请求拦截器
    async executeRequestInterceptors(config) {
        let result = config;
        for (const interceptor of this.requestInterceptors) {
            result = await interceptor(result);
        }
        return result;
    }
    
    // 执行响应拦截器
    async executeResponseInterceptors(response) {
        let result = response;
        for (const interceptor of this.responseInterceptors) {
            result = await interceptor(result);
        }
        return result;
    }
}

// API 缓存
class APICache {
    constructor(maxSize = 100, ttl = 300000) { // 默认5分钟TTL
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }
    
    // 生成缓存键
    generateKey(method, url, data) {
        return `${method}:${url}:${JSON.stringify(data || {})}`;
    }
    
    // 获取缓存
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }
    
    // 设置缓存
    set(key, data) {
        // 如果缓存已满，删除最旧的项
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data: data,
            expiry: Date.now() + this.ttl
        });
    }
    
    // 清除缓存
    clear() {
        this.cache.clear();
    }
    
    // 删除特定缓存
    delete(key) {
        this.cache.delete(key);
    }
    
    // 获取缓存统计
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            ttl: this.ttl
        };
    }
}

// 导出到全局
window.APIClient = APIClient;
window.MultiAgentAPI = MultiAgentAPI;
window.APIError = APIError;
window.APIInterceptor = APIInterceptor;
window.APICache = APICache;