/**
 * 文档管理模块
 */

class DocumentModule {
    constructor(app) {
        this.app = app;
        this.documents = [];
        this.currentDocument = null;
        this.uploadQueue = [];
        
        this.init();
    }
    
    init() {
        this.initElements();
        this.initEventListeners();
        this.loadDocuments();
    }
    
    initElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.documentList = document.getElementById('documentList');
    }
    
    initEventListeners() {
        // 文件输入
        this.fileInput?.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.uploadFiles(files);
        });
        
        // 拖拽上传
        if (this.uploadArea) {
            this.uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                this.uploadArea.classList.add('drag-over');
            });
            
            this.uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                this.uploadArea.classList.remove('drag-over');
            });
            
            this.uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                this.uploadArea.classList.remove('drag-over');
                
                const files = Array.from(e.dataTransfer.files);
                this.uploadFiles(files);
            });
            
            this.uploadArea.addEventListener('click', () => {
                this.fileInput?.click();
            });
        }
    }
    
    async uploadFiles(files) {
        if (!files || files.length === 0) return;
        
        console.log('📁 开始上传文件:', files);
        
        for (const file of files) {
            await this.uploadFile(file);
        }
    }
    
    async uploadFile(file) {
        try {
            // 验证文件
            const validation = this.validateFile(file);
            if (!validation.valid) {
                this.app.showNotification(`文件 ${file.name} 上传失败: ${validation.error}`, 'error');
                return;
            }
            
            // 创建上传项
            const uploadItem = this.createUploadItem(file);
            this.addDocumentToList(uploadItem);
            
            // 更新进度
            this.updateUploadProgress(uploadItem.id, 0, '准备上传...');
            
            // 上传文件
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch(`${this.app.config.backendUrl}/api/upload`, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                // 上传成功
                const document = {
                    id: result.document_id,
                    name: result.filename,
                    originalName: file.name,
                    size: file.size,
                    type: file.type,
                    uploadedAt: new Date().toISOString(),
                    status: 'uploaded',
                    filePath: result.file_path
                };
                
                this.updateDocumentItem(uploadItem.id, document);
                this.documents.push(document);
                this.saveDocuments();
                
                this.app.showNotification(`文件 ${file.name} 上传成功`, 'success');
                
                // 自动开始分析
                this.startDocumentAnalysis(document);
                
            } else {
                throw new Error(result.message || '上传失败');
            }
            
        } catch (error) {
            console.error('❌ 文件上传失败:', error);
            this.app.showNotification(`文件 ${file.name} 上传失败: ${error.message}`, 'error');
            
            // 更新状态为错误
            const uploadItem = this.findDocumentItem(file.name);
            if (uploadItem) {
                this.updateUploadProgress(uploadItem.id, 0, '上传失败', 'error');
            }
        }
    }
    
    validateFile(file) {
        const maxSize = 50 * 1024 * 1024; // 50MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/markdown'
        ];
        
        const allowedExtensions = ['.pdf', '.doc', '.docx', '.txt', '.md'];
        
        if (file.size > maxSize) {
            return { valid: false, error: '文件大小超过 50MB 限制' };
        }
        
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(extension)) {
            return { valid: false, error: '不支持的文件格式' };
        }
        
        return { valid: true };
    }
    
    createUploadItem(file) {
        return {
            id: Utils.generateId('upload'),
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'uploading',
            progress: 0,
            message: '准备上传...'
        };
    }
    
    addDocumentToList(document) {
        if (!this.documentList) return;
        
        // 移除空状态
        const emptyState = this.documentList.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
        
        const documentEl = document.createElement('div');
        documentEl.className = 'document-item';
        documentEl.dataset.documentId = document.id;
        
        documentEl.innerHTML = `
            <div class="document-header">
                <div class="document-name" title="${document.name}">
                    ${Utils.getFileIcon(document.name)} ${document.name}
                </div>
                <div class="document-status ${document.status}">${this.getStatusText(document.status)}</div>
            </div>
            <div class="document-details">
                <span class="document-size">${Utils.formatFileSize(document.size)}</span>
                <span class="document-time">${Utils.formatTime(document.uploadedAt || new Date())}</span>
            </div>
            <div class="document-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text">准备中...</div>
            </div>
        `;
        
        // 添加点击事件
        documentEl.addEventListener('click', () => {
            this.selectDocument(document);
        });
        
        // 添加右键菜单
        documentEl.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showDocumentContextMenu(e, document);
        });
        
        this.documentList.appendChild(documentEl);
    }
    
    updateDocumentItem(documentId, updates) {
        const documentEl = this.documentList?.querySelector(`[data-document-id="${documentId}"]`);
        if (!documentEl) return;
        
        // 更新状态
        if (updates.status) {
            const statusEl = documentEl.querySelector('.document-status');
            if (statusEl) {
                statusEl.className = `document-status ${updates.status}`;
                statusEl.textContent = this.getStatusText(updates.status);
            }
        }
        
        // 更新名称
        if (updates.name) {
            const nameEl = documentEl.querySelector('.document-name');
            if (nameEl) {
                nameEl.innerHTML = `${Utils.getFileIcon(updates.name)} ${updates.name}`;
                nameEl.title = updates.name;
            }
        }
        
        // 更新时间
        if (updates.uploadedAt) {
            const timeEl = documentEl.querySelector('.document-time');
            if (timeEl) {
                timeEl.textContent = Utils.formatTime(updates.uploadedAt);
            }
        }
        
        // 隐藏进度条
        const progressEl = documentEl.querySelector('.document-progress');
        if (progressEl && updates.status !== 'uploading' && updates.status !== 'processing') {
            progressEl.style.display = 'none';
        }
    }
    
    updateUploadProgress(documentId, progress, message, status = 'uploading') {
        const documentEl = this.documentList?.querySelector(`[data-document-id="${documentId}"]`);
        if (!documentEl) return;
        
        const progressEl = documentEl.querySelector('.document-progress');
        const progressFill = documentEl.querySelector('.progress-fill');
        const progressText = documentEl.querySelector('.progress-text');
        
        if (progressEl) {
            progressEl.style.display = 'block';
        }
        
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
        
        if (progressText) {
            progressText.textContent = message;
        }
        
        // 更新状态
        const statusEl = documentEl.querySelector('.document-status');
        if (statusEl) {
            statusEl.className = `document-status ${status}`;
            statusEl.textContent = this.getStatusText(status);
        }
    }
    
    getStatusText(status) {
        const statusMap = {
            'uploading': '上传中',
            'uploaded': '已上传',
            'processing': '分析中',
            'completed': '已完成',
            'error': '错误'
        };
        
        return statusMap[status] || status;
    }
    
    selectDocument(document) {
        // 移除之前的选中状态
        this.documentList?.querySelectorAll('.document-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 添加选中状态
        const documentEl = this.documentList?.querySelector(`[data-document-id="${document.id}"]`);
        if (documentEl) {
            documentEl.classList.add('active');
        }
        
        this.currentDocument = document;
        
        // 通知其他模块
        if (this.app.chatModule) {
            this.app.chatModule.setCurrentDocument(document);
        }
        
        if (this.app.analysisModule) {
            this.app.analysisModule.setCurrentDocument(document);
        }
        
        this.app.showNotification(`已选择文档: ${document.name}`, 'info', 2000);
    }
    
    async startDocumentAnalysis(document) {
        try {
            this.updateDocumentItem(document.id, { status: 'processing' });
            
            // 发送分析命令
            if (this.app.websocket && this.app.websocket.isConnected()) {
                this.app.websocket.sendCommand('analyze_document', {
                    document_id: document.id
                });
            } else {
                // 使用 HTTP API
                await this.app.apiRequest('/api/analyze', {
                    method: 'POST',
                    body: JSON.stringify({
                        document_id: document.id,
                        analysis_type: 'comprehensive'
                    })
                });
            }
            
        } catch (error) {
            console.error('❌ 启动文档分析失败:', error);
            this.updateDocumentItem(document.id, { status: 'error' });
            this.app.showNotification(`文档分析启动失败: ${error.message}`, 'error');
        }
    }
    
    showDocumentContextMenu(event, document) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.zIndex = '1000';
        
        menu.innerHTML = `
            <div class="context-menu-item" data-action="select">选择文档</div>
            <div class="context-menu-item" data-action="analyze">重新分析</div>
            <div class="context-menu-item" data-action="download">下载文档</div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="delete">删除文档</div>
        `;
        
        // 添加事件监听
        menu.addEventListener('click', async (e) => {
            const action = e.target.dataset.action;
            if (action) {
                await this.handleContextMenuAction(action, document);
            }
            menu.remove();
        });
        
        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 0);
        
        document.body.appendChild(menu);
    }
    
    async handleContextMenuAction(action, document) {
        switch (action) {
            case 'select':
                this.selectDocument(document);
                break;
                
            case 'analyze':
                await this.startDocumentAnalysis(document);
                break;
                
            case 'download':
                await this.downloadDocument(document);
                break;
                
            case 'delete':
                await this.deleteDocument(document);
                break;
        }
    }
    
    async downloadDocument(document) {
        try {
            const response = await fetch(`${this.app.config.backendUrl}/api/documents/${document.id}/download`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = document.originalName || document.name;
            a.click();
            URL.revokeObjectURL(url);
            
            this.app.showNotification('文档下载成功', 'success');
            
        } catch (error) {
            console.error('❌ 文档下载失败:', error);
            this.app.showNotification(`文档下载失败: ${error.message}`, 'error');
        }
    }
    
    async deleteDocument(document) {
        const confirmed = await this.app.uiManager?.showConfirm(
            `确定要删除文档 "${document.name}" 吗？此操作无法撤销。`,
            '删除文档'
        );
        
        if (!confirmed) return;
        
        try {
            const response = await fetch(`${this.app.config.backendUrl}/api/documents/${document.id}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 从列表中移除
            this.removeDocumentFromList(document.id);
            this.documents = this.documents.filter(d => d.id !== document.id);
            this.saveDocuments();
            
            // 如果是当前选中的文档，清除选择
            if (this.currentDocument && this.currentDocument.id === document.id) {
                this.currentDocument = null;
                if (this.app.chatModule) {
                    this.app.chatModule.setCurrentDocument(null);
                }
            }
            
            this.app.showNotification('文档删除成功', 'success');
            
        } catch (error) {
            console.error('❌ 文档删除失败:', error);
            this.app.showNotification(`文档删除失败: ${error.message}`, 'error');
        }
    }
    
    removeDocumentFromList(documentId) {
        const documentEl = this.documentList?.querySelector(`[data-document-id="${documentId}"]`);
        if (documentEl) {
            documentEl.remove();
        }
        
        // 如果没有文档了，显示空状态
        if (this.documentList && this.documentList.children.length === 0) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = '<p>暂无文档</p>';
            this.documentList.appendChild(emptyState);
        }
    }
    
    findDocumentItem(name) {
        return this.documents.find(doc => doc.name === name || doc.originalName === name);
    }
    
    async loadDocuments() {
        try {
            // 从本地存储加载
            const saved = Utils.StorageUtils.get('documents', []);
            this.documents = saved;
            
            // 渲染文档列表
            this.documents.forEach(document => {
                this.addDocumentToList(document);
            });
            
            // 从服务器同步
            const response = await this.app.apiRequest('/api/documents');
            if (response.success) {
                this.documents = response.documents;
                this.saveDocuments();
                this.refreshDocumentList();
            }
            
        } catch (error) {
            console.error('❌ 加载文档列表失败:', error);
        }
    }
    
    refreshDocumentList() {
        if (!this.documentList) return;
        
        this.documentList.innerHTML = '';
        
        if (this.documents.length === 0) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = '<p>暂无文档</p>';
            this.documentList.appendChild(emptyState);
        } else {
            this.documents.forEach(document => {
                this.addDocumentToList(document);
            });
        }
    }
    
    saveDocuments() {
        try {
            Utils.StorageUtils.set('documents', this.documents);
        } catch (error) {
            console.error('❌ 保存文档列表失败:', error);
        }
    }
    
    getStats() {
        return {
            totalDocuments: this.documents.length,
            uploadedDocuments: this.documents.filter(d => d.status === 'uploaded').length,
            processingDocuments: this.documents.filter(d => d.status === 'processing').length,
            completedDocuments: this.documents.filter(d => d.status === 'completed').length,
            errorDocuments: this.documents.filter(d => d.status === 'error').length,
            currentDocument: this.currentDocument?.id || null,
            uploadQueueSize: this.uploadQueue.length
        };
    }
}

// 导出到全局
window.DocumentModule = DocumentModule;