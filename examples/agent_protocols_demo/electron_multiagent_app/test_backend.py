#!/usr/bin/env python3
"""
后端功能测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加后端路径到 Python 路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from agents.agent_manager import AgentManager
    from protocols.ag_ui_protocol import AGUIProtocol
    from services.openai_service import OpenAIService
    from services.document_service import DocumentService
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r backend/requirements.txt")
    sys.exit(1)


async def test_agent_manager():
    """测试智能体管理器"""
    print("\n🤖 测试智能体管理器...")
    
    try:
        manager = AgentManager()
        await manager.initialize()
        
        # 获取智能体状态
        status = manager.get_agents_status()
        print(f"✅ 智能体数量: {len(status)}")
        
        for agent_id, agent_status in status.items():
            print(f"  - {agent_id}: {agent_status['status']}")
        
        # 测试健康检查
        health = await manager.health_check()
        healthy_count = sum(1 for h in health.values() if h)
        print(f"✅ 健康智能体: {healthy_count}/{len(health)}")
        
        await manager.shutdown()
        print("✅ 智能体管理器测试完成")
        
    except Exception as e:
        print(f"❌ 智能体管理器测试失败: {e}")


async def test_ag_ui_protocol():
    """测试 AG-UI 协议"""
    print("\n🔗 测试 AG-UI 协议...")
    
    try:
        protocol = AGUIProtocol()
        
        # 创建模拟智能体
        from agents.chat_agent import ChatAgent
        chat_agent = ChatAgent()
        await chat_agent.start()
        
        # 注册智能体
        protocol.register_agent("test_chat_agent", chat_agent)
        
        # 测试命令发送
        response = await protocol.send_command_to_agent(
            "test_chat_agent",
            "process_message",
            {"message": "Hello, this is a test!", "client_id": "test_client"}
        )
        
        print(f"✅ 命令响应: {response['success']}")
        if response['success']:
            print(f"  响应内容: {response['data']['response'][:100]}...")
        
        # 测试协议统计
        stats = protocol.get_protocol_stats()
        print(f"✅ 协议统计: {stats}")
        
        await chat_agent.stop()
        print("✅ AG-UI 协议测试完成")
        
    except Exception as e:
        print(f"❌ AG-UI 协议测试失败: {e}")


def test_openai_service():
    """测试 OpenAI 服务"""
    print("\n🧠 测试 OpenAI 服务...")
    
    try:
        service = OpenAIService()
        
        # 检查配置状态
        config = service.get_config()
        print(f"✅ OpenAI 配置: {config}")
        
        if not config['is_configured']:
            print("⚠️ OpenAI API Key 未配置，跳过 API 测试")
        else:
            print("✅ OpenAI 服务配置正常")
        
        print("✅ OpenAI 服务测试完成")
        
    except Exception as e:
        print(f"❌ OpenAI 服务测试失败: {e}")


def test_document_service():
    """测试文档服务"""
    print("\n📄 测试文档服务...")
    
    try:
        service = DocumentService()
        
        # 测试统计信息
        stats = service.get_document_stats()
        print(f"✅ 文档统计: {stats}")
        
        # 创建测试文档
        test_content = "这是一个测试文档内容。\n包含多行文本用于测试。"
        test_file = Path("test_document.txt")
        test_file.write_text(test_content, encoding='utf-8')
        
        # 注册测试文档
        doc_id = service.generate_document_id("test_document.txt")
        doc_info = service.register_document(doc_id, test_file, "test_document.txt")
        print(f"✅ 文档注册: {doc_info['id']}")
        
        # 获取文档列表
        documents = service.get_all_documents()
        print(f"✅ 文档数量: {len(documents)}")
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
        service.delete_document(doc_id)
        
        print("✅ 文档服务测试完成")
        
    except Exception as e:
        print(f"❌ 文档服务测试失败: {e}")


async def test_agent_communication():
    """测试智能体间通信"""
    print("\n💬 测试智能体间通信...")
    
    try:
        from agents.chat_agent import ChatAgent
        from agents.content_analyzer_agent import ContentAnalyzerAgent
        
        # 创建智能体
        chat_agent = ChatAgent()
        analyzer_agent = ContentAnalyzerAgent()
        
        await chat_agent.start()
        await analyzer_agent.start()
        
        # 设置智能体注册表
        registry = {
            "chat_agent": chat_agent,
            "analyzer_agent": analyzer_agent
        }
        
        chat_agent.set_agent_registry(registry)
        analyzer_agent.set_agent_registry(registry)
        
        # 测试协作请求
        success = await chat_agent.request_collaboration(
            "analyzer_agent",
            "需要分析文本内容",
            {"text": "这是需要分析的文本"}
        )
        
        print(f"✅ 协作请求发送: {success}")
        
        # 等待消息处理
        await asyncio.sleep(1)
        
        # 检查消息统计
        chat_status = chat_agent.get_status()
        analyzer_status = analyzer_agent.get_status()
        
        print(f"✅ 聊天智能体消息: 发送 {chat_status['sent_messages']}, 接收 {chat_status['received_messages']}")
        print(f"✅ 分析智能体消息: 发送 {analyzer_status['sent_messages']}, 接收 {analyzer_status['received_messages']}")
        
        await chat_agent.stop()
        await analyzer_agent.stop()
        
        print("✅ 智能体通信测试完成")
        
    except Exception as e:
        print(f"❌ 智能体通信测试失败: {e}")


async def test_workflow():
    """测试工作流"""
    print("\n🔄 测试工作流...")
    
    try:
        manager = AgentManager()
        await manager.initialize()
        
        # 获取协调智能体
        coordinator = manager.get_agent("coordinator")
        if coordinator:
            # 测试工作流命令
            result = await coordinator.handle_ui_command(
                "start_document_analysis",
                {"document_id": "test_doc_123"}
            )
            
            print(f"✅ 工作流启动: {result['success']}")
            
            # 等待工作流处理
            await asyncio.sleep(2)
            
        await manager.shutdown()
        print("✅ 工作流测试完成")
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")


async def main():
    """主测试函数"""
    print("🧪 多智能体后端功能测试")
    print("=" * 50)
    
    # 运行各项测试
    await test_agent_manager()
    await test_ag_ui_protocol()
    test_openai_service()
    test_document_service()
    await test_agent_communication()
    await test_workflow()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")
    print("\n💡 提示:")
    print("- 如果要测试完整功能，请配置 OpenAI API Key")
    print("- 运行 'python start_demo.py' 启动完整演示")
    print("- 运行 'python backend/main.py' 仅启动后端服务")


if __name__ == "__main__":
    asyncio.run(main())