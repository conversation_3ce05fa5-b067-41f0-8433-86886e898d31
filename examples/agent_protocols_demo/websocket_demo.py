"""
WebSocket A2A 和 AG-UI 协议演示
展示基于 WebSocket 的实时智能体通信协议
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import threading
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn


class WebSocketAgentProtocol:
    """WebSocket 智能体协议处理器"""
    
    def __init__(self):
        self.agents: Dict[str, Dict] = {}
        self.connections: Dict[str, WebSocket] = {}
        self.message_history: List[Dict] = []
        self.system_status = "running"
    
    def register_agent(self, agent_id: str, websocket: WebSocket, agent_type: str = "generic"):
        """注册智能体"""
        self.agents[agent_id] = {
            "id": agent_id,
            "type": agent_type,
            "status": "connected",
            "last_seen": datetime.now(),
            "message_count": 0
        }
        self.connections[agent_id] = websocket
        print(f"智能体 {agent_id} ({agent_type}) 已注册")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            self.agents[agent_id]["status"] = "disconnected"
            del self.connections[agent_id]
            print(f"智能体 {agent_id} 已断开连接")
    
    async def send_to_agent(self, agent_id: str, message: Dict[str, Any]):
        """发送消息到特定智能体 (A2A 协议)"""
        if agent_id in self.connections:
            try:
                await self.connections[agent_id].send_text(json.dumps(message))
                self.agents[agent_id]["message_count"] += 1
                self.agents[agent_id]["last_seen"] = datetime.now()
                
                # 记录消息历史
                self.message_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "type": "agent_to_agent",
                    "from": message.get("sender", "system"),
                    "to": agent_id,
                    "content": message
                })
                
                return True
            except Exception as e:
                print(f"发送消息到 {agent_id} 失败: {e}")
                return False
        return False
    
    async def broadcast_message(self, message: Dict[str, Any], exclude: str = None):
        """广播消息到所有智能体"""
        results = []
        for agent_id in self.connections:
            if agent_id != exclude:
                success = await self.send_to_agent(agent_id, message)
                results.append((agent_id, success))
        return results
    
    async def handle_agent_message(self, agent_id: str, message: Dict[str, Any]):
        """处理智能体消息"""
        message_type = message.get("type", "unknown")
        
        if message_type == "heartbeat":
            # 心跳消息
            self.agents[agent_id]["last_seen"] = datetime.now()
            return {"type": "heartbeat_ack", "timestamp": datetime.now().isoformat()}
        
        elif message_type == "task_request":
            # 任务请求
            task_id = message.get("task_id")
            return await self.assign_task(agent_id, task_id)
        
        elif message_type == "task_result":
            # 任务结果
            return await self.handle_task_result(agent_id, message)
        
        elif message_type == "collaboration_request":
            # 协作请求
            target_agent = message.get("target_agent")
            return await self.handle_collaboration(agent_id, target_agent, message)
        
        else:
            return {"type": "error", "message": f"未知消息类型: {message_type}"}
    
    async def assign_task(self, agent_id: str, task_id: str):
        """分配任务"""
        task = {
            "type": "task_assignment",
            "task_id": task_id,
            "assigned_to": agent_id,
            "timestamp": datetime.now().isoformat(),
            "details": f"执行任务 {task_id}"
        }
        
        await self.send_to_agent(agent_id, task)
        return {"type": "task_assigned", "task_id": task_id}
    
    async def handle_task_result(self, agent_id: str, message: Dict[str, Any]):
        """处理任务结果"""
        task_id = message.get("task_id")
        result = message.get("result")
        
        # 通知其他智能体任务完成
        notification = {
            "type": "task_completed",
            "task_id": task_id,
            "completed_by": agent_id,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_message(notification, exclude=agent_id)
        return {"type": "task_result_received", "task_id": task_id}
    
    async def handle_collaboration(self, requester: str, target: str, message: Dict[str, Any]):
        """处理协作请求"""
        if target in self.connections:
            collaboration_msg = {
                "type": "collaboration_invitation",
                "from": requester,
                "message": message.get("message", ""),
                "timestamp": datetime.now().isoformat()
            }
            
            await self.send_to_agent(target, collaboration_msg)
            return {"type": "collaboration_request_sent", "target": target}
        else:
            return {"type": "error", "message": f"目标智能体 {target} 不在线"}
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            "status": self.system_status,
            "total_agents": len(self.agents),
            "connected_agents": len([a for a in self.agents.values() if a["status"] == "connected"]),
            "total_messages": len(self.message_history),
            "agents": self.agents
        }


# 创建协议处理器实例
protocol = WebSocketAgentProtocol()

# FastAPI 应用
app = FastAPI(title="WebSocket Agent Protocol Demo")


@app.get("/")
async def get_index():
    """返回主页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket Agent Protocol Demo</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .agent-panel { border: 1px solid #ccc; margin: 10px; padding: 15px; border-radius: 5px; }
            .connected { border-color: #4CAF50; background-color: #f9fff9; }
            .disconnected { border-color: #f44336; background-color: #fff9f9; }
            .message-log { height: 300px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px; }
            button { padding: 10px 15px; margin: 5px; cursor: pointer; }
            .send-btn { background-color: #4CAF50; color: white; border: none; }
            .broadcast-btn { background-color: #2196F3; color: white; border: none; }
            input, textarea { width: 100%; padding: 8px; margin: 5px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 WebSocket 智能体协议演示</h1>
            
            <div id="system-status">
                <h2>系统状态</h2>
                <div id="status-info">连接中...</div>
            </div>
            
            <div id="agents-panel">
                <h2>智能体面板</h2>
                <div id="agents-list"></div>
            </div>
            
            <div id="control-panel">
                <h2>控制面板</h2>
                <div>
                    <input type="text" id="agent-id" placeholder="智能体ID" />
                    <select id="agent-type">
                        <option value="worker">工作者</option>
                        <option value="coordinator">协调者</option>
                        <option value="monitor">监控者</option>
                    </select>
                    <button onclick="connectAgent()" class="send-btn">连接智能体</button>
                </div>
                
                <div>
                    <textarea id="message-content" placeholder="消息内容 (JSON格式)">{"type": "task_request", "task_id": "task_001"}</textarea>
                    <button onclick="sendMessage()" class="send-btn">发送消息</button>
                    <button onclick="broadcastMessage()" class="broadcast-btn">广播消息</button>
                </div>
            </div>
            
            <div id="message-log">
                <h2>消息日志</h2>
                <div class="message-log" id="log-content"></div>
            </div>
        </div>
        
        <script>
            let ws = null;
            let currentAgentId = null;
            
            function connectAgent() {
                const agentId = document.getElementById('agent-id').value;
                const agentType = document.getElementById('agent-type').value;
                
                if (!agentId) {
                    alert('请输入智能体ID');
                    return;
                }
                
                if (ws) {
                    ws.close();
                }
                
                ws = new WebSocket(`ws://localhost:8000/ws/${agentId}?type=${agentType}`);
                currentAgentId = agentId;
                
                ws.onopen = function(event) {
                    addLog(`智能体 ${agentId} 已连接`);
                    updateStatus();
                };
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    addLog(`收到消息: ${JSON.stringify(message, null, 2)}`);
                };
                
                ws.onclose = function(event) {
                    addLog(`智能体 ${agentId} 连接已关闭`);
                    updateStatus();
                };
                
                ws.onerror = function(error) {
                    addLog(`连接错误: ${error}`);
                };
            }
            
            function sendMessage() {
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    alert('请先连接智能体');
                    return;
                }
                
                const content = document.getElementById('message-content').value;
                try {
                    const message = JSON.parse(content);
                    ws.send(JSON.stringify(message));
                    addLog(`发送消息: ${JSON.stringify(message, null, 2)}`);
                } catch (e) {
                    alert('消息格式错误，请输入有效的JSON');
                }
            }
            
            function broadcastMessage() {
                const content = document.getElementById('message-content').value;
                try {
                    const message = JSON.parse(content);
                    message.type = 'broadcast';
                    
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify(message));
                        addLog(`广播消息: ${JSON.stringify(message, null, 2)}`);
                    }
                } catch (e) {
                    alert('消息格式错误，请输入有效的JSON');
                }
            }
            
            function addLog(message) {
                const logContent = document.getElementById('log-content');
                const timestamp = new Date().toLocaleTimeString();
                logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContent.scrollTop = logContent.scrollHeight;
            }
            
            function updateStatus() {
                fetch('/status')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('status-info').innerHTML = 
                            `<p>系统状态: ${data.status}</p>
                             <p>总智能体数: ${data.total_agents}</p>
                             <p>在线智能体: ${data.connected_agents}</p>
                             <p>消息总数: ${data.total_messages}</p>`;
                    });
            }
            
            // 定期更新状态
            setInterval(updateStatus, 5000);
            updateStatus();
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws/{agent_id}")
async def websocket_endpoint(websocket: WebSocket, agent_id: str, type: str = "generic"):
    """WebSocket 端点"""
    await websocket.accept()
    protocol.register_agent(agent_id, websocket, type)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理消息
            response = await protocol.handle_agent_message(agent_id, message)
            
            # 发送响应
            if response:
                await websocket.send_text(json.dumps(response))
                
    except WebSocketDisconnect:
        protocol.unregister_agent(agent_id)
    except Exception as e:
        print(f"WebSocket 错误: {e}")
        protocol.unregister_agent(agent_id)


@app.get("/status")
async def get_status():
    """获取系统状态"""
    return protocol.get_system_status()


@app.get("/agents")
async def get_agents():
    """获取智能体列表"""
    return {"agents": protocol.agents}


@app.get("/messages")
async def get_messages():
    """获取消息历史"""
    return {"messages": protocol.message_history[-50:]}  # 最近50条消息


def run_server():
    """运行服务器"""
    print("启动 WebSocket 智能体协议演示服务器...")
    print("访问 http://localhost:8000 查看演示界面")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")


if __name__ == "__main__":
    run_server()