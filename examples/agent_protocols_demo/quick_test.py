#!/usr/bin/env python3
"""
快速测试脚本 - 无需安装额外依赖
演示基本的 A2A 和 AG-UI 协议概念
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, List, Any


class SimpleAgent:
    """简单智能体实现"""
    
    def __init__(self, agent_id: str, agent_type: str = "generic"):
        self.id = agent_id
        self.type = agent_type
        self.status = "active"
        self.message_queue: List[Dict] = []
        self.sent_messages = 0
        self.received_messages = 0
        
    def send_message(self, target_agent: 'SimpleAgent', message_type: str, content: Any):
        """发送消息到另一个智能体 (A2A 协议)"""
        message = {
            "id": f"msg_{int(time.time() * 1000)}",
            "timestamp": datetime.now().isoformat(),
            "sender": self.id,
            "sender_type": self.type,
            "receiver": target_agent.id,
            "message_type": message_type,
            "content": content
        }
        
        target_agent.receive_message(message)
        self.sent_messages += 1
        
        print(f"[A2A] {self.id} -> {target_agent.id}: {message_type}")
        return message
    
    def receive_message(self, message: Dict):
        """接收消息"""
        self.message_queue.append(message)
        self.received_messages += 1
        
        # 自动处理某些消息类型
        self.process_message(message)
    
    def process_message(self, message: Dict):
        """处理收到的消息"""
        message_type = message.get("message_type")
        content = message.get("content")
        sender = message.get("sender")
        
        if message_type == "ping":
            # 回复 pong
            sender_agent = agent_registry.get(sender)
            if sender_agent:
                self.send_message(sender_agent, "pong", {"original_content": content})
        
        elif message_type == "task_assignment":
            print(f"  └─ {self.id} 收到任务: {content}")
            # 模拟任务处理
            time.sleep(0.1)
            result = f"任务 {content.get('task_id', 'unknown')} 已完成"
            
            sender_agent = agent_registry.get(sender)
            if sender_agent:
                self.send_message(sender_agent, "task_result", {"result": result})
        
        elif message_type == "collaboration_request":
            print(f"  └─ {self.id} 收到协作请求: {content}")
            # 接受协作
            sender_agent = agent_registry.get(sender)
            if sender_agent:
                self.send_message(sender_agent, "collaboration_accepted", 
                                {"message": "协作请求已接受"})
    
    def get_status(self) -> Dict:
        """获取智能体状态"""
        return {
            "id": self.id,
            "type": self.type,
            "status": self.status,
            "sent_messages": self.sent_messages,
            "received_messages": self.received_messages,
            "queue_size": len(self.message_queue)
        }


class SimpleUIProtocol:
    """简单的 AG-UI 协议实现"""
    
    def __init__(self):
        self.agents: Dict[str, SimpleAgent] = {}
        self.ui_messages: List[Dict] = []
    
    def register_agent(self, agent: SimpleAgent):
        """注册智能体到 UI 协议"""
        self.agents[agent.id] = agent
        self.log_ui_event("agent_registered", {"agent_id": agent.id, "type": agent.type})
    
    def send_command_to_agent(self, agent_id: str, command: str, parameters: Dict = None):
        """从 UI 发送命令到智能体 (AG-UI 协议)"""
        if agent_id not in self.agents:
            return {"error": f"智能体 {agent_id} 不存在"}
        
        agent = self.agents[agent_id]
        
        # 创建命令消息
        command_message = {
            "id": f"ui_cmd_{int(time.time() * 1000)}",
            "timestamp": datetime.now().isoformat(),
            "sender": "UI",
            "receiver": agent_id,
            "message_type": "ui_command",
            "content": {
                "command": command,
                "parameters": parameters or {}
            }
        }
        
        agent.receive_message(command_message)
        self.log_ui_event("command_sent", command_message)
        
        print(f"[AG-UI] UI -> {agent_id}: {command}")
        
        # 模拟智能体响应
        response = self.simulate_agent_response(agent, command, parameters)
        return response
    
    def simulate_agent_response(self, agent: SimpleAgent, command: str, parameters: Dict):
        """模拟智能体对 UI 命令的响应"""
        if parameters is None:
            parameters = {}
            
        responses = {
            "status": f"智能体 {agent.id} 状态正常",
            "execute_task": f"正在执行任务: {parameters.get('task_name', '未知任务')}",
            "get_data": {"data": [1, 2, 3, 4, 5], "timestamp": datetime.now().isoformat()},
            "analyze": {"result": "分析完成", "score": random.uniform(0.7, 0.95)},
            "collaborate": f"正在与其他智能体协作..."
        }
        
        response = responses.get(command, f"执行命令: {command}")
        
        # 记录响应
        self.log_ui_event("agent_response", {
            "agent_id": agent.id,
            "command": command,
            "response": response
        })
        
        return response
    
    def log_ui_event(self, event_type: str, data: Dict):
        """记录 UI 事件"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "data": data
        }
        self.ui_messages.append(event)
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.status == "active"]),
            "total_ui_events": len(self.ui_messages),
            "agents": {aid: agent.get_status() for aid, agent in self.agents.items()}
        }


# 全局智能体注册表
agent_registry: Dict[str, SimpleAgent] = {}


def demo_a2a_protocol():
    """演示 A2A (Agent-to-Agent) 协议"""
    print("\n=== A2A 协议演示 ===")
    
    # 创建智能体
    coordinator = SimpleAgent("coordinator_001", "coordinator")
    worker1 = SimpleAgent("worker_001", "worker")
    worker2 = SimpleAgent("worker_002", "worker")
    monitor = SimpleAgent("monitor_001", "monitor")
    
    # 注册智能体
    for agent in [coordinator, worker1, worker2, monitor]:
        agent_registry[agent.id] = agent
    
    print("创建了 4 个智能体: 1个协调者, 2个工作者, 1个监控者")
    
    # 演示不同类型的 A2A 通信
    print("\n1. 协调者分配任务给工作者")
    coordinator.send_message(worker1, "task_assignment", {"task_id": "task_001", "description": "数据处理"})
    coordinator.send_message(worker2, "task_assignment", {"task_id": "task_002", "description": "报告生成"})
    
    print("\n2. 工作者之间协作")
    worker1.send_message(worker2, "collaboration_request", {"project": "joint_analysis"})
    
    print("\n3. 监控者检查系统状态")
    monitor.send_message(coordinator, "ping", {"check_type": "health"})
    
    # 显示智能体状态
    print("\n=== 智能体状态 ===")
    for agent_id, agent in agent_registry.items():
        status = agent.get_status()
        print(f"{agent_id}: 发送 {status['sent_messages']} 条, 接收 {status['received_messages']} 条")


def demo_ag_ui_protocol():
    """演示 AG-UI (Agent-GUI) 协议"""
    print("\n=== AG-UI 协议演示 ===")
    
    # 创建 UI 协议处理器
    ui_protocol = SimpleUIProtocol()
    
    # 创建智能体并注册到 UI
    data_agent = SimpleAgent("data_agent", "data_processor")
    ml_agent = SimpleAgent("ml_agent", "machine_learning")
    viz_agent = SimpleAgent("viz_agent", "visualization")
    
    for agent in [data_agent, ml_agent, viz_agent]:
        ui_protocol.register_agent(agent)
        agent_registry[agent.id] = agent
    
    print("创建了 3 个智能体并注册到 UI 协议")
    
    # 演示 UI 到智能体的命令
    print("\n1. UI 发送命令到智能体")
    ui_protocol.send_command_to_agent("data_agent", "get_data", {"source": "database"})
    ui_protocol.send_command_to_agent("ml_agent", "analyze", {"model": "random_forest"})
    ui_protocol.send_command_to_agent("viz_agent", "execute_task", {"task_name": "create_chart"})
    
    print("\n2. 查询智能体状态")
    for agent_id in ["data_agent", "ml_agent", "viz_agent"]:
        response = ui_protocol.send_command_to_agent(agent_id, "status")
        print(f"  {agent_id}: {response}")
    
    # 显示系统状态
    print("\n=== 系统状态 ===")
    status = ui_protocol.get_system_status()
    print(f"总智能体数: {status['total_agents']}")
    print(f"活跃智能体: {status['active_agents']}")
    print(f"UI 事件总数: {status['total_ui_events']}")


def demo_mixed_protocol():
    """演示混合协议 (A2A + AG-UI)"""
    print("\n=== 混合协议演示 ===")
    
    # 创建一个完整的智能体系统
    ui_protocol = SimpleUIProtocol()
    
    # 创建不同类型的智能体
    agents = [
        SimpleAgent("ui_controller", "ui_interface"),
        SimpleAgent("task_manager", "coordinator"),
        SimpleAgent("data_processor", "worker"),
        SimpleAgent("result_analyzer", "worker"),
        SimpleAgent("system_monitor", "monitor")
    ]
    
    # 注册所有智能体
    for agent in agents:
        ui_protocol.register_agent(agent)
        agent_registry[agent.id] = agent
    
    print("创建了完整的智能体生态系统")
    
    # 模拟完整的工作流
    print("\n1. UI 启动工作流")
    ui_protocol.send_command_to_agent("task_manager", "execute_task", 
                                    {"workflow": "data_analysis_pipeline"})
    
    print("\n2. 任务管理器协调工作者")
    task_manager = agent_registry["task_manager"]
    data_processor = agent_registry["data_processor"]
    result_analyzer = agent_registry["result_analyzer"]
    
    task_manager.send_message(data_processor, "task_assignment", 
                            {"task_id": "process_data", "priority": "high"})
    task_manager.send_message(result_analyzer, "task_assignment",
                            {"task_id": "analyze_results", "depends_on": "process_data"})
    
    print("\n3. 工作者协作完成任务")
    data_processor.send_message(result_analyzer, "collaboration_request",
                              {"data_ready": True, "location": "/tmp/processed_data"})
    
    print("\n4. 监控者报告状态")
    monitor = agent_registry["system_monitor"]
    ui_protocol.send_command_to_agent("system_monitor", "status")
    
    # 最终状态报告
    print("\n=== 最终状态报告 ===")
    final_status = ui_protocol.get_system_status()
    print(json.dumps(final_status, indent=2, ensure_ascii=False))


def main():
    """主函数"""
    print("🤖 智能体协议快速演示")
    print("=" * 50)
    
    # 运行所有演示
    demo_a2a_protocol()
    time.sleep(1)
    
    demo_ag_ui_protocol()
    time.sleep(1)
    
    demo_mixed_protocol()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n要运行完整的演示，请安装依赖并运行:")
    print("pip install -r requirements.txt")
    print("python run_demos.py gradio  # 或其他演示名称")


if __name__ == "__main__":
    main()