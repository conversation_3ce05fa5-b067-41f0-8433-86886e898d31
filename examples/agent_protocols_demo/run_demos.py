#!/usr/bin/env python3
"""
智能体协议演示启动脚本
"""

import sys
import subprocess
import arg<PERSON>se


def run_demo(demo_name):
    """运行指定的演示"""
    demos = {
        "spade": {
            "file": "spade_demo.py",
            "description": "SPADE 多智能体通信演示"
        },
        "mesa": {
            "file": "mesa_demo.py", 
            "description": "Mesa 智能体建模演示"
        },
        "gradio": {
            "file": "gradio_demo.py",
            "description": "Gradio AG-UI 界面演示"
        },
        "streamlit": {
            "file": "streamlit_demo.py",
            "description": "Streamlit AG-UI 界面演示"
        },
        "websocket": {
            "file": "websocket_demo.py",
            "description": "WebSocket 实时通信演示"
        }
    }
    
    if demo_name not in demos:
        print(f"错误: 未知的演示名称 '{demo_name}'")
        print("可用的演示:")
        for name, info in demos.items():
            print(f"  {name}: {info['description']}")
        return False
    
    demo_info = demos[demo_name]
    print(f"启动 {demo_info['description']}...")
    
    try:
        if demo_name == "streamlit":
            # Streamlit 需要特殊的启动方式
            subprocess.run([sys.executable, "-m", "streamlit", "run", demo_info["file"]])
        else:
            subprocess.run([sys.executable, demo_info["file"]])
    except KeyboardInterrupt:
        print(f"\n{demo_info['description']} 已停止")
    except Exception as e:
        print(f"运行演示时出错: {e}")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能体协议演示启动器")
    parser.add_argument(
        "demo",
        choices=["spade", "mesa", "gradio", "streamlit", "websocket", "all"],
        help="要运行的演示名称"
    )
    
    args = parser.parse_args()
    
    if args.demo == "all":
        print("=== 智能体协议演示概览 ===")
        print("1. SPADE: 多智能体通信协议")
        print("2. Mesa: 智能体建模框架")
        print("3. Gradio: 机器学习界面协议")
        print("4. Streamlit: 数据科学界面协议")
        print("5. WebSocket: 实时通信协议")
        print("\n请选择具体的演示运行，例如: python run_demos.py gradio")
    else:
        run_demo(args.demo)


if __name__ == "__main__":
    main()