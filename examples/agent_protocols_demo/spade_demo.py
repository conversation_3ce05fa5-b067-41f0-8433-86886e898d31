"""
SPADE (Smart Python Agent Development Environment) 演示
展示多智能体之间的 A2A 通信协议
"""

import asyncio
import time
from spade.agent import Agent
from spade.behaviour import CyclicBehaviour, OneShotBehaviour
from spade.message import Message
from spade.template import Template


class SenderAgent(Agent):
    """发送者智能体"""
    
    class SendBehaviour(OneShotBehaviour):
        async def run(self):
            print(f"[{self.agent.jid}] 发送消息...")
            
            # 创建消息
            msg = Message(to="receiver@localhost")
            msg.set_metadata("performative", "inform")
            msg.set_metadata("ontology", "agent_communication")
            msg.body = "Hello from Sender Agent! 这是一条 A2A 协议消息。"
            
            await self.send(msg)
            print(f"[{self.agent.jid}] 消息已发送: {msg.body}")
            
            # 等待回复
            reply = await self.receive(timeout=10)
            if reply:
                print(f"[{self.agent.jid}] 收到回复: {reply.body}")
            else:
                print(f"[{self.agent.jid}] 未收到回复")

    async def setup(self):
        print(f"[{self.jid}] 发送者智能体启动")
        self.add_behaviour(self.SendBehaviour())


class ReceiverAgent(Agent):
    """接收者智能体"""
    
    class ReceiveBehaviour(CyclicBehaviour):
        async def run(self):
            # 等待消息
            msg = await self.receive(timeout=10)
            if msg:
                print(f"[{self.agent.jid}] 收到消息: {msg.body}")
                print(f"[{self.agent.jid}] 发送者: {msg.sender}")
                
                # 发送回复
                reply = msg.make_reply()
                reply.body = "Message received! 消息已收到，这是回复。"
                await self.send(reply)
                print(f"[{self.agent.jid}] 回复已发送")
            else:
                print(f"[{self.agent.jid}] 等待消息中...")

    async def setup(self):
        print(f"[{self.jid}] 接收者智能体启动")
        template = Template()
        template.set_metadata("performative", "inform")
        self.add_behaviour(self.ReceiveBehaviour(), template)


class CoordinatorAgent(Agent):
    """协调者智能体 - 管理其他智能体"""
    
    class CoordinateBehaviour(CyclicBehaviour):
        def __init__(self):
            super().__init__()
            self.message_count = 0
            
        async def run(self):
            await asyncio.sleep(2)
            self.message_count += 1
            
            # 广播协调消息
            msg = Message(to="sender@localhost")
            msg.set_metadata("performative", "request")
            msg.body = f"Coordination message #{self.message_count} - 请开始通信"
            
            await self.send(msg)
            print(f"[{self.agent.jid}] 发送协调消息 #{self.message_count}")
            
            if self.message_count >= 3:
                print(f"[{self.agent.jid}] 协调完成，停止")
                self.kill()

    async def setup(self):
        print(f"[{self.jid}] 协调者智能体启动")
        self.add_behaviour(self.CoordinateBehaviour())


async def main():
    """主函数 - 演示 SPADE A2A 协议"""
    print("=== SPADE A2A 协议演示 ===")
    print("启动多智能体系统...")
    
    # 创建智能体实例
    receiver = ReceiverAgent("receiver@localhost", "password")
    sender = SenderAgent("sender@localhost", "password")
    coordinator = CoordinatorAgent("coordinator@localhost", "password")
    
    # 启动智能体
    await receiver.start(auto_register=True)
    await sender.start(auto_register=True)
    await coordinator.start(auto_register=True)
    
    print("所有智能体已启动，开始通信...")
    
    # 运行一段时间
    await asyncio.sleep(10)
    
    # 停止智能体
    await sender.stop()
    await receiver.stop()
    await coordinator.stop()
    
    print("演示结束")


if __name__ == "__main__":
    # 注意：SPADE 需要 XMPP 服务器，这里提供简化版本
    print("SPADE 演示 (简化版本)")
    print("注意：完整版本需要 XMPP 服务器 (如 ejabberd)")
    print("这里展示 SPADE 的基本结构和 A2A 通信模式")
    
    # 模拟 SPADE 行为
    class MockAgent:
        def __init__(self, name):
            self.name = name
            
        async def send_message(self, to_agent, message):
            print(f"[{self.name}] -> [{to_agent}]: {message}")
            
        async def receive_message(self, from_agent, message):
            print(f"[{self.name}] <- [{from_agent}]: {message}")
            return f"ACK: {message}"
    
    async def mock_demo():
        # 创建模拟智能体
        sender = MockAgent("SenderAgent")
        receiver = MockAgent("ReceiverAgent")
        coordinator = MockAgent("CoordinatorAgent")
        
        print("\n=== 模拟 A2A 通信 ===")
        
        # 协调者发起通信
        await coordinator.send_message("SenderAgent", "开始通信序列")
        
        # 发送者向接收者发送消息
        await sender.send_message("ReceiverAgent", "Hello! 这是 A2A 协议消息")
        
        # 接收者回复
        reply = await receiver.receive_message("SenderAgent", "Hello! 这是 A2A 协议消息")
        await receiver.send_message("SenderAgent", reply)
        
        # 协调者确认
        await coordinator.send_message("All", "通信完成")
    
    asyncio.run(mock_demo())