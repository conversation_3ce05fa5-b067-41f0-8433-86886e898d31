"""
Streamlit AG-UI 协议演示
展示基于 Streamlit 的智能体界面协议
"""

import streamlit as st
import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px


class StreamlitAgentProtocol:
    """Streamlit 智能体协议处理器"""
    
    def __init__(self):
        if 'agents' not in st.session_state:
            st.session_state.agents = {
                "data_agent": {
                    "name": "数据智能体",
                    "status": "active",
                    "tasks_completed": 0,
                    "last_activity": datetime.now(),
                    "capabilities": ["数据分析", "图表生成", "统计计算"]
                },
                "ml_agent": {
                    "name": "机器学习智能体", 
                    "status": "active",
                    "tasks_completed": 0,
                    "last_activity": datetime.now(),
                    "capabilities": ["模型训练", "预测", "特征工程"]
                },
                "viz_agent": {
                    "name": "可视化智能体",
                    "status": "active", 
                    "tasks_completed": 0,
                    "last_activity": datetime.now(),
                    "capabilities": ["图表创建", "仪表板", "交互式可视化"]
                }
            }
        
        if 'message_log' not in st.session_state:
            st.session_state.message_log = []
        
        if 'system_metrics' not in st.session_state:
            st.session_state.system_metrics = {
                'timestamps': [],
                'cpu_usage': [],
                'memory_usage': [],
                'active_agents': []
            }
    
    def send_command(self, agent_id, command, parameters=None):
        """发送命令到智能体"""
        if agent_id not in st.session_state.agents:
            return {"error": f"智能体 {agent_id} 不存在"}
        
        agent = st.session_state.agents[agent_id]
        timestamp = datetime.now()
        
        # 模拟命令处理
        result = self._process_command(agent_id, command, parameters)
        
        # 更新智能体状态
        agent["tasks_completed"] += 1
        agent["last_activity"] = timestamp
        
        # 记录消息
        st.session_state.message_log.append({
            "timestamp": timestamp,
            "agent_id": agent_id,
            "agent_name": agent["name"],
            "command": command,
            "parameters": parameters,
            "result": result
        })
        
        return result
    
    def _process_command(self, agent_id, command, parameters):
        """处理智能体命令"""
        agent = st.session_state.agents[agent_id]
        
        if agent_id == "data_agent":
            if command == "analyze_data":
                # 生成模拟数据分析结果
                data = np.random.randn(100, 3)
                df = pd.DataFrame(data, columns=['A', 'B', 'C'])
                return {
                    "type": "data_analysis",
                    "summary": {
                        "rows": len(df),
                        "columns": len(df.columns),
                        "mean_A": df['A'].mean(),
                        "std_A": df['A'].std()
                    },
                    "data": df
                }
            elif command == "generate_report":
                return {
                    "type": "report",
                    "content": "数据分析报告已生成",
                    "metrics": {
                        "处理时间": "2.3秒",
                        "数据点": 1000,
                        "准确率": "95.2%"
                    }
                }
        
        elif agent_id == "ml_agent":
            if command == "train_model":
                return {
                    "type": "model_training",
                    "model_type": parameters.get("model_type", "linear_regression"),
                    "accuracy": np.random.uniform(0.8, 0.95),
                    "training_time": np.random.uniform(1.0, 5.0),
                    "status": "completed"
                }
            elif command == "predict":
                predictions = np.random.randn(10)
                return {
                    "type": "prediction",
                    "predictions": predictions.tolist(),
                    "confidence": np.random.uniform(0.7, 0.9)
                }
        
        elif agent_id == "viz_agent":
            if command == "create_chart":
                chart_type = parameters.get("chart_type", "line")
                return {
                    "type": "visualization",
                    "chart_type": chart_type,
                    "status": "created",
                    "elements": ["x轴", "y轴", "图例", "标题"]
                }
        
        return {"type": "generic", "message": f"命令 {command} 已执行"}
    
    def get_system_status(self):
        """获取系统状态"""
        active_agents = sum(1 for agent in st.session_state.agents.values() 
                          if agent["status"] == "active")
        total_tasks = sum(agent["tasks_completed"] 
                         for agent in st.session_state.agents.values())
        
        return {
            "active_agents": active_agents,
            "total_agents": len(st.session_state.agents),
            "total_tasks": total_tasks,
            "uptime": "运行中",
            "last_update": datetime.now()
        }
    
    def update_metrics(self):
        """更新系统指标"""
        current_time = datetime.now()
        st.session_state.system_metrics['timestamps'].append(current_time)
        st.session_state.system_metrics['cpu_usage'].append(np.random.uniform(20, 80))
        st.session_state.system_metrics['memory_usage'].append(np.random.uniform(30, 70))
        st.session_state.system_metrics['active_agents'].append(
            sum(1 for agent in st.session_state.agents.values() if agent["status"] == "active")
        )
        
        # 保持最近50个数据点
        for key in ['timestamps', 'cpu_usage', 'memory_usage', 'active_agents']:
            if len(st.session_state.system_metrics[key]) > 50:
                st.session_state.system_metrics[key] = st.session_state.system_metrics[key][-50:]


def main():
    """主界面"""
    st.set_page_config(
        page_title="Agent-UI Protocol Demo",
        page_icon="🤖",
        layout="wide"
    )
    
    st.title("🤖 Streamlit AG-UI 协议演示")
    st.markdown("展示智能体与 Streamlit 界面的交互协议")
    
    # 创建协议处理器
    protocol = StreamlitAgentProtocol()
    
    # 侧边栏 - 系统控制
    with st.sidebar:
        st.header("系统控制")
        
        if st.button("刷新系统状态"):
            protocol.update_metrics()
            st.rerun()
        
        st.header("快速操作")
        selected_agent = st.selectbox(
            "选择智能体",
            options=list(st.session_state.agents.keys()),
            format_func=lambda x: st.session_state.agents[x]["name"]
        )
        
        if st.button("执行数据分析"):
            result = protocol.send_command("data_agent", "analyze_data")
            st.success("数据分析完成")
        
        if st.button("训练模型"):
            result = protocol.send_command("ml_agent", "train_model", 
                                         {"model_type": "random_forest"})
            st.success("模型训练完成")
        
        if st.button("创建图表"):
            result = protocol.send_command("viz_agent", "create_chart",
                                         {"chart_type": "scatter"})
            st.success("图表创建完成")
    
    # 主界面标签页
    tab1, tab2, tab3, tab4 = st.tabs(["智能体状态", "命令中心", "系统监控", "消息日志"])
    
    with tab1:
        st.header("智能体状态")
        
        # 显示智能体卡片
        cols = st.columns(len(st.session_state.agents))
        
        for i, (agent_id, agent_info) in enumerate(st.session_state.agents.items()):
            with cols[i]:
                status_color = "🟢" if agent_info["status"] == "active" else "🔴"
                st.markdown(f"### {status_color} {agent_info['name']}")
                st.metric("完成任务", agent_info["tasks_completed"])
                st.write(f"**状态**: {agent_info['status']}")
                st.write(f"**最后活动**: {agent_info['last_activity'].strftime('%H:%M:%S')}")
                
                with st.expander("能力列表"):
                    for capability in agent_info["capabilities"]:
                        st.write(f"• {capability}")
    
    with tab2:
        st.header("命令中心")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.subheader("发送命令")
            
            agent_choice = st.selectbox(
                "目标智能体",
                options=list(st.session_state.agents.keys()),
                format_func=lambda x: st.session_state.agents[x]["name"],
                key="cmd_agent"
            )
            
            command_type = st.selectbox(
                "命令类型",
                ["analyze_data", "train_model", "create_chart", "generate_report", "predict"]
            )
            
            # 参数输入
            st.subheader("命令参数")
            param_json = st.text_area(
                "JSON 参数",
                value='{"model_type": "random_forest", "chart_type": "line"}',
                height=100
            )
            
            if st.button("执行命令", type="primary"):
                try:
                    parameters = json.loads(param_json) if param_json.strip() else {}
                    result = protocol.send_command(agent_choice, command_type, parameters)
                    st.success("命令执行成功")
                    st.json(result)
                except json.JSONDecodeError:
                    st.error("参数格式错误，请输入有效的 JSON")
                except Exception as e:
                    st.error(f"执行错误: {str(e)}")
        
        with col2:
            st.subheader("批量操作")
            
            if st.button("执行完整工作流"):
                with st.spinner("执行工作流中..."):
                    # 步骤1: 数据分析
                    result1 = protocol.send_command("data_agent", "analyze_data")
                    st.write("✅ 数据分析完成")
                    
                    # 步骤2: 模型训练
                    result2 = protocol.send_command("ml_agent", "train_model")
                    st.write("✅ 模型训练完成")
                    
                    # 步骤3: 创建可视化
                    result3 = protocol.send_command("viz_agent", "create_chart")
                    st.write("✅ 可视化创建完成")
                    
                st.success("工作流执行完成！")
    
    with tab3:
        st.header("系统监控")
        
        # 更新指标
        protocol.update_metrics()
        
        # 系统状态概览
        status = protocol.get_system_status()
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("活跃智能体", status["active_agents"], delta=0)
        with col2:
            st.metric("总任务数", status["total_tasks"], delta=1)
        with col3:
            st.metric("系统状态", "正常", delta=None)
        with col4:
            st.metric("运行时间", "运行中", delta=None)
        
        # 性能图表
        if st.session_state.system_metrics['timestamps']:
            col1, col2 = st.columns(2)
            
            with col1:
                # CPU 使用率
                fig_cpu = go.Figure()
                fig_cpu.add_trace(go.Scatter(
                    x=st.session_state.system_metrics['timestamps'],
                    y=st.session_state.system_metrics['cpu_usage'],
                    mode='lines+markers',
                    name='CPU 使用率',
                    line=dict(color='red')
                ))
                fig_cpu.update_layout(title="CPU 使用率", yaxis_title="百分比")
                st.plotly_chart(fig_cpu, use_container_width=True)
            
            with col2:
                # 内存使用率
                fig_mem = go.Figure()
                fig_mem.add_trace(go.Scatter(
                    x=st.session_state.system_metrics['timestamps'],
                    y=st.session_state.system_metrics['memory_usage'],
                    mode='lines+markers',
                    name='内存使用率',
                    line=dict(color='blue')
                ))
                fig_mem.update_layout(title="内存使用率", yaxis_title="百分比")
                st.plotly_chart(fig_mem, use_container_width=True)
    
    with tab4:
        st.header("消息日志")
        
        if st.session_state.message_log:
            # 显示最近的消息
            recent_messages = st.session_state.message_log[-20:]  # 最近20条
            
            for msg in reversed(recent_messages):
                with st.expander(f"[{msg['timestamp'].strftime('%H:%M:%S')}] {msg['agent_name']} - {msg['command']}"):
                    col1, col2 = st.columns([1, 1])
                    
                    with col1:
                        st.write("**命令参数:**")
                        st.json(msg['parameters'] or {})
                    
                    with col2:
                        st.write("**执行结果:**")
                        st.json(msg['result'])
        else:
            st.info("暂无消息日志")
        
        if st.button("清空日志"):
            st.session_state.message_log = []
            st.rerun()
    
    # 自动刷新
    time.sleep(1)


if __name__ == "__main__":
    main()