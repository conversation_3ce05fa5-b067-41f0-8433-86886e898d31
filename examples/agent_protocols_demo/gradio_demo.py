"""
Gradio AG-UI 协议演示
展示智能体与用户界面的交互协议
"""

import gradio as gr
import json
import time
import random
from datetime import datetime


class AgentUIProtocol:
    """智能体-UI 协议处理器"""
    
    def __init__(self):
        self.agents = {
            "assistant": {"status": "active", "tasks": 0, "last_response": ""},
            "analyzer": {"status": "active", "tasks": 0, "last_response": ""},
            "coordinator": {"status": "active", "tasks": 0, "last_response": ""}
        }
        self.message_history = []
        self.system_status = "运行中"
    
    def send_to_agent(self, agent_name, message, message_type="user_input"):
        """向智能体发送消息 (AG-UI 协议)"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 模拟智能体处理
        if agent_name in self.agents:
            self.agents[agent_name]["tasks"] += 1
            
            # 根据智能体类型生成不同响应
            if agent_name == "assistant":
                response = f"助手智能体收到: {message}。正在处理用户请求..."
            elif agent_name == "analyzer":
                response = f"分析智能体正在分析: {message}。检测到 {random.randint(1, 5)} 个关键点。"
            elif agent_name == "coordinator":
                response = f"协调智能体收到指令: {message}。正在协调其他智能体..."
            else:
                response = f"未知智能体 {agent_name}"
            
            self.agents[agent_name]["last_response"] = response
            
            # 记录消息历史
            self.message_history.append({
                "timestamp": timestamp,
                "type": "user_to_agent",
                "agent": agent_name,
                "message": message,
                "response": response
            })
            
            return response
        else:
            return f"错误: 智能体 {agent_name} 不存在"
    
    def get_agent_status(self):
        """获取所有智能体状态"""
        status_text = "=== 智能体状态 ===\n"
        for name, info in self.agents.items():
            status_text += f"{name}: {info['status']} (任务数: {info['tasks']})\n"
        return status_text
    
    def get_message_history(self):
        """获取消息历史"""
        if not self.message_history:
            return "暂无消息历史"
        
        history_text = "=== 消息历史 ===\n"
        for msg in self.message_history[-10:]:  # 显示最近10条
            history_text += f"[{msg['timestamp']}] {msg['type']}: {msg['message']}\n"
            history_text += f"  └─ 响应: {msg['response']}\n\n"
        
        return history_text
    
    def broadcast_message(self, message):
        """广播消息到所有智能体"""
        responses = []
        for agent_name in self.agents.keys():
            response = self.send_to_agent(agent_name, message, "broadcast")
            responses.append(f"{agent_name}: {response}")
        
        return "\n".join(responses)
    
    def simulate_agent_communication(self):
        """模拟智能体间通信"""
        communications = [
            "assistant -> analyzer: 请分析用户输入的情感倾向",
            "analyzer -> coordinator: 分析完成，情感为积极",
            "coordinator -> assistant: 可以提供积极的回复",
            "assistant -> user: 基于分析结果生成回复"
        ]
        
        result = "=== 智能体间通信 (A2A) ===\n"
        for comm in communications:
            result += f"{comm}\n"
            time.sleep(0.1)  # 模拟处理时间
        
        return result


# 创建协议处理器实例
protocol = AgentUIProtocol()


def create_gradio_interface():
    """创建 Gradio 界面"""
    
    with gr.Blocks(title="Agent-UI Protocol Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 智能体-UI 协议演示 (AG-UI)")
        gr.Markdown("这个演示展示了智能体与用户界面之间的通信协议")
        
        with gr.Tab("智能体通信"):
            with gr.Row():
                with gr.Column():
                    agent_dropdown = gr.Dropdown(
                        choices=["assistant", "analyzer", "coordinator"],
                        label="选择智能体",
                        value="assistant"
                    )
                    user_input = gr.Textbox(
                        label="发送消息",
                        placeholder="输入要发送给智能体的消息..."
                    )
                    send_btn = gr.Button("发送消息", variant="primary")
                    broadcast_btn = gr.Button("广播到所有智能体")
                
                with gr.Column():
                    agent_response = gr.Textbox(
                        label="智能体响应",
                        lines=5,
                        interactive=False
                    )
        
        with gr.Tab("系统状态"):
            with gr.Row():
                status_btn = gr.Button("刷新状态")
                simulate_btn = gr.Button("模拟 A2A 通信")
            
            system_status = gr.Textbox(
                label="系统状态",
                lines=8,
                interactive=False
            )
        
        with gr.Tab("消息历史"):
            history_btn = gr.Button("刷新历史")
            message_history = gr.Textbox(
                label="消息历史",
                lines=15,
                interactive=False
            )
        
        with gr.Tab("协议信息"):
            gr.Markdown("""
            ## AG-UI 协议特性
            
            ### 消息格式
            ```json
            {
                "timestamp": "HH:MM:SS",
                "type": "user_to_agent | agent_to_user | broadcast",
                "agent": "agent_name",
                "message": "message_content",
                "response": "agent_response"
            }
            ```
            
            ### 支持的操作
            - **单向通信**: 用户 → 智能体
            - **双向通信**: 用户 ↔ 智能体
            - **广播通信**: 用户 → 所有智能体
            - **状态查询**: 获取智能体状态
            - **历史记录**: 查看通信历史
            
            ### 智能体类型
            - **Assistant**: 通用助手智能体
            - **Analyzer**: 分析智能体
            - **Coordinator**: 协调智能体
            """)
        
        # 事件绑定
        send_btn.click(
            fn=lambda agent, msg: protocol.send_to_agent(agent, msg),
            inputs=[agent_dropdown, user_input],
            outputs=[agent_response]
        )
        
        broadcast_btn.click(
            fn=lambda msg: protocol.broadcast_message(msg),
            inputs=[user_input],
            outputs=[agent_response]
        )
        
        status_btn.click(
            fn=lambda: protocol.get_agent_status(),
            outputs=[system_status]
        )
        
        simulate_btn.click(
            fn=lambda: protocol.simulate_agent_communication(),
            outputs=[system_status]
        )
        
        history_btn.click(
            fn=lambda: protocol.get_message_history(),
            outputs=[message_history]
        )
    
    return demo


def main():
    """主函数"""
    print("启动 Gradio AG-UI 协议演示...")
    
    # 创建界面
    demo = create_gradio_interface()
    
    # 启动服务器
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()