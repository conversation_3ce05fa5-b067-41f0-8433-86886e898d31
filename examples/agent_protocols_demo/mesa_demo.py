"""
Mesa 框架演示 - 基于智能体的建模
展示智能体之间的交互和通信协议
"""

import random
import matplotlib.pyplot as plt
from mesa import Agent, Model
from mesa.time import RandomActivation
from mesa.space import MultiGrid
from mesa.datacollection import DataCollector


class CommunicatingAgent(Agent):
    """可通信的智能体"""
    
    def __init__(self, unique_id, model, agent_type="worker"):
        super().__init__(unique_id, model)
        self.agent_type = agent_type
        self.energy = 100
        self.messages_sent = 0
        self.messages_received = 0
        self.collaboration_score = 0
        
    def step(self):
        """智能体的行为步骤"""
        if self.agent_type == "coordinator":
            self.coordinate()
        elif self.agent_type == "worker":
            self.work()
        elif self.agent_type == "monitor":
            self.monitor()
    
    def coordinate(self):
        """协调者行为"""
        # 向附近的工作者发送任务
        neighbors = self.model.grid.get_neighbors(
            self.pos, moore=True, include_center=False, radius=2
        )
        
        workers = [agent for agent in neighbors if agent.agent_type == "worker"]
        
        if workers and self.energy > 20:
            target = random.choice(workers)
            self.send_message(target, "task_assignment", {"task_id": random.randint(1, 100)})
            self.energy -= 5
    
    def work(self):
        """工作者行为"""
        # 处理任务并与其他工作者协作
        if self.energy > 10:
            neighbors = self.model.grid.get_neighbors(
                self.pos, moore=True, include_center=False, radius=1
            )
            
            other_workers = [agent for agent in neighbors if agent.agent_type == "worker"]
            
            if other_workers and random.random() < 0.3:
                partner = random.choice(other_workers)
                self.send_message(partner, "collaboration", {"project": "shared_task"})
                self.collaboration_score += 1
                self.energy -= 3
    
    def monitor(self):
        """监控者行为"""
        # 监控系统状态
        all_agents = self.model.schedule.agents
        total_energy = sum(agent.energy for agent in all_agents)
        
        if total_energy < 500:  # 系统能量过低
            coordinators = [agent for agent in all_agents if agent.agent_type == "coordinator"]
            for coord in coordinators:
                self.send_message(coord, "system_alert", {"alert": "low_energy"})
    
    def send_message(self, target_agent, message_type, content):
        """发送消息 (A2A 协议)"""
        message = {
            "sender_id": self.unique_id,
            "sender_type": self.agent_type,
            "message_type": message_type,
            "content": content,
            "timestamp": self.model.schedule.time
        }
        
        target_agent.receive_message(message)
        self.messages_sent += 1
        
        print(f"[Step {self.model.schedule.time}] {self.agent_type}#{self.unique_id} -> {target_agent.agent_type}#{target_agent.unique_id}: {message_type}")
    
    def receive_message(self, message):
        """接收消息 (A2A 协议)"""
        self.messages_received += 1
        
        # 根据消息类型处理
        if message["message_type"] == "task_assignment":
            self.energy += 10  # 获得任务奖励
            print(f"  └─ 任务分配: {message['content']}")
            
        elif message["message_type"] == "collaboration":
            self.collaboration_score += 1
            print(f"  └─ 协作请求: {message['content']}")
            
        elif message["message_type"] == "system_alert":
            if self.agent_type == "coordinator":
                self.energy += 20  # 紧急补充能量
            print(f"  └─ 系统警报: {message['content']}")


class MultiAgentModel(Model):
    """多智能体模型"""
    
    def __init__(self, n_coordinators=2, n_workers=8, n_monitors=1, width=10, height=10):
        super().__init__()
        
        self.num_agents = n_coordinators + n_workers + n_monitors
        self.grid = MultiGrid(width, height, True)
        self.schedule = RandomActivation(self)
        
        # 创建智能体
        agent_id = 0
        
        # 创建协调者
        for i in range(n_coordinators):
            agent = CommunicatingAgent(agent_id, self, "coordinator")
            self.schedule.add(agent)
            
            # 随机放置
            x = self.random.randrange(self.grid.width)
            y = self.random.randrange(self.grid.height)
            self.grid.place_agent(agent, (x, y))
            agent_id += 1
        
        # 创建工作者
        for i in range(n_workers):
            agent = CommunicatingAgent(agent_id, self, "worker")
            self.schedule.add(agent)
            
            x = self.random.randrange(self.grid.width)
            y = self.random.randrange(self.grid.height)
            self.grid.place_agent(agent, (x, y))
            agent_id += 1
        
        # 创建监控者
        for i in range(n_monitors):
            agent = CommunicatingAgent(agent_id, self, "monitor")
            self.schedule.add(agent)
            
            x = self.random.randrange(self.grid.width)
            y = self.random.randrange(self.grid.height)
            self.grid.place_agent(agent, (x, y))
            agent_id += 1
        
        # 数据收集器
        self.datacollector = DataCollector(
            model_reporters={
                "Total Energy": lambda m: sum(agent.energy for agent in m.schedule.agents),
                "Messages Sent": lambda m: sum(agent.messages_sent for agent in m.schedule.agents),
                "Collaboration Score": lambda m: sum(agent.collaboration_score for agent in m.schedule.agents)
            }
        )
    
    def step(self):
        """模型步骤"""
        self.datacollector.collect(self)
        self.schedule.step()


def run_mesa_demo():
    """运行 Mesa 演示"""
    print("=== Mesa A2A 协议演示 ===")
    
    # 创建模型
    model = MultiAgentModel(n_coordinators=2, n_workers=6, n_monitors=1)
    
    # 运行模拟
    for i in range(10):
        print(f"\n--- 步骤 {i+1} ---")
        model.step()
        
        # 显示统计信息
        total_energy = sum(agent.energy for agent in model.schedule.agents)
        total_messages = sum(agent.messages_sent for agent in model.schedule.agents)
        total_collaboration = sum(agent.collaboration_score for agent in model.schedule.agents)
        
        print(f"系统总能量: {total_energy}")
        print(f"总消息数: {total_messages}")
        print(f"协作分数: {total_collaboration}")
    
    # 收集数据并绘图
    model_data = model.datacollector.get_model_vars_dataframe()
    
    # 创建图表
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    model_data["Total Energy"].plot(ax=axes[0], title="系统总能量")
    model_data["Messages Sent"].plot(ax=axes[1], title="消息发送总数")
    model_data["Collaboration Score"].plot(ax=axes[2], title="协作分数")
    
    plt.tight_layout()
    plt.savefig("examples/agent_protocols_demo/mesa_results.png")
    print("\n结果图表已保存到 mesa_results.png")


if __name__ == "__main__":
    run_mesa_demo()