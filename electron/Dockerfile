# 使用 Node.js 18 作为基础镜像
FROM node:18-alpine AS builder

# 安装系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    linux-headers \
    udev

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 复制 electron 目录
COPY ../electron ./electron

# 复制前端构建文件
COPY --from=frontend-build /app/dist ./frontend/dist

# 设置环境变量
ENV NODE_ENV=production

# 构建 Electron 应用
RUN npm run electron:dist

# 使用运行时镜像
FROM node:18-alpine

# 安装运行时依赖
RUN apk add --no-cache \
    udev \
    dbus \
    xvfb \
    gtk+3.0 \
    gdk-pixbuf \
    libxss1 \
    libxtst6 \
    libnss3 \
    libasound2 \
    libxrandr2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf-2.0-0 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxi6 \
    libxtst6 \
    libxrandr2 \
    libasound2 \
    libatspi2.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf-2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    ca-certificates \
    fonts-noto-cjk \
    fonts-noto-color-emoji

# 设置工作目录
WORKDIR /app

# 从构建阶段复制 Electron 应用
COPY --from=builder /app/dist/linux-unpacked /app/electron-app

# 设置环境变量以运行 Electron 应用
ENV DISPLAY=:99

# 创建启动脚本
RUN echo '#!/bin/sh' > /app/start-electron.sh && \
    echo 'Xvfb :99 -screen 0 1024x768x24 -ac &' >> /app/start-electron.sh && \
    echo 'export DISPLAY=:99' >> /app/start-electron.sh && \
    echo '/app/electron-app/electron &' >> /app/start-electron.sh && \
    chmod +x /app/start-electron.sh

# 暴露端口（如果需要远程访问）
EXPOSE 99

# 启动 Electron 应用
CMD ["/app/start-electron.sh"]