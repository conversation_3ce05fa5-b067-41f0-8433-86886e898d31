# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
backend/logs/

# Uploads
backend/uploaded_files/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Build artifacts
dist/
build/

# Test coverage
htmlcov/
.coverage
.pytest_cache/

# Temporary files
*.tmp
*.temp
.cache/

# Documentation
docs/
README.md
*.md

# Examples
examples/

# Tests
tests/
backend/tests/
frontend/test/
frontend/src/test/

# Electron
electron/
dist-electron/

# MCP Server
mcp_server/