{"name": "accounting-desktop-app", "version": "1.0.0", "description": "跨平台会计桌面应用", "main": "electron/main.js", "homepage": "./", "scripts": {"electron": "electron .", "electron:dev": "concurrently \"yarn frontend:dev\" \"wait-on http://localhost:5173 && NODE_ENV=development electron .\"", "electron:pack": "electron-builder", "electron:dist": "yarn frontend:build && electron-builder", "frontend:dev": "cd frontend && yarn dev", "frontend:build": "cd frontend && yarn build", "frontend:install": "cd frontend && yarn install", "install:all": "yarn install && yarn frontend:install", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.accounting.desktop", "productName": "会计助手", "directories": {"output": "dist"}, "files": ["electron/**/*", "frontend/dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.finance", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}}, "devDependencies": {"concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "wait-on": "^7.2.0"}, "dependencies": {"electron-updater": "^6.1.7"}}