#!/bin/bash

# 设置颜色变量
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 Docker 是否已安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误：Docker 未安装，请先安装 Docker${NC}"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}错误：Docker Compose 未安装，请先安装 Docker Compose${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}会计应用 Docker 启动脚本${NC}"
    echo ""
    echo "用法: $0 {command}"
    echo ""
    echo "命令:"
    echo "  up            启动 Web 服务（后台运行）"
    echo "  up-dev        启动开发环境（前台运行，显示日志）"
    echo "  electron      启动 Electron 桌面应用"
    echo "  all           启动所有服务包括 Electron 应用"
    echo "  down          停止所有服务"
    echo "  restart       重启所有服务"
    echo "  logs          查看服务日志"
    echo "  status        查看服务状态"
    echo "  clean         清理所有容器、网络和卷（谨慎使用）"
    echo "  rebuild       重新构建并启动所有服务"
    echo "  help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 up         # 启动 Web 服务"
    echo "  $0 electron  # 启动 Electron 桌面应用"
    echo "  $0 all       # 启动所有服务包括 Electron 应用"
    echo "  $0 logs      # 查看服务日志"
    echo "  $0 down      # 停止所有服务"
}

# 启动所有服务（后台运行）
start_services() {
    echo -e "${GREEN}正在启动会计应用 Docker 服务...${NC}"
    docker-compose up -d
    echo -e "${GREEN}服务已启动！${NC}"
    echo -e "${BLUE}前端访问地址: http://localhost:3000${NC}"
    echo -e "${BLUE}后端API地址: http://localhost:8000${NC}"
}

# 启动 Electron 应用
start_electron() {
    echo -e "${GREEN}正在启动会计应用 Electron 桌面应用...${NC}"
    
    # 检查是否在 Linux 系统上
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo -e "${YELLOW}提示：在 Linux 上运行 Electron 应用需要允许容器访问 X11 服务器${NC}"
        echo -e "${YELLOW}请运行以下命令：${NC}"
        echo -e "${BLUE}xhost +local:docker${NC}"
        read -p "是否已运行上述命令？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${RED}请先运行上述命令，然后重新尝试启动 Electron 应用${NC}"
            exit 1
        fi
    fi
    
    # 启动 Electron 服务
    docker-compose up -d electron
    echo -e "${GREEN}Electron 应用已启动！${NC}"
    echo -e "${YELLOW}注意：Electron 应用将在容器内运行，GUI 界面将显示在您的桌面上${NC}"
}

# 启动所有服务包括 Electron
start_all_with_electron() {
    echo -e "${GREEN}正在启动会计应用所有服务（包括 Electron）...${NC}"
    
    # 检查是否在 Linux 系统上
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo -e "${YELLOW}提示：在 Linux 上运行 Electron 应用需要允许容器访问 X11 服务器${NC}"
        echo -e "${YELLOW}请运行以下命令：${NC}"
        echo -e "${BLUE}xhost +local:docker${NC}"
        read -p "是否已运行上述命令？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${RED}请先运行上述命令，然后重新尝试启动所有服务${NC}"
            exit 1
        fi
    fi
    
    # 启动所有服务
    docker-compose up -d
    echo -e "${GREEN}所有服务已启动！${NC}"
    echo -e "${BLUE}前端访问地址: http://localhost:3000${NC}"
    echo -e "${BLUE}后端API地址: http://localhost:8000${NC}"
    echo -e "${YELLOW}Electron 应用已启动，GUI 界面将显示在您的桌面上${NC}"
}

# 启动开发环境（前台运行，显示日志）
start_dev() {
    echo -e "${GREEN}正在启动会计应用开发环境...${NC}"
    docker-compose up
}

# 停止所有服务
stop_services() {
    echo -e "${YELLOW}正在停止会计应用 Docker 服务...${NC}"
    docker-compose down
    echo -e "${GREEN}服务已停止${NC}"
}

# 重启所有服务
restart_services() {
    echo -e "${YELLOW}正在重启会计应用 Docker 服务...${NC}"
    docker-compose restart
    echo -e "${GREEN}服务已重启${NC}"
}

# 查看服务日志
show_logs() {
    echo -e "${BLUE}显示服务日志...${NC}"
    docker-compose logs -f
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}服务状态：${NC}"
    docker-compose ps
}

# 清理所有容器、网络和卷
clean_all() {
    echo -e "${RED}警告：这将删除所有容器、网络和卷，包括数据！${NC}"
    read -p "确定要继续吗？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}正在清理所有资源...${NC}"
        docker-compose down -v --remove-orphans
        echo -e "${GREEN}清理完成${NC}"
    else
        echo -e "${GREEN}操作已取消${NC}"
    fi
}

# 重新构建并启动所有服务
rebuild_services() {
    echo -e "${YELLOW}正在重新构建并启动会计应用 Docker 服务...${NC}"
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    echo -e "${GREEN}服务已重新构建并启动！${NC}"
    echo -e "${BLUE}前端访问地址: http://localhost:3000${NC}"
    echo -e "${BLUE}后端API地址: http://localhost:8000${NC}"
}

# 主程序
case "$1" in
    up)
        check_docker
        start_services
        ;;
    up-dev)
        check_docker
        start_dev
        ;;
    electron)
        check_docker
        start_electron
        ;;
    all)
        check_docker
        start_all_with_electron
        ;;
    down)
        check_docker
        stop_services
        ;;
    restart)
        check_docker
        restart_services
        ;;
    logs)
        check_docker
        show_logs
        ;;
    status)
        check_docker
        show_status
        ;;
    clean)
        check_docker
        clean_all
        ;;
    rebuild)
        check_docker
        rebuild_services
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        exit 1
esac

exit 0